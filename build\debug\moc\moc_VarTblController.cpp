/****************************************************************************
** Meta object code from reading C++ file 'VarTblController.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../include/mvc/VarTblController.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#include <QtCore/QList>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'VarTblController.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_VarTblController_t {
    QByteArrayData data[51];
    char stringdata0[813];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_VarTblController_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_VarTblController_t qt_meta_stringdata_VarTblController = {
    {
QT_MOC_LITERAL(0, 0, 16), // "VarTblController"
QT_MOC_LITERAL(1, 17, 15), // "editModeChanged"
QT_MOC_LITERAL(2, 33, 0), // ""
QT_MOC_LITERAL(3, 34, 8), // "EditMode"
QT_MOC_LITERAL(4, 43, 4), // "mode"
QT_MOC_LITERAL(5, 48, 2), // "id"
QT_MOC_LITERAL(6, 51, 18), // "operationCompleted"
QT_MOC_LITERAL(7, 70, 7), // "success"
QT_MOC_LITERAL(8, 78, 7), // "message"
QT_MOC_LITERAL(9, 86, 9), // "operation"
QT_MOC_LITERAL(10, 96, 15), // "OperationResult"
QT_MOC_LITERAL(11, 112, 6), // "result"
QT_MOC_LITERAL(12, 119, 19), // "variableDataChanged"
QT_MOC_LITERAL(13, 139, 23), // "connectionStatusChanged"
QT_MOC_LITERAL(14, 163, 9), // "connected"
QT_MOC_LITERAL(15, 173, 17), // "syncStatusChanged"
QT_MOC_LITERAL(16, 191, 7), // "syncing"
QT_MOC_LITERAL(17, 199, 10), // "SyncStatus"
QT_MOC_LITERAL(18, 210, 6), // "status"
QT_MOC_LITERAL(19, 217, 17), // "busyStatusChanged"
QT_MOC_LITERAL(20, 235, 4), // "busy"
QT_MOC_LITERAL(21, 240, 11), // "showMessage"
QT_MOC_LITERAL(22, 252, 7), // "isError"
QT_MOC_LITERAL(23, 260, 22), // "onAddVariableRequested"
QT_MOC_LITERAL(24, 283, 23), // "onEditVariableRequested"
QT_MOC_LITERAL(25, 307, 25), // "onDeleteVariableRequested"
QT_MOC_LITERAL(26, 333, 10), // "QList<int>"
QT_MOC_LITERAL(27, 344, 3), // "ids"
QT_MOC_LITERAL(28, 348, 15), // "onDataSubmitted"
QT_MOC_LITERAL(29, 364, 5), // "label"
QT_MOC_LITERAL(30, 370, 8), // "typeText"
QT_MOC_LITERAL(31, 379, 5), // "value"
QT_MOC_LITERAL(32, 385, 15), // "onEditCancelled"
QT_MOC_LITERAL(33, 401, 22), // "onRefreshDataRequested"
QT_MOC_LITERAL(34, 424, 19), // "onSyncDataRequested"
QT_MOC_LITERAL(35, 444, 26), // "onConnectToServerRequested"
QT_MOC_LITERAL(36, 471, 31), // "onDisconnectFromServerRequested"
QT_MOC_LITERAL(37, 503, 24), // "setWaitForServerResponse"
QT_MOC_LITERAL(38, 528, 4), // "wait"
QT_MOC_LITERAL(39, 533, 24), // "getWaitForServerResponse"
QT_MOC_LITERAL(40, 558, 24), // "setServerResponseTimeout"
QT_MOC_LITERAL(41, 583, 9), // "timeoutMs"
QT_MOC_LITERAL(42, 593, 24), // "getServerResponseTimeout"
QT_MOC_LITERAL(43, 618, 18), // "onModelDataChanged"
QT_MOC_LITERAL(44, 637, 24), // "onConnectionStateChanged"
QT_MOC_LITERAL(45, 662, 45), // "CommandTransceiverRefactored:..."
QT_MOC_LITERAL(46, 708, 5), // "state"
QT_MOC_LITERAL(47, 714, 25), // "onCommandResponseReceived"
QT_MOC_LITERAL(48, 740, 45), // "CommandTransceiverRefactored:..."
QT_MOC_LITERAL(49, 786, 8), // "response"
QT_MOC_LITERAL(50, 795, 17) // "onAutoSyncTimeout"

    },
    "VarTblController\0editModeChanged\0\0"
    "EditMode\0mode\0id\0operationCompleted\0"
    "success\0message\0operation\0OperationResult\0"
    "result\0variableDataChanged\0"
    "connectionStatusChanged\0connected\0"
    "syncStatusChanged\0syncing\0SyncStatus\0"
    "status\0busyStatusChanged\0busy\0showMessage\0"
    "isError\0onAddVariableRequested\0"
    "onEditVariableRequested\0"
    "onDeleteVariableRequested\0QList<int>\0"
    "ids\0onDataSubmitted\0label\0typeText\0"
    "value\0onEditCancelled\0onRefreshDataRequested\0"
    "onSyncDataRequested\0onConnectToServerRequested\0"
    "onDisconnectFromServerRequested\0"
    "setWaitForServerResponse\0wait\0"
    "getWaitForServerResponse\0"
    "setServerResponseTimeout\0timeoutMs\0"
    "getServerResponseTimeout\0onModelDataChanged\0"
    "onConnectionStateChanged\0"
    "CommandTransceiverRefactored::ConnectionState\0"
    "state\0onCommandResponseReceived\0"
    "CommandTransceiverRefactored::CommandResponse\0"
    "response\0onAutoSyncTimeout"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_VarTblController[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      28,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
      11,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,  154,    2, 0x06 /* Public */,
       1,    1,  159,    2, 0x26 /* Public | MethodCloned */,
       6,    2,  162,    2, 0x06 /* Public */,
       6,    4,  167,    2, 0x06 /* Public */,
      12,    2,  176,    2, 0x06 /* Public */,
      13,    1,  181,    2, 0x06 /* Public */,
      15,    1,  184,    2, 0x06 /* Public */,
      15,    1,  187,    2, 0x06 /* Public */,
      19,    1,  190,    2, 0x06 /* Public */,
      21,    2,  193,    2, 0x06 /* Public */,
      21,    1,  198,    2, 0x26 /* Public | MethodCloned */,

 // slots: name, argc, parameters, tag, flags
      23,    0,  201,    2, 0x0a /* Public */,
      24,    1,  202,    2, 0x0a /* Public */,
      25,    1,  205,    2, 0x0a /* Public */,
      28,    4,  208,    2, 0x0a /* Public */,
      32,    0,  217,    2, 0x0a /* Public */,
      33,    0,  218,    2, 0x0a /* Public */,
      34,    0,  219,    2, 0x0a /* Public */,
      35,    0,  220,    2, 0x0a /* Public */,
      36,    0,  221,    2, 0x0a /* Public */,
      37,    1,  222,    2, 0x0a /* Public */,
      39,    0,  225,    2, 0x0a /* Public */,
      40,    1,  226,    2, 0x0a /* Public */,
      42,    0,  229,    2, 0x0a /* Public */,
      43,    2,  230,    2, 0x08 /* Private */,
      44,    1,  235,    2, 0x08 /* Private */,
      47,    1,  238,    2, 0x08 /* Private */,
      50,    0,  241,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3, QMetaType::Int,    4,    5,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,    7,    8,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 10, QMetaType::QString, QMetaType::Int,    9,   11,    8,    5,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,    9,    5,
    QMetaType::Void, QMetaType::Bool,   14,
    QMetaType::Void, QMetaType::Bool,   16,
    QMetaType::Void, 0x80000000 | 17,   18,
    QMetaType::Void, QMetaType::Bool,   20,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,    8,   22,
    QMetaType::Void, QMetaType::QString,    8,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    5,
    QMetaType::Void, 0x80000000 | 26,   27,
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::QString, QMetaType::QVariant,    5,   29,   30,   31,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   38,
    QMetaType::Bool,
    QMetaType::Void, QMetaType::Int,   41,
    QMetaType::Int,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,    9,    5,
    QMetaType::Void, 0x80000000 | 45,   46,
    QMetaType::Void, 0x80000000 | 48,   49,
    QMetaType::Void,

       0        // eod
};

void VarTblController::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<VarTblController *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->editModeChanged((*reinterpret_cast< EditMode(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 1: _t->editModeChanged((*reinterpret_cast< EditMode(*)>(_a[1]))); break;
        case 2: _t->operationCompleted((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 3: _t->operationCompleted((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< OperationResult(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3])),(*reinterpret_cast< int(*)>(_a[4]))); break;
        case 4: _t->variableDataChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 5: _t->connectionStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 6: _t->syncStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 7: _t->syncStatusChanged((*reinterpret_cast< SyncStatus(*)>(_a[1]))); break;
        case 8: _t->busyStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 9: _t->showMessage((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 10: _t->showMessage((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 11: _t->onAddVariableRequested(); break;
        case 12: _t->onEditVariableRequested((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 13: _t->onDeleteVariableRequested((*reinterpret_cast< const QList<int>(*)>(_a[1]))); break;
        case 14: _t->onDataSubmitted((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3])),(*reinterpret_cast< const QVariant(*)>(_a[4]))); break;
        case 15: _t->onEditCancelled(); break;
        case 16: _t->onRefreshDataRequested(); break;
        case 17: _t->onSyncDataRequested(); break;
        case 18: _t->onConnectToServerRequested(); break;
        case 19: _t->onDisconnectFromServerRequested(); break;
        case 20: _t->setWaitForServerResponse((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 21: { bool _r = _t->getWaitForServerResponse();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 22: _t->setServerResponseTimeout((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 23: { int _r = _t->getServerResponseTimeout();
            if (_a[0]) *reinterpret_cast< int*>(_a[0]) = std::move(_r); }  break;
        case 24: _t->onModelDataChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 25: _t->onConnectionStateChanged((*reinterpret_cast< CommandTransceiverRefactored::ConnectionState(*)>(_a[1]))); break;
        case 26: _t->onCommandResponseReceived((*reinterpret_cast< const CommandTransceiverRefactored::CommandResponse(*)>(_a[1]))); break;
        case 27: _t->onAutoSyncTimeout(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 13:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QList<int> >(); break;
            }
            break;
        case 26:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< CommandTransceiverRefactored::CommandResponse >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (VarTblController::*)(EditMode , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblController::editModeChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (VarTblController::*)(bool , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblController::operationCompleted)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (VarTblController::*)(const QString & , OperationResult , const QString & , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblController::operationCompleted)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (VarTblController::*)(const QString & , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblController::variableDataChanged)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (VarTblController::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblController::connectionStatusChanged)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (VarTblController::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblController::syncStatusChanged)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (VarTblController::*)(SyncStatus );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblController::syncStatusChanged)) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (VarTblController::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblController::busyStatusChanged)) {
                *result = 8;
                return;
            }
        }
        {
            using _t = void (VarTblController::*)(const QString & , bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblController::showMessage)) {
                *result = 9;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject VarTblController::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_VarTblController.data,
    qt_meta_data_VarTblController,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *VarTblController::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *VarTblController::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_VarTblController.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int VarTblController::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 28)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 28;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 28)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 28;
    }
    return _id;
}

// SIGNAL 0
void VarTblController::editModeChanged(EditMode _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 2
void VarTblController::operationCompleted(bool _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void VarTblController::operationCompleted(const QString & _t1, OperationResult _t2, const QString & _t3, int _t4)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t4))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void VarTblController::variableDataChanged(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void VarTblController::connectionStatusChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void VarTblController::syncStatusChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void VarTblController::syncStatusChanged(SyncStatus _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}

// SIGNAL 8
void VarTblController::busyStatusChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 8, _a);
}

// SIGNAL 9
void VarTblController::showMessage(const QString & _t1, bool _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 9, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
