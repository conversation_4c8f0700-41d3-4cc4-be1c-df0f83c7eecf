#include "RWSConfig.h"

// 简化版实现，只提供基本的工具函数

QString RWSStrategyUtils::strategyTypeToString(IRWSStrategy::StrategyType type)
{
    switch (type) {
    case IRWSStrategy::PreLength:
        return "PreLength";
    case IRWSStrategy::EndWithNewLine:
        return "EndWithNewLine";
    case IRWSStrategy::Simple:
        return "Simple";
    default:
        return "Unknown";
    }
}

IRWSStrategy::StrategyType RWSStrategyUtils::stringToStrategyType(const QString &str)
{
    if (str == "PreLength") {
        return IRWSStrategy::PreLength;
    } else if (str == "EndWithNewLine") {
        return IRWSStrategy::EndWithNewLine;
    } else if (str == "Simple") {
        return IRWSStrategy::Simple;
    } else {
        return IRWSStrategy::PreLength; // 默认值
    }
}


