#!/bin/bash

# CommandTransceiver 测试服务器编译脚本
# 适用于 Linux/macOS 环境

echo "========================================"
echo "  CommandTransceiver 测试服务器编译"
echo "========================================"

# 检查是否存在 qmake
if ! command -v qmake &> /dev/null; then
    echo "错误: 未找到 qmake，请确保 Qt 已正确安装"
    echo "Ubuntu/Debian: sudo apt-get install qt5-qmake qt5-default"
    echo "CentOS/RHEL: sudo yum install qt5-qtbase-devel"
    echo "macOS: brew install qt5"
    exit 1
fi

echo "找到 qmake:"
qmake -version

echo ""
echo "检查 Qt 版本兼容性..."
echo "需要 Qt 5.12 或更高版本"

# 创建构建目录
mkdir -p build
mkdir -p bin/debug
mkdir -p bin/release

echo ""
echo "正在生成 Makefile..."
qmake test_server.pro
if [ $? -ne 0 ]; then
    echo "错误: qmake 执行失败"
    exit 1
fi

echo ""
echo "正在编译项目..."
make
if [ $? -ne 0 ]; then
    echo ""
    echo "编译失败，请检查错误信息"
    exit 1
fi

echo ""
echo "========================================"
echo "  编译成功！"
echo "========================================"

# 查找生成的可执行文件
if [ -f "bin/debug/CommandTransceiverTestServer_d" ]; then
    echo "Debug 版本: bin/debug/CommandTransceiverTestServer_d"
fi
if [ -f "bin/release/CommandTransceiverTestServer" ]; then
    echo "Release 版本: bin/release/CommandTransceiverTestServer"
fi
if [ -f "debug/CommandTransceiverTestServer_d" ]; then
    echo "Debug 版本: debug/CommandTransceiverTestServer_d"
fi
if [ -f "release/CommandTransceiverTestServer" ]; then
    echo "Release 版本: release/CommandTransceiverTestServer"
fi

echo ""
echo "使用方法:"
echo "  基本启动: ./CommandTransceiverTestServer"
echo "  指定端口: ./CommandTransceiverTestServer -p 9090"
echo "  查看帮助: ./CommandTransceiverTestServer --help"
echo ""

# 设置可执行权限
if [ -f "bin/debug/CommandTransceiverTestServer_d" ]; then
    chmod +x bin/debug/CommandTransceiverTestServer_d
fi
if [ -f "bin/release/CommandTransceiverTestServer" ]; then
    chmod +x bin/release/CommandTransceiverTestServer
fi
if [ -f "debug/CommandTransceiverTestServer_d" ]; then
    chmod +x debug/CommandTransceiverTestServer_d
fi
if [ -f "release/CommandTransceiverTestServer" ]; then
    chmod +x release/CommandTransceiverTestServer
fi

echo "编译完成！"
