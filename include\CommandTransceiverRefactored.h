#ifndef COMMAND_TRANSCEIVER_REFACTORED_H
#define COMMAND_TRANSCEIVER_REFACTORED_H

#include <QObject>
#include <QMutex>
#include <QMutexLocker>
#include <QWaitCondition>
#include <QTimer>
#include <QJsonObject>
#include <QJsonDocument>
#include <QUuid>
#include <QSharedPointer>
#include <QHash>
#include <QHostAddress>
#include <QDateTime>
#include <QThread>
#include <QAtomicInt>
#include <functional>

// 包含Worker类
#include "NetworkWorker.h"
#include "DataProcessorWorker.h"

#ifndef COMMANDTRANSCEIVER_NO_ADA3
namespace cmd {
    class CmdStatusReply;
    class NodeStatusReply;
    class FlowStatusReply;
    class Reply;
}
#endif

/**
 * @brief CommandTransceiver Framework 主类（重构版本）
 * 
 * 重构后的CommandTransceiver，使用新的Worker类架构：
 * - NetworkWorker：处理网络通信（使用moveToThread）
 * - DataProcessorWorker：处理数据编解码（使用moveToThread）
 * - 改进的线程管理和资源清理
 * - 更好的错误处理和异常安全
 * - 修复了原版本中的竞态条件和内存泄漏问题
 */
class CommandTransceiverRefactored : public QObject
{
    Q_OBJECT

public:
    // 使用NetworkWorker中的ConnectionState枚举
    using ConnectionState = NetworkWorker::ConnectionState;

    enum class CommandResult {
        Success,                // 成功
        Timeout,               // 超时
        NetworkError,          // 网络错误
        InvalidCommand,        // 无效命令
        ServerError,           // 服务器错误
        NotConnected,          // 未连接
        ProcessingError        // 数据处理错误
    };

    /**
     * @brief 命令响应结构
     */
    struct CommandResponse {
        CommandResult result;
        QJsonObject data;
        QString errorMessage;
        QUuid commandUuid;
        qint64 createTime;    // 命令创建时间（毫秒时间戳）
        qint64 responseTime;  // 响应处理时间（毫秒）
        qint64 networkTime;   // 网络传输时间（毫秒）

        CommandResponse()
            : result(CommandResult::Success)
            , createTime(QDateTime::currentMSecsSinceEpoch())
            , responseTime(0)
            , networkTime(0)
        {}

        qint64 getTotalTime() const { return responseTime + networkTime; }
        qint64 getAge() const { return QDateTime::currentMSecsSinceEpoch() - createTime; }
        bool isTimeout(qint64 timeoutMs) const { return getAge() > timeoutMs; }
    };

    /**
     * @brief 连接配置
     */
    struct ConnectionConfig {
        QHostAddress serverAddress;
        quint16 serverPort;
        int connectTimeoutMs;      // 连接超时
        int commandTimeoutMs;      // 命令超时
        bool autoReconnect;        // 自动重连
        int reconnectIntervalMs;   // 重连间隔
        bool enableHeartbeat;      // 启用心跳
        int heartbeatIntervalMs;   // 心跳间隔
        
        ConnectionConfig()
            : serverAddress(QHostAddress::LocalHost)
            , serverPort(8080)
            , connectTimeoutMs(5000)
            , commandTimeoutMs(30000)
            , autoReconnect(true)
            , reconnectIntervalMs(3000)
            , enableHeartbeat(false)
            , heartbeatIntervalMs(30000) {}
    };

    explicit CommandTransceiverRefactored(QObject *parent = nullptr);
    ~CommandTransceiverRefactored();

    // ========== 连接管理 ==========
    
    /**
     * @brief 连接到服务器
     * @param config 连接配置
     * @return 是否成功启动连接
     */
    bool connectToServer(const ConnectionConfig &config);

    /**
     * @brief 连接到服务器（简化版本）
     * @param host 服务器地址
     * @param port 服务器端口
     * @return 是否成功启动连接
     */
    bool connectToServer(const QString &host, quint16 port);

    /**
     * @brief 断开连接
     */
    void disconnectFromServer();

    /**
     * @brief 获取连接状态
     * @return 当前连接状态
     */
    ConnectionState getConnectionState() const;

    /**
     * @brief 是否已连接
     * @return true如果已连接
     */
    bool isConnected() const;

    /**
     * @brief 检查组件是否已初始化完成
     * @return true表示所有组件已初始化完成
     */
    bool isInitialized() const;

    // ========== 异步命令发送 ==========

    /**
     * @brief 异步发送命令
     * @param command 命令JSON对象
     * @param uuid 命令UUID（空则自动生成）
     * @return 命令UUID
     */
    QUuid sendCommand(const QJsonObject &command, const QUuid &uuid = QUuid());

    /**
     * @brief 异步发送命令（带回调）
     * @param command 命令JSON对象
     * @param callback 响应回调函数
     * @param uuid 命令UUID（空则自动生成）
     * @return 命令UUID
     */
    QUuid sendCommand(const QJsonObject &command, 
                     std::function<void(const CommandResponse&)> callback,
                     const QUuid &uuid = QUuid());

    // ========== 同步命令发送 ==========

    /**
     * @brief 同步发送命令
     * @param command 命令JSON对象
     * @param timeoutMs 超时时间（毫秒，-1使用默认超时）
     * @param uuid 命令UUID（空则自动生成）
     * @return 命令响应
     */
    CommandResponse sendCommandSync(const QJsonObject &command, 
                                  int timeoutMs = -1,
                                  const QUuid &uuid = QUuid());



    // ========== 配置和状态 ==========

    /**
     * @brief 设置连接配置
     * @param config 连接配置
     */
    void setConnectionConfig(const ConnectionConfig &config);

    /**
     * @brief 获取连接配置
     * @return 当前连接配置
     */
    ConnectionConfig getConnectionConfig() const;

    /**
     * @brief 获取统计信息
     * @return 统计信息JSON对象
     */
    QJsonObject getStatistics() const;

signals:
    /**
     * @brief 组件初始化完成信号
     * 当所有内部Worker线程初始化完成后发出此信号
     */
    void initialized();

    /**
     * @brief 连接状态变化信号
     * @param state 新的连接状态
     */
    void connectionStateChanged(ConnectionState state);

    /**
     * @brief 命令响应信号
     * @param response 命令响应
     */
    void commandResponseReceived(const CommandResponse &response);

    /**
     * @brief 命令接收信号
     * @param command 命令对象
     * @param uuid 命令UUID
     */
    void commandReceived(const QJsonObject &command, const QUuid &uuid);

    /**
     * @brief 响应接收信号
     * @param reply 响应对象
     * @param uuid 响应UUID
     */
    void replyReceived(const QJsonObject &reply, const QUuid &uuid);

    /**
     * @brief 错误信号
     * @param error 错误描述
     */
    void errorOccurred(const QString &error);

private slots:
    // Worker初始化和清理
    void onNetworkWorkerInitialized();
    void onDataProcessorWorkerInitialized();
    void onNetworkWorkerCleanupCompleted();
    void onDataProcessorWorkerCleanupCompleted();
    
    // 网络相关槽函数
    void onNetworkConnectionStateChanged(NetworkWorker::ConnectionState state);
    void onNetworkRawDataReceived(const QByteArray &data);
    void onNetworkError(NetworkWorker::NetworkError error, const QString &description);
    void onNetworkDataSent(qint64 bytesWritten);
    void onNetworkDataSendFailed(const QString &error);
    
    // 数据处理相关槽函数
    void onDataProcessorCommandReceived(const QJsonObject &command, const QUuid &uuid);
    void onDataProcessorReplyReceived(const QJsonObject &reply, const QUuid &uuid);
    void onDataProcessorJsonEncoded(const QByteArray &encodedData, const QJsonObject &originalJson);
    void onDataProcessorError(DataProcessorWorker::ProcessError error, const QString &errorString);
    
    // 同步命令超时
    void onSyncCommandTimeout();

private:
    // Worker线程和对象
    QThread *m_networkThread;
    QThread *m_dataProcessorThread;
    NetworkWorker *m_networkWorker;
    DataProcessorWorker *m_dataProcessorWorker;
    
    // 初始化状态
    QAtomicInt m_networkWorkerInitialized;
    QAtomicInt m_dataProcessorWorkerInitialized;
    QAtomicInt m_cleanupInProgress;
    
    // 连接管理
    mutable QMutex m_connectionMutex;
    ConnectionState m_connectionState;
    ConnectionConfig m_connectionConfig;
    bool m_pendingConnection; // 是否有待处理的连接请求
    
    // 同步命令支持
    mutable QMutex m_syncCommandMutex;
    QHash<QUuid, CommandResponse*> m_pendingSyncCommands;
    QHash<QUuid, QWaitCondition*> m_syncCommandConditions;
    QTimer *m_syncTimeoutTimer;
    
    // 异步命令回调
    mutable QMutex m_callbackMutex;
    QHash<QUuid, std::function<void(const CommandResponse&)>> m_commandCallbacks;
    
    // 统计信息
    mutable QMutex m_statsMutex;
    qint64 m_commandsSent;
    qint64 m_responsesReceived;
    qint64 m_errorsCount;
    qint64 m_startTime;

    /**
     * @brief 初始化组件
     */
    void initializeComponents();

    /**
     * @brief 连接信号槽
     */
    void connectSignals();

    /**
     * @brief 清理资源
     */
    void cleanup();

    /**
     * @brief 生成命令UUID
     * @return 新的UUID
     */
    QUuid generateCommandUuid();

    /**
     * @brief 处理命令响应
     * @param uuid 命令UUID
     * @param response 响应数据
     */
    void handleCommandResponse(const QUuid &uuid, const CommandResponse &response);

    /**
     * @brief 清理超时的同步命令
     */
    void cleanupTimeoutSyncCommands();

    /**
     * @brief 检查并发出初始化完成信号
     */
    void checkAndEmitInitialized();

    /**
     * @brief 执行实际的连接操作
     * @param config 连接配置
     * @return 是否成功启动连接
     */
    bool performConnection(const ConnectionConfig &config);
};

// 注册元类型以支持信号槽
Q_DECLARE_METATYPE(CommandTransceiverRefactored::CommandResponse)

#endif // COMMAND_TRANSCEIVER_REFACTORED_H
