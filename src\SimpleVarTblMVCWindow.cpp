/**
 * @file SimpleVarTblMVCWindow.cpp
 * @brief 简单的VarTbl MVC验证窗口类实现
 *
 * <AUTHOR> Framework
 * @date 2025-01-23
 */

#include "SimpleVarTblMVCWindow.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QDebug>
#include <QMessageBox>

// VarTbl MVC架构组件
#ifdef COMMANDTRANSCEIVER_MVC_SUPPORT
#include "mvc/VarTblManager.h"
#endif

// ada3数据结构（如果支持）
#ifdef COMMANDTRANSCEIVER_VARTBL_SUPPORT
#include "cmd/vartbl.h"
#include "cmd/var.h"
#endif

SimpleVarTblMVCWindow::SimpleVarTblMVCWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_addDataButton(nullptr)
#ifdef COMMANDTRANSCEIVER_MVC_SUPPORT
    , m_varTblManager(nullptr)
#endif
{
    setupUI();
    setupVarTblMVC();

    setWindowTitle(tr("VarTbl MVC架构验证"));
    resize(800, 600);

    qDebug() << "VarTbl MVC验证窗口初始化完成";
}

SimpleVarTblMVCWindow::~SimpleVarTblMVCWindow()
{
    qDebug() << "VarTbl MVC验证窗口销毁";
}

void SimpleVarTblMVCWindow::onAddDemoDataClicked()
{
#ifdef COMMANDTRANSCEIVER_VARTBL_SUPPORT
    if (m_varTblManager) {
        // 添加简单的示例数据
        m_varTblManager->addVariable(1, cmd::Var("Hello World"), "问候语", false);
        m_varTblManager->addVariable(2, cmd::Var(QStringList{"苹果", "香蕉", "橙子"}), "水果", false);
        m_varTblManager->addVariable(3, cmd::Var("测试数据"), "测试", false);

        qDebug() << "添加示例数据完成";
    }
#else
    QMessageBox::information(this, tr("提示"), tr("需要ada3支持才能使用VarTbl功能"));
#endif
}

void SimpleVarTblMVCWindow::setupUI()
{
    // 创建中央窗口部件
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    QVBoxLayout *mainLayout = new QVBoxLayout(centralWidget);

    // 添加一个简单的按钮来测试功能
    m_addDataButton = new QPushButton(tr("添加示例数据"), this);
    connect(m_addDataButton, &QPushButton::clicked, this, &SimpleVarTblMVCWindow::onAddDemoDataClicked);
    mainLayout->addWidget(m_addDataButton);

#ifndef COMMANDTRANSCEIVER_MVC_SUPPORT
    QLabel *noMVCLabel = new QLabel(tr("VarTbl MVC功能需要ada3支持\n请确保ada3模块可用"), this);
    noMVCLabel->setAlignment(Qt::AlignCenter);
    noMVCLabel->setStyleSheet("color: gray; font-size: 14px;");
    mainLayout->addWidget(noMVCLabel);
#endif

    qDebug() << "UI设置完成";
}

void SimpleVarTblMVCWindow::setupVarTblMVC()
{
#ifdef COMMANDTRANSCEIVER_MVC_SUPPORT
    try {
        // 创建VarTbl MVC管理器
        m_varTblManager = new VarTblManager(this);
        
        // 获取View并添加到布局中
        if (m_varTblManager->getView()) {
            QWidget *centralWidget = this->centralWidget();
            if (centralWidget && centralWidget->layout()) {
                centralWidget->layout()->addWidget(m_varTblManager->getView());
            }
        }

        qDebug() << "VarTbl MVC架构设置完成";
    } catch (const std::exception &e) {
        qDebug() << "VarTbl MVC设置失败:" << e.what();
        QMessageBox::warning(this, tr("警告"), tr("VarTbl MVC初始化失败: %1").arg(e.what()));
    }
#else
    qDebug() << "VarTbl MVC支持未启用";
#endif
}
