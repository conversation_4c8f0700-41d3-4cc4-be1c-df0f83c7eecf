/****************************************************************************
** Meta object code from reading C++ file 'VarTblManager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../include/mvc/VarTblManager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'VarTblManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_VarTblManager_t {
    QByteArrayData data[19];
    char stringdata0[333];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_VarTblManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_VarTblManager_t qt_meta_stringdata_VarTblManager = {
    {
QT_MOC_LITERAL(0, 0, 13), // "VarTblManager"
QT_MOC_LITERAL(1, 14, 18), // "operationCompleted"
QT_MOC_LITERAL(2, 33, 0), // ""
QT_MOC_LITERAL(3, 34, 9), // "operation"
QT_MOC_LITERAL(4, 44, 7), // "success"
QT_MOC_LITERAL(5, 52, 7), // "message"
QT_MOC_LITERAL(6, 60, 2), // "id"
QT_MOC_LITERAL(7, 63, 23), // "connectionStatusChanged"
QT_MOC_LITERAL(8, 87, 9), // "connected"
QT_MOC_LITERAL(9, 97, 19), // "variableDataChanged"
QT_MOC_LITERAL(10, 117, 17), // "syncStatusChanged"
QT_MOC_LITERAL(11, 135, 28), // "VarTblController::SyncStatus"
QT_MOC_LITERAL(12, 164, 6), // "status"
QT_MOC_LITERAL(13, 171, 30), // "onControllerOperationCompleted"
QT_MOC_LITERAL(14, 202, 33), // "VarTblController::OperationRe..."
QT_MOC_LITERAL(15, 236, 6), // "result"
QT_MOC_LITERAL(16, 243, 35), // "onControllerConnectionStatusC..."
QT_MOC_LITERAL(17, 279, 23), // "onControllerDataChanged"
QT_MOC_LITERAL(18, 303, 29) // "onControllerSyncStatusChanged"

    },
    "VarTblManager\0operationCompleted\0\0"
    "operation\0success\0message\0id\0"
    "connectionStatusChanged\0connected\0"
    "variableDataChanged\0syncStatusChanged\0"
    "VarTblController::SyncStatus\0status\0"
    "onControllerOperationCompleted\0"
    "VarTblController::OperationResult\0"
    "result\0onControllerConnectionStatusChanged\0"
    "onControllerDataChanged\0"
    "onControllerSyncStatusChanged"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_VarTblManager[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       9,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    4,   59,    2, 0x06 /* Public */,
       1,    3,   68,    2, 0x26 /* Public | MethodCloned */,
       7,    1,   75,    2, 0x06 /* Public */,
       9,    2,   78,    2, 0x06 /* Public */,
      10,    1,   83,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      13,    4,   86,    2, 0x08 /* Private */,
      16,    1,   95,    2, 0x08 /* Private */,
      17,    2,   98,    2, 0x08 /* Private */,
      18,    1,  103,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::Bool, QMetaType::QString, QMetaType::Int,    3,    4,    5,    6,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool, QMetaType::QString,    3,    4,    5,
    QMetaType::Void, QMetaType::Bool,    8,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,    3,    6,
    QMetaType::Void, 0x80000000 | 11,   12,

 // slots: parameters
    QMetaType::Void, QMetaType::QString, 0x80000000 | 14, QMetaType::QString, QMetaType::Int,    3,   15,    5,    6,
    QMetaType::Void, QMetaType::Bool,    8,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,    3,    6,
    QMetaType::Void, 0x80000000 | 11,   12,

       0        // eod
};

void VarTblManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<VarTblManager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->operationCompleted((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3])),(*reinterpret_cast< int(*)>(_a[4]))); break;
        case 1: _t->operationCompleted((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 2: _t->connectionStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 3: _t->variableDataChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 4: _t->syncStatusChanged((*reinterpret_cast< VarTblController::SyncStatus(*)>(_a[1]))); break;
        case 5: _t->onControllerOperationCompleted((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< VarTblController::OperationResult(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3])),(*reinterpret_cast< int(*)>(_a[4]))); break;
        case 6: _t->onControllerConnectionStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 7: _t->onControllerDataChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 8: _t->onControllerSyncStatusChanged((*reinterpret_cast< VarTblController::SyncStatus(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (VarTblManager::*)(const QString & , bool , const QString & , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblManager::operationCompleted)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (VarTblManager::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblManager::connectionStatusChanged)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (VarTblManager::*)(const QString & , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblManager::variableDataChanged)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (VarTblManager::*)(VarTblController::SyncStatus );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblManager::syncStatusChanged)) {
                *result = 4;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject VarTblManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_VarTblManager.data,
    qt_meta_data_VarTblManager,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *VarTblManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *VarTblManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_VarTblManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int VarTblManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 9;
    }
    return _id;
}

// SIGNAL 0
void VarTblManager::operationCompleted(const QString & _t1, bool _t2, const QString & _t3, int _t4)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t4))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 2
void VarTblManager::connectionStatusChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void VarTblManager::variableDataChanged(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void VarTblManager::syncStatusChanged(VarTblController::SyncStatus _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
