#ifndef NETWORK_WORKER_H
#define NETWORK_WORKER_H

#include <QObject>
#include <QTcpSocket>
#include <QTimer>
#include <QMutex>
#include <QAtomicInt>
#include <QHostAddress>
#include <memory>
#include "IRWSStrategy.h"
#include "RWSStrategyFactory.h"
#include "RWSConfig.h"

/**
 * @brief 网络通信工作器类
 *
 * 使用Qt推荐的moveToThread方式在独立线程中处理网络通信
 * 修复了原NetworkThread中的线程安全问题和资源管理问题
 *
 * 主要改进：
 * - 不继承QThread，使用QObject + moveToThread模式
 * - 使用QAtomicInt确保线程安全的状态管理
 * - 改进的资源清理机制
 * - 更好的错误处理和异常安全
 * - 集成RWSStrategyFactory，支持可配置的读写策略
 * - 支持运行时策略切换，提供更好的协议适应性
 */
class NetworkWorker : public QObject
{
    Q_OBJECT

public:
    enum class ConnectionState {
        Disconnected,   // 未连接
        Connecting,     // 连接中
        Connected,      // 已连接
        Reconnecting,   // 重连中
        Error          // 错误状态
    };

    enum class NetworkError {
        NoError,
        ConnectionTimeout,
        ConnectionRefused,
        HostNotFound,
        SocketError,
        ProtocolError,
        WriteError,
        ReadError
    };

    explicit NetworkWorker(QObject *parent = nullptr);
    ~NetworkWorker();

    /**
     * @brief 获取当前连接状态
     * @return 连接状态
     */
    ConnectionState connectionState() const;

    /**
     * @brief 是否已连接
     * @return true如果已连接
     */
    bool isConnected() const;

public slots:
    /**
     * @brief 初始化网络组件（在工作线程中调用）
     */
    void initialize();

    /**
     * @brief 清理网络组件（在工作线程中调用）
     */
    void cleanup();

    /**
     * @brief 连接到服务器
     * @param host 服务器地址
     * @param port 服务器端口
     * @param timeoutMs 连接超时时间（毫秒）
     */
    void connectToServer(const QString &host, quint16 port, int timeoutMs = 5000);

    /**
     * @brief 断开连接
     */
    void disconnectFromServer();

    /**
     * @brief 发送数据
     * @param data 要发送的数据
     */
    void sendData(const QByteArray &data);

    /**
     * @brief 启用/禁用自动重连
     * @param enabled 是否启用
     * @param intervalMs 重连间隔（毫秒）
     */
    void setAutoReconnect(bool enabled, int intervalMs = 3000);

    /**
     * @brief 设置心跳检测
     * @param enabled 是否启用
     * @param intervalMs 心跳间隔（毫秒）
     */
    void setHeartbeat(bool enabled, int intervalMs = 30000);

    /**
     * @brief 停止工作器（准备线程退出）
     */
    void stop();

    /**
     * @brief 设置读写策略类型（线程安全）
     * @param type 策略类型
     */
    void setRWSStrategyType(IRWSStrategy::StrategyType type);

    /**
     * @brief 设置读写策略配置（线程安全）
     * @param config 策略配置
     */
    void setRWSStrategyConfig(const RWSConfig &config);

    /**
     * @brief 获取当前策略类型
     * @return 当前使用的策略类型
     */
    IRWSStrategy::StrategyType getCurrentStrategyType() const;

signals:
    /**
     * @brief 初始化完成信号
     */
    void initialized();

    /**
     * @brief 清理完成信号
     */
    void cleanupCompleted();

    /**
     * @brief 连接状态变化信号
     * @param state 新的连接状态
     */
    void connectionStateChanged(NetworkWorker::ConnectionState state);

    /**
     * @brief 接收到原始数据信号
     * @param data 接收到的数据
     */
    void rawDataReceived(const QByteArray &data);

    /**
     * @brief 网络错误信号
     * @param error 错误类型
     * @param description 错误描述
     */
    void networkError(NetworkError error, const QString &description);

    /**
     * @brief 数据发送完成信号
     * @param bytesWritten 发送的字节数
     */
    void dataSent(qint64 bytesWritten);

    /**
     * @brief 数据发送失败信号
     * @param error 错误描述
     */
    void dataSendFailed(const QString &error);

    /**
     * @brief 读写策略切换完成信号
     * @param type 新的策略类型
     * @param strategyName 策略名称
     */
    void rwsStrategyChanged(IRWSStrategy::StrategyType type, const QString &strategyName);

private slots:
    void onSocketConnected();
    void onSocketDisconnected();
    void onSocketError(QAbstractSocket::SocketError error);
    void onSocketReadyRead();
    void onSocketBytesWritten(qint64 bytes);
    void onReconnectTimer();
    void onHeartbeatTimer();
    void onConnectionTimeout();

private:
    // 网络相关
    QTcpSocket *m_socket;
    std::unique_ptr<IRWSStrategy> m_rwsStrategy;  // 读写策略实例

    // 策略配置
    RWSConfig m_rwsConfig;  // 当前策略配置
    
    // 连接参数
    QString m_host;
    quint16 m_port;
    int m_connectionTimeout;
    
    // 状态管理 - 使用原子操作确保线程安全
    mutable QMutex m_stateMutex;
    ConnectionState m_connectionState;
    QAtomicInt m_shouldStop;  // 使用原子操作
    
    // 重连机制
    QTimer *m_reconnectTimer;
    QAtomicInt m_autoReconnectEnabled;  // 使用原子操作
    int m_reconnectInterval;
    
    // 心跳机制
    QTimer *m_heartbeatTimer;
    QAtomicInt m_heartbeatEnabled;  // 使用原子操作
    int m_heartbeatInterval;
    
    // 连接超时定时器
    QTimer *m_connectionTimeoutTimer;
    
    /**
     * @brief 设置连接状态（线程安全）
     * @param state 新状态
     */
    void setConnectionState(NetworkWorker::ConnectionState state);
    
    /**
     * @brief 启动重连定时器
     */
    void startReconnectTimer();
    
    /**
     * @brief 停止重连定时器
     */
    void stopReconnectTimer();
    
    /**
     * @brief 发送心跳包
     */
    void sendHeartbeat();
    
    /**
     * @brief 将Socket错误转换为NetworkError
     * @param socketError Socket错误
     * @return NetworkError
     */
    NetworkError convertSocketError(QAbstractSocket::SocketError socketError);
    
    /**
     * @brief 安全地清理定时器
     * @param timer 要清理的定时器指针引用
     */
    void safeCleanupTimer(QTimer *&timer);

    /**
     * @brief 创建读写策略实例（内部方法）
     * @param config 策略配置
     * @return 策略实例，创建失败返回nullptr
     */
    std::unique_ptr<IRWSStrategy> createRWSStrategy(const RWSConfig &config);

    /**
     * @brief 切换读写策略的内部实现（在工作线程中调用）
     * @param config 新的策略配置
     */
    void switchRWSStrategyInternal(const RWSConfig &config);
};

// 注册元类型以支持信号槽
Q_DECLARE_METATYPE(NetworkWorker::ConnectionState)
Q_DECLARE_METATYPE(NetworkWorker::NetworkError)

#endif // NETWORK_WORKER_H
