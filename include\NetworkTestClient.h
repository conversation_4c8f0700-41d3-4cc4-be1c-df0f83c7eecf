#ifndef NETWORKTESTCLIENT_H
#define NETWORKTESTCLIENT_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QTextEdit>
#include <QGroupBox>
#include <QSpinBox>
#include <QStatusBar>
#include <QTimer>
#include <QThread>
#include <QMutex>
#include <QDateTime>
#include <QComboBox>
#include <QSplitter>
#include <QJsonObject>
#include <QJsonDocument>
#include <QUuid>

#include "CommandTransceiverRefactored.h"

/**
 * @brief 网络通信测试客户端主窗口
 *
 * 提供简单的GUI界面用于测试CommandTransceiverRefactored类的完整功能
 * 主要功能：
 * - 连接到指定服务器地址和端口
 * - 发送JSON格式的命令到服务器
 * - 接收并显示来自服务器的响应
 * - 显示连接状态和统计信息
 * - 测试异步命令发送、连接管理、状态监控等核心功能
 */
class NetworkTestClient : public QMainWindow
{
    Q_OBJECT

public:
    explicit NetworkTestClient(QWidget *parent = nullptr);
    ~NetworkTestClient();

private slots:
    /**
     * @brief 连接/断开连接按钮点击处理
     */
    void onConnectButtonClicked();

    /**
     * @brief 发送命令按钮点击处理
     */
    void onSendButtonClicked();

    /**
     * @brief 清空接收区域
     */
    void onClearButtonClicked();

    /**
     * @brief 命令模板选择变化
     */
    void onCommandTemplateChanged();

    /**
     * @brief CommandTransceiver初始化完成处理
     */
    void onCommandTransceiverInitialized();

    /**
     * @brief 连接状态变化处理
     */
    void onConnectionStateChanged(NetworkWorker::ConnectionState state);

    /**
     * @brief 命令响应接收处理
     */
    void onCommandResponseReceived(const CommandTransceiverRefactored::CommandResponse &response);

    /**
     * @brief 命令接收处理
     */
    void onCommandReceived(const QJsonObject &command, const QUuid &uuid);

    /**
     * @brief 响应接收处理
     */
    void onReplyReceived(const QJsonObject &reply, const QUuid &uuid);

    /**
     * @brief 错误处理
     */
    void onErrorOccurred(const QString &error);

    /**
     * @brief 更新连接状态显示
     */
    void updateConnectionStatus();

    /**
     * @brief 回车键发送命令
     */
    void onCommandInputReturnPressed();

private:
    /**
     * @brief 初始化UI界面
     */
    void setupUI();

    /**
     * @brief 初始化CommandTransceiver组件
     */
    void setupCommandTransceiverComponents();

    /**
     * @brief 连接信号槽
     */
    void connectSignals();

    /**
     * @brief 添加日志消息
     */
    void addLogMessage(const QString &message, const QString &type = "INFO");

    /**
     * @brief 更新统计信息
     */
    void updateStatistics();

    /**
     * @brief 设置UI控件启用状态
     */
    void setUIEnabled(bool connected);

    /**
     * @brief 格式化JSON显示
     */
    QString formatJsonForDisplay(const QJsonObject &json);

    /**
     * @brief 解析JSON命令
     */
    QJsonObject parseJsonCommand(const QString &text);

    /**
     * @brief 获取命令模板
     */
    QString getCommandTemplate(const QString &templateName);

    /**
     * @brief 初始化命令模板
     */
    void setupCommandTemplates();

private:
    // UI组件
    QWidget *m_centralWidget;
    QVBoxLayout *m_mainLayout;
    QSplitter *m_mainSplitter;

    // 连接配置组
    QGroupBox *m_connectionGroup;
    QGridLayout *m_connectionLayout;
    QLabel *m_hostLabel;
    QLineEdit *m_hostEdit;
    QLabel *m_portLabel;
    QSpinBox *m_portSpinBox;
    QPushButton *m_connectButton;
    QLabel *m_statusLabel;

    // 命令发送组
    QGroupBox *m_sendGroup;
    QVBoxLayout *m_sendLayout;
    QHBoxLayout *m_templateLayout;
    QLabel *m_templateLabel;
    QComboBox *m_templateComboBox;
    QTextEdit *m_commandEdit;
    QPushButton *m_sendButton;

    // 响应接收组
    QGroupBox *m_receiveGroup;
    QVBoxLayout *m_receiveLayout;
    QTextEdit *m_receiveTextEdit;
    QPushButton *m_clearButton;

    // 统计信息组
    QGroupBox *m_statsGroup;
    QGridLayout *m_statsLayout;
    QLabel *m_sentCountLabel;
    QLabel *m_receivedCountLabel;
    QLabel *m_sentBytesLabel;
    QLabel *m_receivedBytesLabel;

    // CommandTransceiver组件
    CommandTransceiverRefactored *m_commandTransceiver;

    // 状态变量
    NetworkWorker::ConnectionState m_connectionState;
    QMutex m_statsMutex;
    int m_commandsSent;
    int m_responsesReceived;
    int m_commandsReceived;
    int m_repliesReceived;
    qint64 m_bytesSent;
    qint64 m_bytesReceived;

    // 定时器
    QTimer *m_statusUpdateTimer;

    // 命令模板
    QHash<QString, QString> m_commandTemplates;
};

#endif // NETWORKTESTCLIENT_H
