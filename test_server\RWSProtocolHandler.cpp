#include "RWSProtocolHandler.h"
#include <QDataStream>
#include <QDebug>
#include <QByteArray>

const QByteArray RWSProtocolHandler::NEWLINE_DELIMITER = "\n";

RWSProtocolHandler::RWSProtocolHandler(ProtocolType type, QObject *parent)
    : QObject(parent)
    , m_protocolType(type)
    , m_compressedPacketsReceived(0)
    , m_totalCompressedBytes(0)
    , m_totalUncompressedBytes(0)
{
    qDebug() << "RWSProtocolHandler: 初始化完成，协议类型:" << getProtocolName();
}

RWSProtocolHandler::~RWSProtocolHandler()
{
    qDebug() << "RWSProtocolHandler: 析构";
}

void RWSProtocolHandler::setProtocolType(ProtocolType type)
{
    if (m_protocolType != type) {
        m_protocolType = type;
        clearBuffer(); // 切换协议时清空缓冲区
        qDebug() << "RWSProtocolHandler: 协议类型已切换为:" << getProtocolName();
    }
}

QByteArray RWSProtocolHandler::wrapMessage(const QByteArray &message)
{
    switch (m_protocolType) {
    case PreLength:
        return wrapPreLengthMessage(message);
    case EndWithNewLine:
        return wrapEndWithNewLineMessage(message);
    case Simple:
        return wrapSimpleMessage(message);
    default:
        qWarning() << "RWSProtocolHandler: 不支持的协议类型:" << static_cast<int>(m_protocolType);
        return QByteArray();
    }
}

QList<QByteArray> RWSProtocolHandler::processReceivedData(const QByteArray &newData)
{
    if (newData.isEmpty()) {
        return QList<QByteArray>();
    }

    // 直接按协议处理数据，压缩检测在消息提取后进行
    switch (m_protocolType) {
    case PreLength:
        return processPreLengthData(newData);
    case EndWithNewLine:
        return processEndWithNewLineData(newData);
    case Simple:
        return processSimpleData(newData);
    default:
        qWarning() << "RWSProtocolHandler: 不支持的协议类型:" << static_cast<int>(m_protocolType);
        return QList<QByteArray>();
    }
}

void RWSProtocolHandler::clearBuffer()
{
    m_receiveBuffer.clear();
    qDebug() << "RWSProtocolHandler: 缓冲区已清空";
}

QString RWSProtocolHandler::getProtocolName() const
{
    return protocolTypeToString(m_protocolType);
}

QString RWSProtocolHandler::protocolTypeToString(ProtocolType type)
{
    switch (type) {
    case PreLength:
        return "PreLength";
    case EndWithNewLine:
        return "EndWithNewLine";
    case Simple:
        return "Simple";
    default:
        return "Unknown";
    }
}

RWSProtocolHandler::ProtocolType RWSProtocolHandler::stringToProtocolType(const QString &str)
{
    QString lowerStr = str.toLower();

    if (lowerStr == "prelength" || lowerStr == "pre_length" || lowerStr == "PreLength") {
        return PreLength;
    } else if (lowerStr == "endwithnewline" || lowerStr == "end_with_new_line" ||
               lowerStr == "newline" || lowerStr == "EndWithNewLine") {
        return EndWithNewLine;
    } else if (lowerStr == "simple" || lowerStr == "Simple") {
        return Simple;
    } else {
        qWarning() << "RWSProtocolHandler: 未知的协议类型字符串:" << str << "，使用默认协议 PreLength";
        return PreLength; // 默认协议
    }
}

// ========== PreLength 协议实现 ==========

QByteArray RWSProtocolHandler::wrapPreLengthMessage(const QByteArray &message)
{
    if (message.isEmpty()) {
        return QByteArray();
    }

    // 创建带长度头的数据包
    QByteArray packet;
    QDataStream stream(&packet, QIODevice::WriteOnly);
    stream.setByteOrder(QDataStream::BigEndian);

    // 写入长度头（4字节）
    stream << static_cast<quint32>(message.size());

    // 写入消息内容
    packet.append(message);

    return packet;
}

QList<QByteArray> RWSProtocolHandler::processPreLengthData(const QByteArray &newData)
{
    // 添加新数据到缓冲区
    m_receiveBuffer.append(newData);

    // 提取完整消息
    return extractPreLengthMessages();
}

QList<QByteArray> RWSProtocolHandler::extractPreLengthMessages()
{
    QList<QByteArray> messages;

    while (m_receiveBuffer.size() >= LENGTH_HEADER_SIZE) {
        // 读取长度头
        QDataStream stream(m_receiveBuffer);
        stream.setByteOrder(QDataStream::BigEndian);

        quint32 messageLength;
        stream >> messageLength;

        // 验证消息长度
        if (!isValidMessageLength(messageLength)) {
            qWarning() << "RWSProtocolHandler: 无效的消息长度:" << messageLength;
            m_receiveBuffer.clear(); // 清空缓冲区，避免错误传播
            break;
        }

        // 检查是否有完整消息
        if (m_receiveBuffer.size() < LENGTH_HEADER_SIZE + messageLength) {
            break; // 数据不完整，等待更多数据
        }

        // 提取消息
        QByteArray message = m_receiveBuffer.mid(LENGTH_HEADER_SIZE, messageLength);

        // 尝试解压缩消息（CommandTransceiver 格式）
        QByteArray processedMessage = decompressData(message);

        // 如果数据被解压缩，更新统计信息
        if (processedMessage.size() != message.size()) {
            m_compressedPacketsReceived++;
            m_totalCompressedBytes += message.size();
            m_totalUncompressedBytes += processedMessage.size();

            qDebug() << "RWSProtocolHandler: 解压缩消息"
                     << "压缩大小:" << message.size()
                     << "解压后大小:" << processedMessage.size()
                     << "压缩率:" << QString::number((1.0 - double(message.size()) / processedMessage.size()) * 100, 'f', 1) + "%";
        }

        messages.append(processedMessage);

        // 从缓冲区移除已处理的数据
        m_receiveBuffer.remove(0, LENGTH_HEADER_SIZE + messageLength);
    }

    return messages;
}

// ========== EndWithNewLine 协议实现 ==========

QByteArray RWSProtocolHandler::wrapEndWithNewLineMessage(const QByteArray &message)
{
    if (message.isEmpty()) {
        return QByteArray();
    }

    // 添加换行符分隔符
    QByteArray packet = message;
    packet.append(NEWLINE_DELIMITER);

    return packet;
}

QList<QByteArray> RWSProtocolHandler::processEndWithNewLineData(const QByteArray &newData)
{
    // 添加新数据到缓冲区
    m_receiveBuffer.append(newData);

    // 提取完整行
    return extractEndWithNewLineMessages();
}

QList<QByteArray> RWSProtocolHandler::extractEndWithNewLineMessages()
{
    QList<QByteArray> messages;

    int delimiterPos;
    while ((delimiterPos = m_receiveBuffer.indexOf(NEWLINE_DELIMITER)) != -1) {
        // 提取一行数据（不包含分隔符）
        QByteArray message = m_receiveBuffer.left(delimiterPos);

        // 验证行长度
        if (message.size() <= MAX_LINE_LENGTH) {
            messages.append(message);
        } else {
            qWarning() << "RWSProtocolHandler: 行长度超出限制:" << message.size();
        }

        // 从缓冲区移除已处理的数据（包含分隔符）
        m_receiveBuffer.remove(0, delimiterPos + NEWLINE_DELIMITER.size());
    }

    return messages;
}

// ========== Simple 协议实现 ==========

QByteArray RWSProtocolHandler::wrapSimpleMessage(const QByteArray &message)
{
    // 简单协议直接返回原始数据，不做任何封装
    return message;
}

QList<QByteArray> RWSProtocolHandler::processSimpleData(const QByteArray &newData)
{
    // 简单协议直接返回接收到的数据
    QList<QByteArray> messages;
    if (!newData.isEmpty()) {
        messages.append(newData);
    }
    return messages;
}

// ========== 辅助方法 ==========

bool RWSProtocolHandler::isValidMessageLength(quint32 length) const
{
    return length > 0 && length <= MAX_MESSAGE_SIZE;
}

// ========== 压缩相关方法 ==========

QByteArray RWSProtocolHandler::decompressData(const QByteArray &data)
{
    if (data.isEmpty()) {
        return data;
    }

    // 直接尝试解压缩（CommandTransceiver 使用 qCompress 格式）
    QByteArray uncompressed = qUncompress(data);
    if (!uncompressed.isEmpty()) {
        // 解压缩成功，返回解压后的数据
        return uncompressed;
    }

    // 解压缩失败，说明不是压缩数据，返回原始数据
    return data;
}

bool RWSProtocolHandler::isCompressedData(const QByteArray &data)
{
    if (data.isEmpty()) {
        return false;
    }

    // 简单检测：尝试解压缩，如果成功则认为是压缩数据
    QByteArray test = qUncompress(data);
    return !test.isEmpty();
}

QVariantMap RWSProtocolHandler::getCompressionStats() const
{
    QVariantMap stats;
    stats["compressedPacketsReceived"] = m_compressedPacketsReceived;
    stats["totalCompressedBytes"] = m_totalCompressedBytes;
    stats["totalUncompressedBytes"] = m_totalUncompressedBytes;

    if (m_totalUncompressedBytes > 0) {
        double compressionRatio = 1.0 - (double(m_totalCompressedBytes) / m_totalUncompressedBytes);
        stats["compressionRatio"] = QString::number(compressionRatio * 100, 'f', 2) + "%";
    } else {
        stats["compressionRatio"] = "0%";
    }

    return stats;
}
