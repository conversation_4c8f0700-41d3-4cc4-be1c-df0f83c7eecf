#include "NetworkTestClient.h"
#include <QApplication>
#include <QMessageBox>
#include <QScrollBar>
#include <QDebug>
#include <QJsonParseError>
#include <QJsonDocument>
#include <QHostAddress>

NetworkTestClient::NetworkTestClient(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_commandTransceiver(nullptr)
    , m_connectionState(NetworkWorker::ConnectionState::Disconnected)
    , m_commandsSent(0)
    , m_responsesReceived(0)
    , m_commandsReceived(0)
    , m_repliesReceived(0)
    , m_bytesSent(0)
    , m_bytesReceived(0)
{
    setWindowTitle("CommandTransceiver 测试客户端 - 完整功能测试");
    setMinimumSize(800, 700);
    resize(1000, 800);

    setupUI();
    setupCommandTemplates();
    setupCommandTransceiverComponents();
    connectSignals();

    // 设置默认服务器地址
    m_hostEdit->setText("127.0.0.1");
    m_portSpinBox->setValue(8080);

    // 初始状态设置
    setUIEnabled(false);
    m_connectButton->setEnabled(false); // 在初始化完成前禁用连接按钮
    updateConnectionStatus();
    updateStatistics();

    // 启动状态更新定时器
    m_statusUpdateTimer = new QTimer(this);
    connect(m_statusUpdateTimer, &QTimer::timeout, this, &NetworkTestClient::updateConnectionStatus);
    m_statusUpdateTimer->start(1000); // 每秒更新一次状态

    addLogMessage("CommandTransceiver 测试客户端已启动", "SYSTEM");
    addLogMessage("支持JSON命令格式，可测试异步命令发送、连接管理等功能", "INFO");
}

NetworkTestClient::~NetworkTestClient()
{
    if (m_commandTransceiver) {
        // 断开连接
        if (m_connectionState == CommandTransceiverRefactored::ConnectionState::Connected) {
            m_commandTransceiver->disconnectFromServer();
        }

        // CommandTransceiver会自动清理资源
        m_commandTransceiver->deleteLater();
    }

    addLogMessage("CommandTransceiver 测试客户端已关闭", "SYSTEM");
}

void NetworkTestClient::setupUI()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    m_mainLayout = new QVBoxLayout(m_centralWidget);

    // 连接配置组
    m_connectionGroup = new QGroupBox("服务器连接配置", this);
    m_connectionLayout = new QGridLayout(m_connectionGroup);

    m_hostLabel = new QLabel("服务器地址:", this);
    m_hostEdit = new QLineEdit(this);
    m_hostEdit->setPlaceholderText("输入服务器IP地址");

    m_portLabel = new QLabel("端口:", this);
    m_portSpinBox = new QSpinBox(this);
    m_portSpinBox->setRange(1, 65535);
    m_portSpinBox->setValue(8080);

    m_connectButton = new QPushButton("连接", this);
    m_connectButton->setMinimumHeight(30);

    m_statusLabel = new QLabel("状态: 未连接", this);
    m_statusLabel->setStyleSheet("color: red; font-weight: bold;");

    m_connectionLayout->addWidget(m_hostLabel, 0, 0);
    m_connectionLayout->addWidget(m_hostEdit, 0, 1);
    m_connectionLayout->addWidget(m_portLabel, 0, 2);
    m_connectionLayout->addWidget(m_portSpinBox, 0, 3);
    m_connectionLayout->addWidget(m_connectButton, 0, 4);
    m_connectionLayout->addWidget(m_statusLabel, 1, 0, 1, 5);

    m_mainLayout->addWidget(m_connectionGroup);

    // 创建主分割器
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);
    m_mainLayout->addWidget(m_mainSplitter);

    // JSON命令发送组
    m_sendGroup = new QGroupBox("JSON命令发送", this);
    m_sendLayout = new QVBoxLayout(m_sendGroup);

    // 命令模板选择
    m_templateLayout = new QHBoxLayout();
    m_templateLabel = new QLabel("命令模板:", this);
    m_templateComboBox = new QComboBox(this);
    m_templateComboBox->setMinimumWidth(200);

    m_templateLayout->addWidget(m_templateLabel);
    m_templateLayout->addWidget(m_templateComboBox);
    m_templateLayout->addStretch();

    m_sendLayout->addLayout(m_templateLayout);

    // JSON命令输入
    m_commandEdit = new QTextEdit(this);
    m_commandEdit->setPlaceholderText("输入JSON格式的命令...\n例如: {\"command\": \"test\", \"data\": \"hello\"}");
    m_commandEdit->setMaximumHeight(120);
    m_commandEdit->setFont(QFont("Consolas", 10));

    m_sendButton = new QPushButton("发送命令", this);
    m_sendButton->setMinimumHeight(30);

    m_sendLayout->addWidget(m_commandEdit);
    m_sendLayout->addWidget(m_sendButton);

    m_mainSplitter->addWidget(m_sendGroup);

    // 响应接收组
    m_receiveGroup = new QGroupBox("响应和日志", this);
    m_receiveLayout = new QVBoxLayout(m_receiveGroup);

    m_receiveTextEdit = new QTextEdit(this);
    m_receiveTextEdit->setReadOnly(true);
    m_receiveTextEdit->setFont(QFont("Consolas", 9));

    m_clearButton = new QPushButton("清空日志", this);
    m_clearButton->setMaximumWidth(100);

    QHBoxLayout *receiveButtonLayout = new QHBoxLayout();
    receiveButtonLayout->addStretch();
    receiveButtonLayout->addWidget(m_clearButton);

    m_receiveLayout->addWidget(m_receiveTextEdit);
    m_receiveLayout->addLayout(receiveButtonLayout);

    m_mainSplitter->addWidget(m_receiveGroup);

    // 设置分割器比例
    m_mainSplitter->setStretchFactor(0, 1);
    m_mainSplitter->setStretchFactor(1, 2);

    // 统计信息组
    m_statsGroup = new QGroupBox("统计信息", this);
    m_statsLayout = new QGridLayout(m_statsGroup);

    m_sentCountLabel = new QLabel("发送命令数: 0", this);
    m_receivedCountLabel = new QLabel("接收响应数: 0", this);
    m_sentBytesLabel = new QLabel("发送字节数: 0", this);
    m_receivedBytesLabel = new QLabel("接收字节数: 0", this);

    m_statsLayout->addWidget(m_sentCountLabel, 0, 0);
    m_statsLayout->addWidget(m_receivedCountLabel, 0, 1);
    m_statsLayout->addWidget(m_sentBytesLabel, 1, 0);
    m_statsLayout->addWidget(m_receivedBytesLabel, 1, 1);

    m_mainLayout->addWidget(m_statsGroup);

    // 设置状态栏
    statusBar()->showMessage("就绪 - 支持CommandTransceiver完整功能测试");
}

void NetworkTestClient::setupCommandTemplates()
{
    // 初始化命令模板
    m_commandTemplates["自定义"] = "";
    m_commandTemplates["简单测试"] = "{\n  \"command\": \"test\",\n  \"data\": \"hello world\"\n}";
    m_commandTemplates["状态查询"] = "{\n  \"command\": \"status\",\n  \"type\": \"query\"\n}";
    m_commandTemplates["数据发送"] = "{\n  \"command\": \"send_data\",\n  \"payload\": {\n    \"message\": \"test message\",\n    \"timestamp\": \"2025-01-21T10:00:00Z\"\n  }\n}";
    m_commandTemplates["配置设置"] = "{\n  \"command\": \"config\",\n  \"settings\": {\n    \"timeout\": 5000,\n    \"retry_count\": 3\n  }\n}";
    m_commandTemplates["心跳检测"] = "{\n  \"command\": \"heartbeat\",\n  \"timestamp\": \"2025-01-21T10:00:00Z\"\n}";

    // 填充模板下拉框
    for (auto it = m_commandTemplates.begin(); it != m_commandTemplates.end(); ++it) {
        m_templateComboBox->addItem(it.key());
    }
}

void NetworkTestClient::setupCommandTransceiverComponents()
{
    // 创建CommandTransceiver实例
    m_commandTransceiver = new CommandTransceiverRefactored(this);

    addLogMessage("CommandTransceiver组件已创建，正在初始化...", "SYSTEM");
}

void NetworkTestClient::connectSignals()
{
    // UI信号连接
    connect(m_connectButton, &QPushButton::clicked, this, &NetworkTestClient::onConnectButtonClicked);
    connect(m_sendButton, &QPushButton::clicked, this, &NetworkTestClient::onSendButtonClicked);
    connect(m_clearButton, &QPushButton::clicked, this, &NetworkTestClient::onClearButtonClicked);
    connect(m_templateComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &NetworkTestClient::onCommandTemplateChanged);

    // CommandTransceiver信号连接
    connect(m_commandTransceiver, &CommandTransceiverRefactored::initialized,
            this, &NetworkTestClient::onCommandTransceiverInitialized);
    connect(m_commandTransceiver, &CommandTransceiverRefactored::connectionStateChanged,
            this, &NetworkTestClient::onConnectionStateChanged);
    connect(m_commandTransceiver, &CommandTransceiverRefactored::commandResponseReceived,
            this, &NetworkTestClient::onCommandResponseReceived);
    connect(m_commandTransceiver, &CommandTransceiverRefactored::commandReceived,
            this, &NetworkTestClient::onCommandReceived);
    connect(m_commandTransceiver, &CommandTransceiverRefactored::replyReceived,
            this, &NetworkTestClient::onReplyReceived);
    connect(m_commandTransceiver, &CommandTransceiverRefactored::errorOccurred,
            this, &NetworkTestClient::onErrorOccurred);
}

void NetworkTestClient::onConnectButtonClicked()
{
    if (m_connectionState != CommandTransceiverRefactored::ConnectionState::Connected) {
        // 连接到服务器
        QString host = m_hostEdit->text().trimmed();
        quint16 port = static_cast<quint16>(m_portSpinBox->value());

        if (host.isEmpty()) {
            QMessageBox::warning(this, "输入错误", "请输入服务器地址！");
            return;
        }

        addLogMessage(QString("正在连接到 %1:%2...").arg(host).arg(port), "INFO");
        statusBar()->showMessage("正在连接...");

        // 禁用连接按钮，防止重复点击
        m_connectButton->setEnabled(false);
        m_connectButton->setText("连接中...");

        // 配置连接参数
        CommandTransceiverRefactored::ConnectionConfig config;
        config.serverAddress = QHostAddress(host);
        config.serverPort = port;
        config.connectTimeoutMs = 5000;
        config.commandTimeoutMs = 30000;
        config.autoReconnect = true;
        config.reconnectIntervalMs = 3000;
        config.enableHeartbeat = true;
        config.heartbeatIntervalMs = 30000;

        addLogMessage(QString("连接配置: 超时=%1ms, 自动重连=%2, 心跳=%3")
                      .arg(config.connectTimeoutMs)
                      .arg(config.autoReconnect ? "是" : "否")
                      .arg(config.enableHeartbeat ? "是" : "否"), "INFO");

        // 发起连接（内部会处理初始化等待）
        bool success = m_commandTransceiver->connectToServer(config);
        if (!success) {
            addLogMessage("连接启动失败", "ERROR");
            m_connectButton->setEnabled(true);
            m_connectButton->setText("连接");
        } else {
            addLogMessage("连接请求已发送（如果组件未初始化完成，将在初始化后自动连接）", "INFO");
        }
    } else {
        // 断开连接
        addLogMessage("正在断开连接...", "INFO");
        statusBar()->showMessage("正在断开连接...");

        m_connectButton->setEnabled(false);
        m_connectButton->setText("断开中...");

        m_commandTransceiver->disconnectFromServer();
    }
}

void NetworkTestClient::onSendButtonClicked()
{
    QString commandText = m_commandEdit->toPlainText().trimmed();
    if (commandText.isEmpty()) {
        QMessageBox::information(this, "提示", "请输入要发送的JSON命令！");
        return;
    }

    if (m_connectionState != CommandTransceiverRefactored::ConnectionState::Connected) {
        QMessageBox::warning(this, "连接错误", "请先连接到服务器！");
        return;
    }

    // 解析JSON命令
    QJsonObject command = parseJsonCommand(commandText);
    if (command.isEmpty()) {
        QMessageBox::warning(this, "格式错误", "JSON命令格式不正确！");
        return;
    }

    // 发送命令
    QUuid commandUuid = m_commandTransceiver->sendCommand(command);

    addLogMessage(QString("发送命令 [%1]: %2")
                  .arg(commandUuid.toString().left(8))
                  .arg(formatJsonForDisplay(command)), "SEND");

    // 更新统计
    {
        QMutexLocker locker(&m_statsMutex);
        m_commandsSent++;
    }
    updateStatistics();
}

void NetworkTestClient::onClearButtonClicked()
{
    m_receiveTextEdit->clear();
    addLogMessage("日志区域已清空", "SYSTEM");
}

void NetworkTestClient::onCommandTemplateChanged()
{
    QString templateName = m_templateComboBox->currentText();
    QString templateContent = getCommandTemplate(templateName);

    if (!templateContent.isEmpty()) {
        m_commandEdit->setPlainText(templateContent);
        addLogMessage(QString("已加载命令模板: %1").arg(templateName), "INFO");
    }
}

void NetworkTestClient::onCommandTransceiverInitialized()
{
    addLogMessage("CommandTransceiver组件初始化完成，可以开始连接", "SUCCESS");

    // 启用连接按钮
    m_connectButton->setEnabled(true);
}

void NetworkTestClient::onConnectionStateChanged(NetworkWorker::ConnectionState state)
{
    qDebug() << "NetworkTestClient: 收到连接状态变化信号:" << static_cast<int>(state);

    m_connectionState = state;

    QString stateText;
    QString logType;
    bool connected = false;

    switch (state) {
        case NetworkWorker::ConnectionState::Disconnected:
            stateText = "未连接";
            logType = "WARNING";
            m_connectButton->setText("连接");
            break;
        case NetworkWorker::ConnectionState::Connecting:
            stateText = "连接中";
            logType = "INFO";
            m_connectButton->setText("连接中...");
            break;
        case NetworkWorker::ConnectionState::Connected:
            stateText = "已连接";
            logType = "SUCCESS";
            connected = true;
            m_connectButton->setText("断开连接");
            break;
        case NetworkWorker::ConnectionState::Reconnecting:
            stateText = "重连中";
            logType = "INFO";
            m_connectButton->setText("重连中...");
            break;
        case NetworkWorker::ConnectionState::Error:
            stateText = "连接错误";
            logType = "ERROR";
            m_connectButton->setText("连接");
            break;
    }

    qDebug() << "NetworkTestClient: 状态文本:" << stateText << "连接状态:" << connected;

    setUIEnabled(connected);
    m_connectButton->setEnabled(state != NetworkWorker::ConnectionState::Connecting);

    addLogMessage(QString("连接状态变化: %1 (状态码: %2)").arg(stateText).arg(static_cast<int>(state)), logType);
    statusBar()->showMessage(stateText);

    updateConnectionStatus();

    qDebug() << "NetworkTestClient: 状态更新完成";
}

void NetworkTestClient::onCommandResponseReceived(const CommandTransceiverRefactored::CommandResponse &response)
{
    QString resultText;
    QString logType;

    switch (response.result) {
        case CommandTransceiverRefactored::CommandResult::Success:
            resultText = "成功";
            logType = "SUCCESS";
            break;
        case CommandTransceiverRefactored::CommandResult::Timeout:
            resultText = "超时";
            logType = "ERROR";
            break;
        case CommandTransceiverRefactored::CommandResult::NetworkError:
            resultText = "网络错误";
            logType = "ERROR";
            break;
        case CommandTransceiverRefactored::CommandResult::InvalidCommand:
            resultText = "无效命令";
            logType = "ERROR";
            break;
        case CommandTransceiverRefactored::CommandResult::ServerError:
            resultText = "服务器错误";
            logType = "ERROR";
            break;
        case CommandTransceiverRefactored::CommandResult::NotConnected:
            resultText = "未连接";
            logType = "ERROR";
            break;
        case CommandTransceiverRefactored::CommandResult::ProcessingError:
            resultText = "处理错误";
            logType = "ERROR";
            break;
    }

    QString message = QString("命令响应 [%1]: %2")
                      .arg(response.commandUuid.toString().left(8))
                      .arg(resultText);

    if (!response.errorMessage.isEmpty()) {
        message += QString(" - %1").arg(response.errorMessage);
    }

    if (!response.data.isEmpty()) {
        message += QString("\n数据: %1").arg(formatJsonForDisplay(response.data));
    }

    message += QString("\n耗时: %1ms").arg(response.getTotalTime());

    addLogMessage(message, logType);

    // 更新统计
    {
        QMutexLocker locker(&m_statsMutex);
        m_responsesReceived++;
    }
    updateStatistics();
}

void NetworkTestClient::onCommandReceived(const QJsonObject &command, const QUuid &uuid)
{
    addLogMessage(QString("接收到命令 [%1]: %2")
                  .arg(uuid.toString().left(8))
                  .arg(formatJsonForDisplay(command)), "RECEIVE");

    // 更新统计
    {
        QMutexLocker locker(&m_statsMutex);
        m_commandsReceived++;
    }
    updateStatistics();
}

void NetworkTestClient::onReplyReceived(const QJsonObject &reply, const QUuid &uuid)
{
    addLogMessage(QString("接收到回复 [%1]: %2")
                  .arg(uuid.toString().left(8))
                  .arg(formatJsonForDisplay(reply)), "RECEIVE");

    // 更新统计
    {
        QMutexLocker locker(&m_statsMutex);
        m_repliesReceived++;
    }
    updateStatistics();
}

void NetworkTestClient::onErrorOccurred(const QString &error)
{
    addLogMessage(QString("系统错误: %1").arg(error), "ERROR");
    statusBar()->showMessage(QString("错误: %1").arg(error));
}

void NetworkTestClient::updateConnectionStatus()
{
    // 检查实际的连接状态，防止状态不同步
    if (m_commandTransceiver && m_commandTransceiver->isInitialized()) {
        CommandTransceiverRefactored::ConnectionState actualState = m_commandTransceiver->getConnectionState();
        if (actualState != m_connectionState) {
            qDebug() << "NetworkTestClient: 检测到状态不同步，本地状态:" << static_cast<int>(m_connectionState)
                     << "实际状态:" << static_cast<int>(actualState);
            // 同步状态
            onConnectionStateChanged(actualState);
            return; // onConnectionStateChanged 会调用 updateConnectionStatus
        }
    }

    QString statusText;
    QString styleSheet;

    switch (m_connectionState) {
        case CommandTransceiverRefactored::ConnectionState::Disconnected:
            statusText = "状态: 未连接";
            styleSheet = "color: red; font-weight: bold;";
            break;
        case CommandTransceiverRefactored::ConnectionState::Connecting:
            statusText = "状态: 连接中";
            styleSheet = "color: orange; font-weight: bold;";
            break;
        case CommandTransceiverRefactored::ConnectionState::Connected:
            statusText = "状态: 已连接";
            styleSheet = "color: green; font-weight: bold;";
            break;
        case CommandTransceiverRefactored::ConnectionState::Reconnecting:
            statusText = "状态: 重连中";
            styleSheet = "color: orange; font-weight: bold;";
            break;
        case CommandTransceiverRefactored::ConnectionState::Error:
            statusText = "状态: 连接错误";
            styleSheet = "color: red; font-weight: bold;";
            break;
    }

    m_statusLabel->setText(statusText);
    m_statusLabel->setStyleSheet(styleSheet);
}

void NetworkTestClient::onCommandInputReturnPressed()
{
    if (m_connectionState == CommandTransceiverRefactored::ConnectionState::Connected &&
        !m_commandEdit->toPlainText().isEmpty()) {
        onSendButtonClicked();
    }
}

void NetworkTestClient::addLogMessage(const QString &message, const QString &type)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString coloredMessage;

    if (type == "ERROR") {
        coloredMessage = QString("<span style='color: red;'>[%1] %2</span>").arg(timestamp, message);
    } else if (type == "SUCCESS") {
        coloredMessage = QString("<span style='color: green;'>[%1] %2</span>").arg(timestamp, message);
    } else if (type == "WARNING") {
        coloredMessage = QString("<span style='color: orange;'>[%1] %2</span>").arg(timestamp, message);
    } else if (type == "SEND") {
        coloredMessage = QString("<span style='color: blue;'>[%1] %2</span>").arg(timestamp, message);
    } else if (type == "RECEIVE") {
        coloredMessage = QString("<span style='color: purple;'>[%1] %2</span>").arg(timestamp, message);
    } else if (type == "SYSTEM") {
        coloredMessage = QString("<span style='color: gray;'>[%1] %2</span>").arg(timestamp, message);
    } else {
        coloredMessage = QString("[%1] %2").arg(timestamp, message);
    }

    m_receiveTextEdit->append(coloredMessage);

    // 自动滚动到底部
    QScrollBar *scrollBar = m_receiveTextEdit->verticalScrollBar();
    scrollBar->setValue(scrollBar->maximum());
}

void NetworkTestClient::updateStatistics()
{
    QMutexLocker locker(&m_statsMutex);

    m_sentCountLabel->setText(QString("发送命令数: %1").arg(m_commandsSent));
    m_receivedCountLabel->setText(QString("接收响应数: %1 (命令:%2, 回复:%3)")
                                  .arg(m_responsesReceived + m_commandsReceived + m_repliesReceived)
                                  .arg(m_commandsReceived)
                                  .arg(m_repliesReceived));
    m_sentBytesLabel->setText(QString("发送字节数: %1").arg(m_bytesSent));
    m_receivedBytesLabel->setText(QString("接收字节数: %1").arg(m_bytesReceived));
}

void NetworkTestClient::setUIEnabled(bool connected)
{
    m_hostEdit->setEnabled(!connected);
    m_portSpinBox->setEnabled(!connected);
    m_sendButton->setEnabled(connected);
    m_commandEdit->setEnabled(connected);
    m_templateComboBox->setEnabled(connected);
}

QString NetworkTestClient::formatJsonForDisplay(const QJsonObject &json)
{
    QJsonDocument doc(json);
    return doc.toJson(QJsonDocument::Compact);
}

QJsonObject NetworkTestClient::parseJsonCommand(const QString &text)
{
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(text.toUtf8(), &error);

    if (error.error != QJsonParseError::NoError) {
        addLogMessage(QString("JSON解析错误: %1").arg(error.errorString()), "ERROR");
        return QJsonObject();
    }

    if (!doc.isObject()) {
        addLogMessage("JSON必须是对象格式", "ERROR");
        return QJsonObject();
    }

    return doc.object();
}

QString NetworkTestClient::getCommandTemplate(const QString &templateName)
{
    return m_commandTemplates.value(templateName, "");
}
