#include "ServerMainWindow.h"
#include <QCloseEvent>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QJsonArray>

ServerMainWindow::ServerMainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_server(new TestServer(this))
    , m_statisticsTimer(new QTimer(this))
    , m_timeTimer(new QTimer(this))
{
    setWindowTitle("CommandTransceiver 测试服务器 - GUI版本");
    setMinimumSize(1200, 800);
    resize(1400, 900);
    
    // 初始化服务器配置
    m_serverConfig.port = 8080;
    m_serverConfig.protocolType = RWSProtocolHandler::PreLength;
    m_serverConfig.enableHeartbeat = false;
    m_serverConfig.heartbeatInterval = 30000;
    m_serverConfig.maxConnections = 100;
    m_serverConfig.enableLogging = true;
    m_serverConfig.enableEcho = true;
    m_serverConfig.enableAutoResponse = true;
    
    setupUI();
    setupMenuBar();
    setupStatusBar();
    
    // 连接服务器信号
    connect(m_server, &TestServer::serverStarted, this, &ServerMainWindow::onServerStarted);
    connect(m_server, &TestServer::serverStopped, this, &ServerMainWindow::onServerStopped);
    connect(m_server, &TestServer::clientConnected, this, &ServerMainWindow::onClientConnected);
    connect(m_server, &TestServer::clientDisconnected, this, &ServerMainWindow::onClientDisconnected);
    connect(m_server, &TestServer::jsonCommandReceived, this, &ServerMainWindow::onJsonCommandReceived);
    connect(m_server, &TestServer::rawDataReceived, this, &ServerMainWindow::onRawDataReceived);
    
    // 设置定时器
    connect(m_statisticsTimer, &QTimer::timeout, this, &ServerMainWindow::updateStatistics);
    connect(m_timeTimer, &QTimer::timeout, this, [this]() {
        m_timeLabel->setText(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    });
    
    m_statisticsTimer->start(UPDATE_INTERVAL_MS);
    m_timeTimer->start(1000);
    
    // 初始化UI状态
    updateServerStatus();
    addLogMessage("服务器GUI界面已初始化", "SYSTEM");
}

ServerMainWindow::~ServerMainWindow()
{
    if (m_server && m_server->isRunning()) {
        m_server->stopServer();
    }
}

void ServerMainWindow::closeEvent(QCloseEvent *event)
{
    if (m_server && m_server->isRunning()) {
        QMessageBox::StandardButton reply = QMessageBox::question(
            this, "确认退出", "服务器正在运行，是否要停止服务器并退出？",
            QMessageBox::Yes | QMessageBox::No, QMessageBox::No);
        
        if (reply == QMessageBox::Yes) {
            m_server->stopServer();
            event->accept();
        } else {
            event->ignore();
        }
    } else {
        event->accept();
    }
}

void ServerMainWindow::setupUI()
{
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    
    // 创建主布局
    QHBoxLayout *mainLayout = new QHBoxLayout(centralWidget);
    
    // 创建左右分割器
    QSplitter *mainSplitter = new QSplitter(Qt::Horizontal, this);
    mainLayout->addWidget(mainSplitter);
    
    // 左侧面板
    QWidget *leftPanel = new QWidget();
    leftPanel->setMinimumWidth(400);
    leftPanel->setMaximumWidth(500);
    QVBoxLayout *leftLayout = new QVBoxLayout(leftPanel);
    
    setupServerControlGroup();
    setupConnectionGroup();
    setupStatisticsGroup();
    
    leftLayout->addWidget(m_serverControlGroup);
    leftLayout->addWidget(m_connectionGroup);
    leftLayout->addWidget(m_statisticsGroup);
    leftLayout->addStretch();
    
    // 右侧面板
    QWidget *rightPanel = new QWidget();
    QVBoxLayout *rightLayout = new QVBoxLayout(rightPanel);
    
    setupMessageGroup();
    setupSendGroup();
    
    rightLayout->addWidget(m_messageGroup, 2);
    rightLayout->addWidget(m_sendGroup, 1);
    
    mainSplitter->addWidget(leftPanel);
    mainSplitter->addWidget(rightPanel);
    mainSplitter->setStretchFactor(0, 0);
    mainSplitter->setStretchFactor(1, 1);
}

void ServerMainWindow::setupMenuBar()
{
    QMenuBar *menuBar = this->menuBar();
    
    // 文件菜单
    QMenu *fileMenu = menuBar->addMenu("文件(&F)");
    
    QAction *saveLogsAction = new QAction("保存日志(&S)", this);
    saveLogsAction->setShortcut(QKeySequence::Save);
    connect(saveLogsAction, &QAction::triggered, this, &ServerMainWindow::onSaveLogsClicked);
    fileMenu->addAction(saveLogsAction);
    
    fileMenu->addSeparator();
    
    QAction *exitAction = new QAction("退出(&X)", this);
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);
    fileMenu->addAction(exitAction);
    
    // 服务器菜单
    QMenu *serverMenu = menuBar->addMenu("服务器(&S)");
    
    QAction *startAction = new QAction("启动服务器(&S)", this);
    startAction->setShortcut(QKeySequence("F5"));
    connect(startAction, &QAction::triggered, this, &ServerMainWindow::onStartServerClicked);
    serverMenu->addAction(startAction);
    
    QAction *stopAction = new QAction("停止服务器(&T)", this);
    stopAction->setShortcut(QKeySequence("F6"));
    connect(stopAction, &QAction::triggered, this, &ServerMainWindow::onStopServerClicked);
    serverMenu->addAction(stopAction);
    
    // 工具菜单
    QMenu *toolsMenu = menuBar->addMenu("工具(&T)");
    
    QAction *clearLogsAction = new QAction("清空日志(&C)", this);
    clearLogsAction->setShortcut(QKeySequence("Ctrl+L"));
    connect(clearLogsAction, &QAction::triggered, this, &ServerMainWindow::onClearLogsClicked);
    toolsMenu->addAction(clearLogsAction);
    
    QAction *refreshAction = new QAction("刷新连接列表(&R)", this);
    refreshAction->setShortcut(QKeySequence::Refresh);
    connect(refreshAction, &QAction::triggered, this, &ServerMainWindow::onRefreshConnectionsClicked);
    toolsMenu->addAction(refreshAction);
    
    // 帮助菜单
    QMenu *helpMenu = menuBar->addMenu("帮助(&H)");
    
    QAction *aboutAction = new QAction("关于(&A)", this);
    connect(aboutAction, &QAction::triggered, this, [this]() {
        QMessageBox::about(this, "关于", 
            "CommandTransceiver 测试服务器 GUI版本\n\n"
            "版本: 1.0.0\n"
            "用途: 为 CommandTransceiver 客户端提供测试服务\n"
            "支持协议: PreLength, EndWithNewLine, Simple\n\n"
            "© 2024 CommandTransceiver Project");
    });
    helpMenu->addAction(aboutAction);
}

void ServerMainWindow::setupStatusBar()
{
    QStatusBar *statusBar = this->statusBar();
    
    m_statusLabel = new QLabel("就绪");
    m_timeLabel = new QLabel();
    
    statusBar->addWidget(m_statusLabel, 1);
    statusBar->addPermanentWidget(m_timeLabel);
}

void ServerMainWindow::setupServerControlGroup()
{
    m_serverControlGroup = new QGroupBox("服务器控制");
    QGridLayout *layout = new QGridLayout(m_serverControlGroup);
    
    // 端口设置
    layout->addWidget(new QLabel("端口:"), 0, 0);
    m_portSpinBox = new QSpinBox();
    m_portSpinBox->setRange(1, 65535);
    m_portSpinBox->setValue(m_serverConfig.port);
    layout->addWidget(m_portSpinBox, 0, 1);
    
    // 协议选择
    layout->addWidget(new QLabel("协议:"), 1, 0);
    m_protocolComboBox = new QComboBox();
    m_protocolComboBox->addItem("PreLength", static_cast<int>(RWSProtocolHandler::PreLength));
    m_protocolComboBox->addItem("EndWithNewLine", static_cast<int>(RWSProtocolHandler::EndWithNewLine));
    m_protocolComboBox->addItem("Simple", static_cast<int>(RWSProtocolHandler::Simple));
    layout->addWidget(m_protocolComboBox, 1, 1);
    
    // 服务器状态
    layout->addWidget(new QLabel("状态:"), 2, 0);
    m_serverStatusLabel = new QLabel("已停止");
    m_serverStatusLabel->setStyleSheet("color: red; font-weight: bold;");
    layout->addWidget(m_serverStatusLabel, 2, 1);
    
    // 控制按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    m_startButton = new QPushButton("启动服务器");
    m_stopButton = new QPushButton("停止服务器");
    m_stopButton->setEnabled(false);
    
    connect(m_startButton, &QPushButton::clicked, this, &ServerMainWindow::onStartServerClicked);
    connect(m_stopButton, &QPushButton::clicked, this, &ServerMainWindow::onStopServerClicked);
    
    buttonLayout->addWidget(m_startButton);
    buttonLayout->addWidget(m_stopButton);
    layout->addLayout(buttonLayout, 3, 0, 1, 2);
}

void ServerMainWindow::setupConnectionGroup()
{
    m_connectionGroup = new QGroupBox("客户端连接");
    QVBoxLayout *layout = new QVBoxLayout(m_connectionGroup);

    // 连接数标签
    m_connectionCountLabel = new QLabel("当前连接数: 0");
    layout->addWidget(m_connectionCountLabel);

    // 连接表格
    m_connectionTable = new QTableWidget();
    m_connectionTable->setColumnCount(4);
    QStringList headers = {"连接ID", "客户端地址", "协议", "持续时间"};
    m_connectionTable->setHorizontalHeaderLabels(headers);
    m_connectionTable->horizontalHeader()->setStretchLastSection(true);
    m_connectionTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_connectionTable->setAlternatingRowColors(true);
    layout->addWidget(m_connectionTable);

    // 连接管理按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    m_disconnectClientButton = new QPushButton("断开选中连接");
    m_refreshConnectionsButton = new QPushButton("刷新列表");
    m_disconnectClientButton->setEnabled(false);

    connect(m_connectionTable, &QTableWidget::itemSelectionChanged,
            this, &ServerMainWindow::onConnectionSelectionChanged);
    connect(m_disconnectClientButton, &QPushButton::clicked,
            this, &ServerMainWindow::onDisconnectClientClicked);
    connect(m_refreshConnectionsButton, &QPushButton::clicked,
            this, &ServerMainWindow::onRefreshConnectionsClicked);

    buttonLayout->addWidget(m_disconnectClientButton);
    buttonLayout->addWidget(m_refreshConnectionsButton);
    layout->addLayout(buttonLayout);
}

void ServerMainWindow::setupMessageGroup()
{
    m_messageGroup = new QGroupBox("消息日志");
    QVBoxLayout *layout = new QVBoxLayout(m_messageGroup);

    // 消息显示区域
    m_messageLogEdit = new QTextEdit();
    m_messageLogEdit->setReadOnly(true);
    m_messageLogEdit->setFont(QFont("Consolas", 9));
    layout->addWidget(m_messageLogEdit);

    // 日志控制按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    m_clearLogsButton = new QPushButton("清空日志");
    m_saveLogsButton = new QPushButton("保存日志");

    connect(m_clearLogsButton, &QPushButton::clicked, this, &ServerMainWindow::onClearLogsClicked);
    connect(m_saveLogsButton, &QPushButton::clicked, this, &ServerMainWindow::onSaveLogsClicked);

    buttonLayout->addWidget(m_clearLogsButton);
    buttonLayout->addWidget(m_saveLogsButton);
    buttonLayout->addStretch();
    layout->addLayout(buttonLayout);
}

void ServerMainWindow::setupSendGroup()
{
    m_sendGroup = new QGroupBox("发送消息");
    QVBoxLayout *layout = new QVBoxLayout(m_sendGroup);

    // 目标连接选择
    QHBoxLayout *targetLayout = new QHBoxLayout();
    targetLayout->addWidget(new QLabel("目标:"));
    m_targetConnectionComboBox = new QComboBox();
    m_targetConnectionComboBox->addItem("广播到所有连接", "broadcast");
    targetLayout->addWidget(m_targetConnectionComboBox);
    targetLayout->addStretch();
    layout->addLayout(targetLayout);

    // 消息输入区域
    m_sendTextEdit = new QPlainTextEdit();
    m_sendTextEdit->setPlaceholderText("输入要发送的JSON消息...\n例如:\n{\n  \"type\": \"test\",\n  \"message\": \"Hello World\"\n}");
    m_sendTextEdit->setMaximumHeight(150);
    layout->addWidget(m_sendTextEdit);

    // 发送按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    m_sendMessageButton = new QPushButton("发送到选中连接");
    m_broadcastMessageButton = new QPushButton("广播到所有连接");
    m_clearSendAreaButton = new QPushButton("清空输入");

    connect(m_sendMessageButton, &QPushButton::clicked, this, &ServerMainWindow::onSendMessageClicked);
    connect(m_broadcastMessageButton, &QPushButton::clicked, this, &ServerMainWindow::onBroadcastMessageClicked);
    connect(m_clearSendAreaButton, &QPushButton::clicked, this, &ServerMainWindow::onClearSendAreaClicked);

    buttonLayout->addWidget(m_sendMessageButton);
    buttonLayout->addWidget(m_broadcastMessageButton);
    buttonLayout->addWidget(m_clearSendAreaButton);
    layout->addLayout(buttonLayout);
}

void ServerMainWindow::setupStatisticsGroup()
{
    m_statisticsGroup = new QGroupBox("统计信息");
    QGridLayout *layout = new QGridLayout(m_statisticsGroup);

    layout->addWidget(new QLabel("运行时间:"), 0, 0);
    m_uptimeLabel = new QLabel("未启动");
    layout->addWidget(m_uptimeLabel, 0, 1);

    layout->addWidget(new QLabel("总连接数:"), 1, 0);
    m_totalConnectionsLabel = new QLabel("0");
    layout->addWidget(m_totalConnectionsLabel, 1, 1);

    layout->addWidget(new QLabel("接收消息:"), 2, 0);
    m_messagesReceivedLabel = new QLabel("0");
    layout->addWidget(m_messagesReceivedLabel, 2, 1);

    layout->addWidget(new QLabel("发送消息:"), 3, 0);
    m_messagesSentLabel = new QLabel("0");
    layout->addWidget(m_messagesSentLabel, 3, 1);

    layout->addWidget(new QLabel("接收字节:"), 4, 0);
    m_bytesReceivedLabel = new QLabel("0");
    layout->addWidget(m_bytesReceivedLabel, 4, 1);

    layout->addWidget(new QLabel("发送字节:"), 5, 0);
    m_bytesSentLabel = new QLabel("0");
    layout->addWidget(m_bytesSentLabel, 5, 1);
}

// ========== 槽函数实现 ==========

void ServerMainWindow::onStartServerClicked()
{
    if (m_server->isRunning()) {
        QMessageBox::warning(this, "警告", "服务器已在运行！");
        return;
    }

    // 更新配置
    m_serverConfig.port = static_cast<quint16>(m_portSpinBox->value());
    m_serverConfig.protocolType = static_cast<RWSProtocolHandler::ProtocolType>(
        m_protocolComboBox->currentData().toInt());

    if (m_server->startServer(m_serverConfig)) {
        addLogMessage(QString("服务器启动成功 - 端口: %1, 协议: %2")
                      .arg(m_serverConfig.port)
                      .arg(RWSProtocolHandler::protocolTypeToString(m_serverConfig.protocolType)),
                      "SUCCESS");
    } else {
        QMessageBox::critical(this, "错误", "服务器启动失败！");
        addLogMessage("服务器启动失败", "ERROR");
    }
}

void ServerMainWindow::onStopServerClicked()
{
    if (!m_server->isRunning()) {
        QMessageBox::warning(this, "警告", "服务器未运行！");
        return;
    }

    m_server->stopServer();
    addLogMessage("服务器已停止", "INFO");
}

void ServerMainWindow::onClearLogsClicked()
{
    QMutexLocker locker(&m_logMutex);
    m_messageLogEdit->clear();
    addLogMessage("日志已清空", "SYSTEM");
}

void ServerMainWindow::onSaveLogsClicked()
{
    QString fileName = QFileDialog::getSaveFileName(
        this, "保存日志文件",
        QString("server_log_%1.txt").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
        "文本文件 (*.txt);;所有文件 (*.*)");

    if (!fileName.isEmpty()) {
        saveLogsToFile(fileName);
    }
}

void ServerMainWindow::onSendMessageClicked()
{
    QString messageText = m_sendTextEdit->toPlainText().trimmed();
    if (messageText.isEmpty()) {
        QMessageBox::information(this, "提示", "请输入要发送的消息！");
        return;
    }

    QJsonObject message = parseJsonFromText(messageText);
    if (message.isEmpty()) {
        QMessageBox::warning(this, "格式错误", "JSON格式不正确！");
        return;
    }

    QString target = m_targetConnectionComboBox->currentData().toString();
    if (target == "broadcast") {
        int count = m_server->broadcastMessage(message);
        addLogMessage(QString("广播消息已发送给 %1 个客户端: %2")
                      .arg(count).arg(formatJsonForDisplay(message)), "SEND");
    } else {
        QUuid connectionId = QUuid::fromString(target);
        if (m_server->sendMessageToClient(connectionId, message)) {
            addLogMessage(QString("消息已发送给客户端 %1: %2")
                          .arg(connectionId.toString().left(8))
                          .arg(formatJsonForDisplay(message)), "SEND");
        } else {
            QMessageBox::warning(this, "发送失败", "消息发送失败！");
        }
    }
}

void ServerMainWindow::onBroadcastMessageClicked()
{
    QString messageText = m_sendTextEdit->toPlainText().trimmed();
    if (messageText.isEmpty()) {
        QMessageBox::information(this, "提示", "请输入要发送的消息！");
        return;
    }

    QJsonObject message = parseJsonFromText(messageText);
    if (message.isEmpty()) {
        QMessageBox::warning(this, "格式错误", "JSON格式不正确！");
        return;
    }

    int count = m_server->broadcastMessage(message);
    addLogMessage(QString("广播消息已发送给 %1 个客户端: %2")
                  .arg(count).arg(formatJsonForDisplay(message)), "SEND");
}

void ServerMainWindow::onClearSendAreaClicked()
{
    m_sendTextEdit->clear();
}

void ServerMainWindow::onConnectionSelectionChanged()
{
    bool hasSelection = !m_connectionTable->selectedItems().isEmpty();
    m_disconnectClientButton->setEnabled(hasSelection);
}

void ServerMainWindow::onDisconnectClientClicked()
{
    int currentRow = m_connectionTable->currentRow();
    if (currentRow < 0) {
        return;
    }

    QTableWidgetItem *idItem = m_connectionTable->item(currentRow, 0);
    if (!idItem) {
        return;
    }

    QString connectionIdStr = idItem->text();
    QUuid connectionId = QUuid::fromString(connectionIdStr);

    if (m_server->disconnectClient(connectionId)) {
        addLogMessage(QString("已断开客户端连接: %1").arg(connectionIdStr.left(8)), "INFO");
    }
}

void ServerMainWindow::onRefreshConnectionsClicked()
{
    updateConnectionList();
}

// ========== 服务器事件处理 ==========

void ServerMainWindow::onServerStarted(quint16 port)
{
    updateServerStatus();
    m_statusLabel->setText(QString("服务器运行中 - 端口: %1").arg(port));
    addLogMessage(QString("服务器已在端口 %1 上启动").arg(port), "SUCCESS");
}

void ServerMainWindow::onServerStopped()
{
    updateServerStatus();
    m_statusLabel->setText("服务器已停止");
    addLogMessage("服务器已停止", "INFO");
}

void ServerMainWindow::onClientConnected(const QUuid &id, const QString &address)
{
    updateConnectionList();
    addLogMessage(QString("新客户端连接: %1 (%2)").arg(address, id.toString().left(8)), "CONNECT");
}

void ServerMainWindow::onClientDisconnected(const QUuid &id)
{
    updateConnectionList();
    addLogMessage(QString("客户端断开连接: %1").arg(id.toString().left(8)), "DISCONNECT");
}

void ServerMainWindow::onJsonCommandReceived(const QUuid &connectionId, const QJsonObject &command)
{
    QString message = QString("接收到JSON命令 [%1]: %2")
                      .arg(connectionId.toString().left(8))
                      .arg(formatJsonForDisplay(command));
    addReceivedMessage(connectionId, message, "JSON");
}

void ServerMainWindow::onRawDataReceived(const QUuid &connectionId, const QByteArray &data)
{
    QString message = QString("接收到原始数据 [%1]: %2 字节")
                      .arg(connectionId.toString().left(8))
                      .arg(data.size());
    addReceivedMessage(connectionId, message, "RAW");
}

// ========== 定时更新 ==========

void ServerMainWindow::updateStatistics()
{
    if (!m_server) {
        return;
    }

    QJsonObject stats = m_server->getServerStatistics();

    if (m_server->isRunning()) {
        qint64 uptime = stats.value("uptimeSeconds").toInt();
        int hours = uptime / 3600;
        int minutes = (uptime % 3600) / 60;
        int seconds = uptime % 60;
        m_uptimeLabel->setText(QString("%1:%2:%3")
                               .arg(hours, 2, 10, QChar('0'))
                               .arg(minutes, 2, 10, QChar('0'))
                               .arg(seconds, 2, 10, QChar('0')));
    } else {
        m_uptimeLabel->setText("未启动");
    }

    m_totalConnectionsLabel->setText(QString::number(stats.value("totalConnections").toInt()));
    m_messagesReceivedLabel->setText(QString::number(stats.value("totalMessagesReceived").toInt()));
    m_messagesSentLabel->setText(QString::number(stats.value("totalMessagesSent").toInt()));
    m_bytesReceivedLabel->setText(QString::number(stats.value("totalBytesReceived").toInt()));
    m_bytesSentLabel->setText(QString::number(stats.value("totalBytesSent").toInt()));
}

void ServerMainWindow::updateConnectionList()
{
    if (!m_server) {
        return;
    }

    QJsonArray connections = m_server->getConnectionsStatistics();

    m_connectionTable->setRowCount(connections.size());
    m_connectionCountLabel->setText(QString("当前连接数: %1").arg(connections.size()));

    // 更新目标连接下拉框
    QString currentTarget = m_targetConnectionComboBox->currentData().toString();
    m_targetConnectionComboBox->clear();
    m_targetConnectionComboBox->addItem("广播到所有连接", "broadcast");

    for (int i = 0; i < connections.size(); ++i) {
        QJsonObject conn = connections[i].toObject();
        QString connectionId = conn.value("connectionId").toString();
        QString clientAddress = conn.value("clientAddress").toString();
        QString protocolType = conn.value("protocolType").toString();
        qint64 duration = conn.value("connectionDuration").toInt();

        // 格式化持续时间
        QString durationStr;
        if (duration < 60) {
            durationStr = QString("%1秒").arg(duration);
        } else if (duration < 3600) {
            durationStr = QString("%1分%2秒").arg(duration / 60).arg(duration % 60);
        } else {
            durationStr = QString("%1时%2分").arg(duration / 3600).arg((duration % 3600) / 60);
        }

        m_connectionTable->setItem(i, 0, new QTableWidgetItem(connectionId));
        m_connectionTable->setItem(i, 1, new QTableWidgetItem(clientAddress));
        m_connectionTable->setItem(i, 2, new QTableWidgetItem(protocolType));
        m_connectionTable->setItem(i, 3, new QTableWidgetItem(durationStr));

        // 添加到目标选择下拉框
        m_targetConnectionComboBox->addItem(
            QString("%1 (%2)").arg(clientAddress, connectionId.left(8)),
            connectionId);
    }

    // 恢复之前的选择
    if (!currentTarget.isEmpty()) {
        int index = m_targetConnectionComboBox->findData(currentTarget);
        if (index >= 0) {
            m_targetConnectionComboBox->setCurrentIndex(index);
        }
    }
}

// ========== UI更新方法 ==========

void ServerMainWindow::updateServerStatus()
{
    bool isRunning = m_server && m_server->isRunning();

    m_startButton->setEnabled(!isRunning);
    m_stopButton->setEnabled(isRunning);
    m_portSpinBox->setEnabled(!isRunning);
    m_protocolComboBox->setEnabled(!isRunning);

    if (isRunning) {
        m_serverStatusLabel->setText("运行中");
        m_serverStatusLabel->setStyleSheet("color: green; font-weight: bold;");
    } else {
        m_serverStatusLabel->setText("已停止");
        m_serverStatusLabel->setStyleSheet("color: red; font-weight: bold;");
    }
}

void ServerMainWindow::addLogMessage(const QString &message, const QString &type)
{
    QMutexLocker locker(&m_logMutex);

    QString timestamp = formatTimestamp();
    QString coloredMessage;

    if (type == "ERROR") {
        coloredMessage = QString("<span style='color: red;'>[%1] [%2] %3</span>")
                         .arg(timestamp, type, message);
    } else if (type == "SUCCESS") {
        coloredMessage = QString("<span style='color: green;'>[%1] [%2] %3</span>")
                         .arg(timestamp, type, message);
    } else if (type == "CONNECT") {
        coloredMessage = QString("<span style='color: blue;'>[%1] [%2] %3</span>")
                         .arg(timestamp, type, message);
    } else if (type == "DISCONNECT") {
        coloredMessage = QString("<span style='color: orange;'>[%1] [%2] %3</span>")
                         .arg(timestamp, type, message);
    } else if (type == "SEND") {
        coloredMessage = QString("<span style='color: purple;'>[%1] [%2] %3</span>")
                         .arg(timestamp, type, message);
    } else {
        coloredMessage = QString("[%1] [%2] %3").arg(timestamp, type, message);
    }

    m_messageLogEdit->append(coloredMessage);

    // 限制日志行数
    QTextDocument *doc = m_messageLogEdit->document();
    if (doc->blockCount() > MAX_LOG_LINES) {
        QTextCursor cursor = m_messageLogEdit->textCursor();
        cursor.movePosition(QTextCursor::Start);
        cursor.movePosition(QTextCursor::Down, QTextCursor::KeepAnchor,
                           doc->blockCount() - MAX_LOG_LINES);
        cursor.removeSelectedText();
    }

    // 滚动到底部
    m_messageLogEdit->moveCursor(QTextCursor::End);
}

void ServerMainWindow::addReceivedMessage(const QUuid &connectionId, const QString &message, const QString &type)
{
    Q_UNUSED(connectionId)
    addLogMessage(message, type);
}

// ========== 工具方法 ==========

QString ServerMainWindow::formatJsonForDisplay(const QJsonObject &json)
{
    QJsonDocument doc(json);
    return doc.toJson(QJsonDocument::Compact);
}

QString ServerMainWindow::formatTimestamp(const QDateTime &dateTime)
{
    return dateTime.toString("hh:mm:ss.zzz");
}

QJsonObject ServerMainWindow::parseJsonFromText(const QString &text)
{
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(text.toUtf8(), &error);

    if (error.error != QJsonParseError::NoError || !doc.isObject()) {
        return QJsonObject();
    }

    return doc.object();
}

void ServerMainWindow::saveLogsToFile(const QString &fileName)
{
    QFile file(fileName);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out << m_messageLogEdit->toPlainText();
        file.close();

        QMessageBox::information(this, "保存成功",
                                QString("日志已保存到: %1").arg(fileName));
    } else {
        QMessageBox::critical(this, "保存失败",
                             QString("无法保存日志文件: %1").arg(file.errorString()));
    }
}
