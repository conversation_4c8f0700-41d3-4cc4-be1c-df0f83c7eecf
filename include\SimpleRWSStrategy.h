#ifndef SIMPLE_RWS_STRATEGY_H
#define SIMPLE_RWS_STRATEGY_H

#include "IRWSStrategy.h"
#include "RWSConfig.h"
#include <QByteArray>

/**
 * @brief 简化版简单读写策略类
 */
class SimpleRWSStrategy : public IRWSStrategy
{
public:
    /**
     * @brief 构造函数
     */
    explicit SimpleRWSStrategy(const SimpleConfig &config = SimpleConfig());

    /**
     * @brief 析构函数
     */
    ~SimpleRWSStrategy() override;

    // ========== IRWSStrategy接口实现 ==========

    QByteArray wrapMessage(const QByteArray &message) override;
    QList<QByteArray> processReceivedData(const QByteArray &newData) override;

    void clearBuffer() override;
    QString strategyName() const override { return "SimpleRWS"; }
    StrategyType strategyType() const override { return Simple; }

private:
    /**
     * @brief 分块处理数据
     */
    QList<QByteArray> chunkData(const QByteArray &data);

private:
    SimpleConfig m_config;                      // 策略配置
    QByteArray m_receiveBuffer;                 // 接收缓冲区
};

#endif // SIMPLE_RWS_STRATEGY_H
