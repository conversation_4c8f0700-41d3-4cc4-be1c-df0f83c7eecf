/****************************************************************************
** Meta object code from reading C++ file 'VarListEditor.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../include/mvc/VarListEditor.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'VarListEditor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_VarListEditor_t {
    QByteArrayData data[18];
    char stringdata0[313];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_VarListEditor_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_VarListEditor_t qt_meta_stringdata_VarListEditor = {
    {
QT_MOC_LITERAL(0, 0, 13), // "VarListEditor"
QT_MOC_LITERAL(1, 14, 11), // "listChanged"
QT_MOC_LITERAL(2, 26, 0), // ""
QT_MOC_LITERAL(3, 27, 16), // "itemCountChanged"
QT_MOC_LITERAL(4, 44, 5), // "count"
QT_MOC_LITERAL(5, 50, 18), // "onAddButtonClicked"
QT_MOC_LITERAL(6, 69, 21), // "onDeleteButtonClicked"
QT_MOC_LITERAL(7, 91, 21), // "onMoveUpButtonClicked"
QT_MOC_LITERAL(8, 113, 23), // "onMoveDownButtonClicked"
QT_MOC_LITERAL(9, 137, 19), // "onEditButtonClicked"
QT_MOC_LITERAL(10, 157, 20), // "onClearButtonClicked"
QT_MOC_LITERAL(11, 178, 23), // "onQuickAddButtonClicked"
QT_MOC_LITERAL(12, 202, 23), // "onListItemDoubleClicked"
QT_MOC_LITERAL(13, 226, 16), // "QListWidgetItem*"
QT_MOC_LITERAL(14, 243, 4), // "item"
QT_MOC_LITERAL(15, 248, 22), // "onListSelectionChanged"
QT_MOC_LITERAL(16, 271, 17), // "onListItemChanged"
QT_MOC_LITERAL(17, 289, 23) // "onQuickAddReturnPressed"

    },
    "VarListEditor\0listChanged\0\0itemCountChanged\0"
    "count\0onAddButtonClicked\0onDeleteButtonClicked\0"
    "onMoveUpButtonClicked\0onMoveDownButtonClicked\0"
    "onEditButtonClicked\0onClearButtonClicked\0"
    "onQuickAddButtonClicked\0onListItemDoubleClicked\0"
    "QListWidgetItem*\0item\0onListSelectionChanged\0"
    "onListItemChanged\0onQuickAddReturnPressed"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_VarListEditor[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      13,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   79,    2, 0x06 /* Public */,
       3,    1,   80,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       5,    0,   83,    2, 0x08 /* Private */,
       6,    0,   84,    2, 0x08 /* Private */,
       7,    0,   85,    2, 0x08 /* Private */,
       8,    0,   86,    2, 0x08 /* Private */,
       9,    0,   87,    2, 0x08 /* Private */,
      10,    0,   88,    2, 0x08 /* Private */,
      11,    0,   89,    2, 0x08 /* Private */,
      12,    1,   90,    2, 0x08 /* Private */,
      15,    0,   93,    2, 0x08 /* Private */,
      16,    1,   94,    2, 0x08 /* Private */,
      17,    0,   97,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    4,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 13,   14,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 13,   14,
    QMetaType::Void,

       0        // eod
};

void VarListEditor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<VarListEditor *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->listChanged(); break;
        case 1: _t->itemCountChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 2: _t->onAddButtonClicked(); break;
        case 3: _t->onDeleteButtonClicked(); break;
        case 4: _t->onMoveUpButtonClicked(); break;
        case 5: _t->onMoveDownButtonClicked(); break;
        case 6: _t->onEditButtonClicked(); break;
        case 7: _t->onClearButtonClicked(); break;
        case 8: _t->onQuickAddButtonClicked(); break;
        case 9: _t->onListItemDoubleClicked((*reinterpret_cast< QListWidgetItem*(*)>(_a[1]))); break;
        case 10: _t->onListSelectionChanged(); break;
        case 11: _t->onListItemChanged((*reinterpret_cast< QListWidgetItem*(*)>(_a[1]))); break;
        case 12: _t->onQuickAddReturnPressed(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (VarListEditor::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarListEditor::listChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (VarListEditor::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarListEditor::itemCountChanged)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject VarListEditor::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_VarListEditor.data,
    qt_meta_data_VarListEditor,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *VarListEditor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *VarListEditor::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_VarListEditor.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int VarListEditor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 13)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 13)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 13;
    }
    return _id;
}

// SIGNAL 0
void VarListEditor::listChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void VarListEditor::itemCountChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
