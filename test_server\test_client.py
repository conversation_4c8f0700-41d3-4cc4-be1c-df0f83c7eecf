#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CommandTransceiver 测试服务器的简单测试客户端

用于验证测试服务器的基本功能
"""

import socket
import json
import struct
import time
import threading
import sys
import zlib
from typing import Optional, List

class TestClient:
    """简单的测试客户端"""
    
    def __init__(self, host: str = "localhost", port: int = 8080, protocol: str = "prelength", enable_compression: bool = False):
        self.host = host
        self.port = port
        self.protocol = protocol.lower()
        self.enable_compression = enable_compression
        self.socket: Optional[socket.socket] = None
        self.connected = False
        self.receive_thread: Optional[threading.Thread] = None
        self.running = False
        
    def connect(self) -> bool:
        """连接到服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            self.connected = True
            self.running = True
            
            # 启动接收线程
            self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
            self.receive_thread.start()
            
            print(f"✓ 已连接到 {self.host}:{self.port} (协议: {self.protocol})")
            return True
            
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        self.running = False
        self.connected = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
            
        print("✓ 已断开连接")

    def qt_compress(self, data: bytes) -> bytes:
        """模拟 Qt 的 qCompress 函数（CommandTransceiver 格式）"""
        if not self.enable_compression:
            return data

        # Qt qCompress 格式：4字节原始长度（大端序）+ zlib压缩数据
        compressed = zlib.compress(data)
        return struct.pack('>I', len(data)) + compressed

    def simulate_commandtransceiver_format(self, json_data: dict) -> bytes:
        """模拟 CommandTransceiver 的数据格式：JSON -> toBinaryData -> qCompress"""
        if not self.enable_compression:
            # 不压缩时，模拟 QJsonDocument::toBinaryData()
            # 这里简化为 JSON 字符串的 UTF-8 编码
            json_str = json.dumps(json_data, ensure_ascii=False)
            return json_str.encode('utf-8')

        # 模拟 QJsonDocument::toBinaryData() 的输出
        # 实际上 Qt 的二进制格式比较复杂，这里简化处理
        json_str = json.dumps(json_data, ensure_ascii=False)
        binary_data = json_str.encode('utf-8')

        # 使用 qCompress 压缩
        return self.qt_compress(binary_data)

    def qt_uncompress(self, data: bytes) -> Optional[bytes]:
        """模拟 Qt 的 qUncompress 函数"""
        if len(data) < 4:
            return None

        try:
            orig_len = struct.unpack('>I', data[:4])[0]
            uncompressed = zlib.decompress(data[4:])
            if len(uncompressed) == orig_len:
                return uncompressed
        except:
            pass
        return None

    def send_json(self, data: dict) -> bool:
        """发送JSON数据"""
        if not self.connected or not self.socket:
            print("✗ 未连接到服务器")
            return False
            
        try:
            # 使用 CommandTransceiver 格式处理数据
            processed_data = self.simulate_commandtransceiver_format(data)

            # 显示压缩信息
            if self.enable_compression:
                json_str = json.dumps(data, ensure_ascii=False)
                original_size = len(json_str.encode('utf-8'))
                compressed_size = len(processed_data)
                compression_ratio = (1.0 - compressed_size / original_size) * 100
                print(f"  CommandTransceiver 格式压缩: {original_size} → {compressed_size} 字节 (压缩率: {compression_ratio:.1f}%)")

            # 根据协议封装数据
            if self.protocol == "prelength":
                # 4字节长度头 + 数据
                length = len(processed_data)
                packet = struct.pack('>I', length) + processed_data
            elif self.protocol == "endwithnewline":
                # 数据 + 换行符
                packet = processed_data + b'\n'
            else:  # simple
                # 直接发送
                packet = processed_data

            self.socket.send(packet)
            json_str = json.dumps(data, ensure_ascii=False)
            print(f"→ 发送: {json_str}")
            return True
            
        except Exception as e:
            print(f"✗ 发送失败: {e}")
            return False
    
    def send_raw(self, data: bytes) -> bool:
        """发送原始数据"""
        if not self.connected or not self.socket:
            print("✗ 未连接到服务器")
            return False
            
        try:
            # 根据协议封装数据
            if self.protocol == "prelength":
                length = len(data)
                packet = struct.pack('>I', length) + data
            elif self.protocol == "endwithnewline":
                packet = data + b'\n'
            else:  # simple
                packet = data
                
            self.socket.send(packet)
            print(f"→ 发送原始数据: {len(data)} 字节")
            return True
            
        except Exception as e:
            print(f"✗ 发送失败: {e}")
            return False
    
    def _receive_loop(self):
        """接收数据循环"""
        buffer = b''
        
        while self.running and self.connected:
            try:
                data = self.socket.recv(4096)
                if not data:
                    break
                    
                buffer += data
                
                # 根据协议解析数据
                messages = self._parse_messages(buffer)
                for msg in messages:
                    self._handle_message(msg)
                    
            except Exception as e:
                if self.running:
                    print(f"✗ 接收数据错误: {e}")
                break
        
        self.connected = False
    
    def _parse_messages(self, buffer: bytes) -> List[bytes]:
        """解析消息"""
        messages = []
        
        if self.protocol == "prelength":
            while len(buffer) >= 4:
                # 读取长度头
                length = struct.unpack('>I', buffer[:4])[0]
                
                if len(buffer) < 4 + length:
                    break  # 数据不完整
                    
                # 提取消息
                message = buffer[4:4+length]
                messages.append(message)
                buffer = buffer[4+length:]
                
        elif self.protocol == "endwithnewline":
            while b'\n' in buffer:
                pos = buffer.find(b'\n')
                message = buffer[:pos]
                messages.append(message)
                buffer = buffer[pos+1:]
                
        else:  # simple
            if buffer:
                messages.append(buffer)
                buffer = b''
        
        return messages
    
    def _handle_message(self, message: bytes):
        """处理接收到的消息"""
        try:
            # 尝试解析为JSON
            text = message.decode('utf-8')
            data = json.loads(text)
            print(f"← 接收JSON: {json.dumps(data, ensure_ascii=False, indent=2)}")
        except:
            # 原始数据
            print(f"← 接收原始数据: {len(message)} 字节")

def run_basic_tests(client: TestClient):
    """运行基本测试"""
    print("\n========== 基本功能测试 ==========")
    
    # 测试1: Ping命令
    print("\n1. 测试 Ping 命令")
    client.send_json({
        "type": "ping",
        "id": "test-ping-001"
    })
    time.sleep(1)
    
    # 测试2: Echo命令
    print("\n2. 测试 Echo 命令")
    client.send_json({
        "type": "echo",
        "message": "Hello, Test Server!",
        "id": "test-echo-001"
    })
    time.sleep(1)
    
    # 测试3: 服务器状态查询
    print("\n3. 测试服务器状态查询")
    client.send_json({
        "type": "server_status"
    })
    time.sleep(1)
    
    # 测试4: 连接列表查询
    print("\n4. 测试连接列表查询")
    client.send_json({
        "type": "connection_list"
    })
    time.sleep(1)
    
    # 测试5: 心跳测试
    print("\n5. 测试心跳")
    client.send_json({
        "type": "heartbeat",
        "id": "heartbeat-001"
    })
    time.sleep(1)
    
    # 测试6: 压缩统计查询
    print("\n6. 测试压缩统计查询")
    client.send_json({
        "type": "compression_stats",
        "id": "compression-stats-001"
    })
    time.sleep(1)

    # 测试7: 自定义测试命令
    print("\n7. 测试自定义命令")
    client.send_json({
        "type": "test",
        "data": {
            "test_number": 123,
            "test_string": "测试数据",
            "test_array": [1, 2, 3]
        },
        "id": "test-custom-001"
    })
    time.sleep(1)

def run_compression_test(port: int = 8080):
    """运行压缩功能测试"""
    print("\n========== 压缩功能测试 ==========")

    # 测试压缩功能
    client = TestClient(protocol="prelength", port=port, enable_compression=True)

    if not client.connect():
        return False

    try:
        # 发送大量数据测试压缩效果
        large_data = {
            "type": "test_compression",
            "data": {
                "large_text": "这是一个很长的测试文本，用于测试压缩功能。" * 100,
                "repeated_data": ["重复数据"] * 50,
                "numbers": list(range(1000))
            },
            "id": "compression-test-001"
        }

        print("\n发送大数据包测试压缩...")
        client.send_json(large_data)
        time.sleep(2)

        # 查询压缩统计
        print("\n查询压缩统计...")
        client.send_json({
            "type": "compression_stats",
            "id": "compression-stats-query"
        })
        time.sleep(1)

        return True
    finally:
        client.disconnect()

def run_protocol_test(protocol: str, port: int = 8080):
    """运行协议测试"""
    print(f"\n========== {protocol.upper()} 协议测试 ==========")

    client = TestClient(protocol=protocol, port=port)
    
    if not client.connect():
        return False
    
    try:
        run_basic_tests(client)
        
        # 原始数据测试
        print(f"\n7. 测试原始数据传输")
        client.send_raw(b"Raw data test for " + protocol.encode())
        time.sleep(1)
        
        print(f"\n{protocol.upper()} 协议测试完成")
        
    finally:
        client.disconnect()
        time.sleep(1)
    
    return True

def main():
    """主函数"""
    print("CommandTransceiver 测试服务器 - 客户端测试工具")
    print("=" * 50)
    
    # 检查命令行参数
    host = "localhost"
    port = 8080
    
    if len(sys.argv) > 1:
        port = int(sys.argv[1])
    if len(sys.argv) > 2:
        host = sys.argv[2]
    
    print(f"目标服务器: {host}:{port}")
    
    # 测试压缩功能
    try:
        if not run_compression_test(port):
            print("✗ 压缩功能测试失败")
        else:
            print("✓ 压缩功能测试成功")
    except Exception as e:
        print(f"✗ 压缩功能测试异常: {e}")

    # 测试所有协议
    protocols = ["prelength", "endwithnewline", "simple"]

    for protocol in protocols:
        try:
            if not run_protocol_test(protocol, port):
                print(f"✗ {protocol} 协议测试失败")
            else:
                print(f"✓ {protocol} 协议测试成功")
        except KeyboardInterrupt:
            print("\n测试被用户中断")
            break
        except Exception as e:
            print(f"✗ {protocol} 协议测试异常: {e}")
    
    print("\n所有测试完成")

if __name__ == "__main__":
    main()
