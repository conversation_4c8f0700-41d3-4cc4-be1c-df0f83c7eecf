@echo off
REM 快速编译测试脚本

echo 测试编译 CommandTransceiver 测试服务器...

REM 检查 qmake
where qmake >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 qmake
    exit /b 1
)

REM 清理之前的构建
if exist Makefile del Makefile
if exist Makefile.Debug del Makefile.Debug
if exist Makefile.Release del Makefile.Release

echo 生成 Makefile...
qmake test_server.pro
if %errorlevel% neq 0 (
    echo qmake 失败
    exit /b 1
)

echo 开始编译...
REM 尝试不同的编译器
where nmake >nul 2>nul
if %errorlevel% equ 0 (
    nmake debug
) else (
    where mingw32-make >nul 2>nul
    if %errorlevel% equ 0 (
        mingw32-make debug
    ) else (
        where make >nul 2>nul
        if %errorlevel% equ 0 (
            make debug
        ) else (
            echo 未找到合适的编译工具
            exit /b 1
        )
    )
)

if %errorlevel% equ 0 (
    echo 编译成功！
) else (
    echo 编译失败！
    exit /b 1
)
