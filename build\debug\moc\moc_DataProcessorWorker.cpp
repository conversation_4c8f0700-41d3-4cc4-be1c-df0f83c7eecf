/****************************************************************************
** Meta object code from reading C++ file 'DataProcessorWorker.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../include/DataProcessorWorker.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'DataProcessorWorker.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_DataProcessorWorker_t {
    QByteArrayData data[29];
    char stringdata0[329];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_DataProcessorWorker_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_DataProcessorWorker_t qt_meta_stringdata_DataProcessorWorker = {
    {
QT_MOC_LITERAL(0, 0, 19), // "DataProcessorWorker"
QT_MOC_LITERAL(1, 20, 11), // "initialized"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 16), // "cleanupCompleted"
QT_MOC_LITERAL(4, 50, 15), // "commandReceived"
QT_MOC_LITERAL(5, 66, 7), // "command"
QT_MOC_LITERAL(6, 74, 4), // "uuid"
QT_MOC_LITERAL(7, 79, 13), // "replyReceived"
QT_MOC_LITERAL(8, 93, 5), // "reply"
QT_MOC_LITERAL(9, 99, 11), // "jsonEncoded"
QT_MOC_LITERAL(10, 111, 11), // "encodedData"
QT_MOC_LITERAL(11, 123, 12), // "originalJson"
QT_MOC_LITERAL(12, 136, 11), // "dataDecoded"
QT_MOC_LITERAL(13, 148, 11), // "decodedJson"
QT_MOC_LITERAL(14, 160, 12), // "originalData"
QT_MOC_LITERAL(15, 173, 15), // "processingError"
QT_MOC_LITERAL(16, 189, 12), // "ProcessError"
QT_MOC_LITERAL(17, 202, 5), // "error"
QT_MOC_LITERAL(18, 208, 11), // "errorString"
QT_MOC_LITERAL(19, 220, 13), // "encodingError"
QT_MOC_LITERAL(20, 234, 13), // "decodingError"
QT_MOC_LITERAL(21, 248, 10), // "initialize"
QT_MOC_LITERAL(22, 259, 7), // "cleanup"
QT_MOC_LITERAL(23, 267, 14), // "processRawData"
QT_MOC_LITERAL(24, 282, 7), // "rawData"
QT_MOC_LITERAL(25, 290, 14), // "encodeJsonData"
QT_MOC_LITERAL(26, 305, 4), // "json"
QT_MOC_LITERAL(27, 310, 13), // "decodeRawData"
QT_MOC_LITERAL(28, 324, 4) // "stop"

    },
    "DataProcessorWorker\0initialized\0\0"
    "cleanupCompleted\0commandReceived\0"
    "command\0uuid\0replyReceived\0reply\0"
    "jsonEncoded\0encodedData\0originalJson\0"
    "dataDecoded\0decodedJson\0originalData\0"
    "processingError\0ProcessError\0error\0"
    "errorString\0encodingError\0decodingError\0"
    "initialize\0cleanup\0processRawData\0"
    "rawData\0encodeJsonData\0json\0decodeRawData\0"
    "stop"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_DataProcessorWorker[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      15,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       9,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   89,    2, 0x06 /* Public */,
       3,    0,   90,    2, 0x06 /* Public */,
       4,    2,   91,    2, 0x06 /* Public */,
       7,    2,   96,    2, 0x06 /* Public */,
       9,    2,  101,    2, 0x06 /* Public */,
      12,    2,  106,    2, 0x06 /* Public */,
      15,    2,  111,    2, 0x06 /* Public */,
      19,    3,  116,    2, 0x06 /* Public */,
      20,    3,  123,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      21,    0,  130,    2, 0x0a /* Public */,
      22,    0,  131,    2, 0x0a /* Public */,
      23,    1,  132,    2, 0x0a /* Public */,
      25,    1,  135,    2, 0x0a /* Public */,
      27,    1,  138,    2, 0x0a /* Public */,
      28,    0,  141,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QJsonObject, QMetaType::QUuid,    5,    6,
    QMetaType::Void, QMetaType::QJsonObject, QMetaType::QUuid,    8,    6,
    QMetaType::Void, QMetaType::QByteArray, QMetaType::QJsonObject,   10,   11,
    QMetaType::Void, QMetaType::QJsonObject, QMetaType::QByteArray,   13,   14,
    QMetaType::Void, 0x80000000 | 16, QMetaType::QString,   17,   18,
    QMetaType::Void, 0x80000000 | 16, QMetaType::QString, QMetaType::QJsonObject,   17,   18,   11,
    QMetaType::Void, 0x80000000 | 16, QMetaType::QString, QMetaType::QByteArray,   17,   18,   14,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QByteArray,   24,
    QMetaType::Void, QMetaType::QJsonObject,   26,
    QMetaType::Void, QMetaType::QByteArray,   24,
    QMetaType::Void,

       0        // eod
};

void DataProcessorWorker::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<DataProcessorWorker *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->initialized(); break;
        case 1: _t->cleanupCompleted(); break;
        case 2: _t->commandReceived((*reinterpret_cast< const QJsonObject(*)>(_a[1])),(*reinterpret_cast< const QUuid(*)>(_a[2]))); break;
        case 3: _t->replyReceived((*reinterpret_cast< const QJsonObject(*)>(_a[1])),(*reinterpret_cast< const QUuid(*)>(_a[2]))); break;
        case 4: _t->jsonEncoded((*reinterpret_cast< const QByteArray(*)>(_a[1])),(*reinterpret_cast< const QJsonObject(*)>(_a[2]))); break;
        case 5: _t->dataDecoded((*reinterpret_cast< const QJsonObject(*)>(_a[1])),(*reinterpret_cast< const QByteArray(*)>(_a[2]))); break;
        case 6: _t->processingError((*reinterpret_cast< ProcessError(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 7: _t->encodingError((*reinterpret_cast< ProcessError(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< const QJsonObject(*)>(_a[3]))); break;
        case 8: _t->decodingError((*reinterpret_cast< ProcessError(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< const QByteArray(*)>(_a[3]))); break;
        case 9: _t->initialize(); break;
        case 10: _t->cleanup(); break;
        case 11: _t->processRawData((*reinterpret_cast< const QByteArray(*)>(_a[1]))); break;
        case 12: _t->encodeJsonData((*reinterpret_cast< const QJsonObject(*)>(_a[1]))); break;
        case 13: _t->decodeRawData((*reinterpret_cast< const QByteArray(*)>(_a[1]))); break;
        case 14: _t->stop(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (DataProcessorWorker::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DataProcessorWorker::initialized)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (DataProcessorWorker::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DataProcessorWorker::cleanupCompleted)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (DataProcessorWorker::*)(const QJsonObject & , const QUuid & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DataProcessorWorker::commandReceived)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (DataProcessorWorker::*)(const QJsonObject & , const QUuid & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DataProcessorWorker::replyReceived)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (DataProcessorWorker::*)(const QByteArray & , const QJsonObject & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DataProcessorWorker::jsonEncoded)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (DataProcessorWorker::*)(const QJsonObject & , const QByteArray & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DataProcessorWorker::dataDecoded)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (DataProcessorWorker::*)(ProcessError , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DataProcessorWorker::processingError)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (DataProcessorWorker::*)(ProcessError , const QString & , const QJsonObject & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DataProcessorWorker::encodingError)) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (DataProcessorWorker::*)(ProcessError , const QString & , const QByteArray & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DataProcessorWorker::decodingError)) {
                *result = 8;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject DataProcessorWorker::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_DataProcessorWorker.data,
    qt_meta_data_DataProcessorWorker,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *DataProcessorWorker::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *DataProcessorWorker::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_DataProcessorWorker.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int DataProcessorWorker::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 15)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 15;
    }
    return _id;
}

// SIGNAL 0
void DataProcessorWorker::initialized()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void DataProcessorWorker::cleanupCompleted()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void DataProcessorWorker::commandReceived(const QJsonObject & _t1, const QUuid & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void DataProcessorWorker::replyReceived(const QJsonObject & _t1, const QUuid & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void DataProcessorWorker::jsonEncoded(const QByteArray & _t1, const QJsonObject & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void DataProcessorWorker::dataDecoded(const QJsonObject & _t1, const QByteArray & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void DataProcessorWorker::processingError(ProcessError _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void DataProcessorWorker::encodingError(ProcessError _t1, const QString & _t2, const QJsonObject & _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}

// SIGNAL 8
void DataProcessorWorker::decodingError(ProcessError _t1, const QString & _t2, const QByteArray & _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 8, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
