#ifndef VAR_TBL_CONTROLLER_H
#define VAR_TBL_CONTROLLER_H

#include <QObject>
#include <QTimer>
#include <QMutex>
#include <QHash>
#include <QUuid>
#include <QJsonObject>
#include <functional>

// 引入相关组件
#include "VarTblModel.h"
#include "../CommandTransceiverRefactored.h"

// 引入ada3数据结构
#include "cmd/vartbl.h"
#include "cmd/var.h"
#include "cmd/varindex.h"
#include "cmd/varupdate.h"
#include "cmd/varremove.h"
// 注意：cmd/varadd.h 已被ada3移除，使用cmd/varupdate.h替代

/**
 * @brief VarTbl控制器 - 业务逻辑层
 * 
 * 设计原则：
 * - 处理所有业务逻辑和用户操作
 * - 负责与后台的数据同步
 * - 使用CommandTransceiverRefactored进行通信
 * - 操作VarTblModel进行数据更新
 * - 不直接操作UI，通过信号与View通信
 */
class VarTblController : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 同步状态枚举（内部使用）
     */
    enum class SyncStatus {
        Idle,              // 空闲
        Syncing,           // 同步中
        Error              // 错误
    };

    /**
     * @brief 操作结果枚举
     */
    enum class OperationResult {
        Success,           // 成功
        Failed,            // 失败
        Timeout,           // 超时
        NetworkError,      // 网络错误
        ServerError        // 服务器错误
    };

    explicit VarTblController(QObject *parent = nullptr);
    ~VarTblController() override;

    // ========== 组件设置 ==========
    
    /**
     * @brief 设置数据模型
     * @param model VarTbl数据模型
     */
    void setModel(VarTblModel *model);
    
    /**
     * @brief 设置通信组件
     * @param transceiver CommandTransceiver实例
     */
    void setCommandTransceiver(CommandTransceiverRefactored *transceiver);
    
    /**
     * @brief 设置项目UUID（用于VarIndex）
     * @param projectUuid 项目UUID
     */
    void setProjectUuid(const QUuid &projectUuid);

    // ========== 数据操作接口 ==========

    /**
     * @brief 添加变量
     * @param id 变量ID
     * @param var 变量对象
     * @param label 变量标签
     * @param syncToServer 是否同步到服务器
     */
    void addVariable(int id, const cmd::Var &var, const QString &label, bool syncToServer = true);

    /**
     * @brief 更新变量
     * @param id 变量ID
     * @param var 新的变量对象
     * @param label 新的变量标签
     * @param syncToServer 是否同步到服务器
     */
    void updateVariable(int id, const cmd::Var &var, const QString &label, bool syncToServer = true);

    // ========== View数据处理接口（处理来自View的原始数据） ==========

    /**
     * @brief 处理来自View的添加变量请求
     * @param idText 变量ID文本（需要验证和转换）
     * @param label 变量标签
     * @param typeText 变量类型文本
     * @param value 变量值（原始QVariant）
     * @return 操作是否成功
     */
    bool handleAddVariable(const QString &idText, const QString &label, const QString &typeText, const QVariant &value);

    /**
     * @brief 处理来自View的更新变量请求
     * @param originalId 原始变量ID
     * @param idText 新的变量ID文本（需要验证和转换）
     * @param label 变量标签
     * @param typeText 变量类型文本
     * @param value 变量值（原始QVariant）
     * @return 操作是否成功
     */
    bool handleUpdateVariable(int originalId, const QString &idText, const QString &label, const QString &typeText, const QVariant &value);
    
    /**
     * @brief 删除变量
     * @param id 变量ID
     * @param syncToServer 是否同步到服务器
     */
    void removeVariable(int id, bool syncToServer = true);
    
    /**
     * @brief 批量更新变量
     * @param varTbl 新的VarTbl数据
     * @param syncToServer 是否同步到服务器
     */
    void updateVariables(const cmd::VarTbl &varTbl, bool syncToServer = true);

    // ========== 数据同步接口 ==========
    
    /**
     * @brief 从服务器刷新数据
     */
    void refreshFromServer();
    
    /**
     * @brief 同步本地数据到服务器
     */
    void syncToServer();
    
    /**
     * @brief 设置自动同步间隔
     * @param intervalMs 间隔时间（毫秒），0表示禁用自动同步
     */
    void setAutoSyncInterval(int intervalMs);
    
    /**
     * @brief 获取当前同步状态
     * @return 同步状态
     */
    SyncStatus getSyncStatus() const;

    // ========== 连接管理 ==========
    
    /**
     * @brief 连接到服务器
     * @param host 服务器地址
     * @param port 服务器端口
     */
    void connectToServer(const QString &host, quint16 port);
    
    /**
     * @brief 断开服务器连接
     */
    void disconnectFromServer();
    
    /**
     * @brief 检查是否已连接
     * @return 是否已连接
     */
    bool isConnected() const;

    // ========== 数据验证 ==========
    
    /**
     * @brief 验证变量数据
     * @param var 变量对象
     * @param label 变量标签
     * @return 验证结果和错误信息
     */
    std::pair<bool, QString> validateVariable(const cmd::Var &var, const QString &label) const;

    // ========== 编辑模式枚举（与View共享） ==========
    enum class EditMode {
        None,   // 无编辑
        Add,    // 添加模式
        Edit    // 编辑模式
    };

public slots:
    // ========== View操作请求处理槽 ==========

    /**
     * @brief 处理添加变量请求
     */
    void onAddVariableRequested();

    /**
     * @brief 处理编辑变量请求
     * @param id 变量ID
     */
    void onEditVariableRequested(int id);

    /**
     * @brief 处理删除变量请求
     * @param ids 变量ID列表
     */
    void onDeleteVariableRequested(const QList<int> &ids);

    /**
     * @brief 处理数据提交请求
     * @param id 变量ID（编辑模式时为原始ID，添加模式时为新ID）
     * @param label 变量标签
     * @param typeText 变量类型文本
     * @param value 变量值
     */
    void onDataSubmitted(int id, const QString &label, const QString &typeText, const QVariant &value);

    /**
     * @brief 处理编辑取消请求
     */
    void onEditCancelled();

    /**
     * @brief 处理刷新数据请求
     */
    void onRefreshDataRequested();

    /**
     * @brief 处理同步数据请求
     */
    void onSyncDataRequested();

    /**
     * @brief 处理连接服务器请求
     */
    void onConnectToServerRequested();

    /**
     * @brief 处理断开服务器请求
     */
    void onDisconnectFromServerRequested();

    // ========== 配置管理 ==========

    /**
     * @brief 设置是否等待服务器响应
     * @param wait true=等待响应（同步模式），false=不等待（异步模式）
     */
    void setWaitForServerResponse(bool wait);

    /**
     * @brief 获取是否等待服务器响应
     * @return true=等待响应，false=不等待
     */
    bool getWaitForServerResponse() const;

    /**
     * @brief 设置服务器响应超时时间
     * @param timeoutMs 超时时间（毫秒）
     */
    void setServerResponseTimeout(int timeoutMs);

    /**
     * @brief 获取服务器响应超时时间
     * @return 超时时间（毫秒）
     */
    int getServerResponseTimeout() const;

private slots:
    /**
     * @brief 处理Model数据变化（用于业务逻辑）
     * @param operation 操作类型
     * @param id 变量ID
     */
    void onModelDataChanged(const QString &operation, int id);

signals:
    // ========== UI状态更新信号 ==========

    /**
     * @brief 编辑模式变化信号
     * @param mode 编辑模式
     * @param id 变量ID（添加模式时为新生成的ID，编辑模式时为现有ID）
     */
    void editModeChanged(EditMode mode, int id = -1);

    /**
     * @brief 操作完成信号（简化版，用于UI反馈）
     * @param success 是否成功
     * @param message 消息内容
     */
    void operationCompleted(bool success, const QString &message);

    /**
     * @brief 操作完成信号（详细版，用于Manager层）
     * @param operation 操作类型
     * @param result 操作结果
     * @param message 消息内容
     * @param id 相关变量ID
     */
    void operationCompleted(const QString &operation, OperationResult result, const QString &message, int id);

    /**
     * @brief 变量数据变化信号
     * @param operation 操作类型
     * @param id 变量ID
     */
    void variableDataChanged(const QString &operation, int id);

    /**
     * @brief 连接状态变化信号
     * @param connected 是否已连接
     */
    void connectionStatusChanged(bool connected);

    /**
     * @brief 同步状态变化信号（简化版，用于UI反馈）
     * @param syncing 是否正在同步
     */
    void syncStatusChanged(bool syncing);

    /**
     * @brief 同步状态变化信号（详细版，用于Manager层）
     * @param status 同步状态
     */
    void syncStatusChanged(SyncStatus status);

    /**
     * @brief 忙碌状态变化信号
     * @param busy 是否忙碌
     */
    void busyStatusChanged(bool busy);

    /**
     * @brief 显示消息信号
     * @param message 消息内容
     * @param isError 是否为错误消息
     */
    void showMessage(const QString &message, bool isError = false);

private slots:
    /**
     * @brief 处理CommandTransceiver连接状态变化
     * @param state 连接状态
     */
    void onConnectionStateChanged(CommandTransceiverRefactored::ConnectionState state);
    
    /**
     * @brief 处理命令响应
     * @param response 命令响应
     */
    void onCommandResponseReceived(const CommandTransceiverRefactored::CommandResponse &response);
    
    /**
     * @brief 自动同步定时器超时
     */
    void onAutoSyncTimeout();

private:
    // ========== 核心组件 ==========
    
    VarTblModel *m_model;                           // 数据模型
    CommandTransceiverRefactored *m_transceiver;    // 通信组件
    QUuid m_projectUuid;                            // 项目UUID
    
    // ========== 状态管理 ==========

    mutable QMutex m_statusMutex;                   // 状态互斥锁
    SyncStatus m_syncStatus;                        // 同步状态
    bool m_connected;                               // 连接状态

    // ========== 配置选项 ==========

    bool m_waitForServerResponse;                   // 是否等待服务器响应（默认true）
    int m_serverResponseTimeout;                    // 服务器响应超时时间（毫秒，默认5000）
    
    // ========== 自动同步 ==========
    
    QTimer *m_autoSyncTimer;                        // 自动同步定时器
    int m_autoSyncInterval;                         // 自动同步间隔
    
    // ========== 命令跟踪 ==========
    
    QMutex m_commandMutex;                          // 命令互斥锁
    QHash<QUuid, QString> m_pendingCommands;        // 待处理命令（UUID -> 操作类型）
    QHash<QUuid, int> m_commandVariableIds;         // 命令关联的变量ID
    
    // ========== 内部方法 ==========
    
    /**
     * @brief 发送VarUpdate命令
     * @param id 变量ID
     * @param var 变量对象
     * @param label 变量标签
     * @param operation 操作类型
     * @return 命令UUID
     */
    QUuid sendVarUpdateCommand(int id, const cmd::Var &var, const QString &label, const QString &operation);
    
    /**
     * @brief 发送VarUpdate命令（用于新增变量，替代已废弃的VarAdd）
     * @param id 变量ID
     * @param var 变量对象
     * @param label 变量标签
     * @return 命令UUID
     */
    QUuid sendVarUpdateForAdd(int id, const cmd::Var &var, const QString &label);
    
    /**
     * @brief 发送VarRemove命令
     * @param id 变量ID
     * @return 命令UUID
     */
    QUuid sendVarRemoveCommand(int id);
    
    /**
     * @brief 处理命令成功响应
     * @param uuid 命令UUID
     * @param response 响应数据
     */
    void handleCommandSuccess(const QUuid &uuid, const QJsonObject &response);
    
    /**
     * @brief 处理命令错误响应
     * @param uuid 命令UUID
     * @param errorMessage 错误消息
     */
    void handleCommandError(const QUuid &uuid, const QString &errorMessage);

    /**
     * @brief 处理命令超时
     * @param uuid 命令UUID
     */
    void handleCommandTimeout(const QUuid &uuid);
    
    /**
     * @brief 创建VarIndex
     * @param id 变量ID
     * @return VarIndex对象
     */
    cmd::VarIndex createVarIndex(int id) const;
    
    /**
     * @brief 更新同步状态
     * @param status 新状态
     */
    void updateSyncStatus(SyncStatus status);
    
    /**
     * @brief 生成操作ID
     * @return 操作ID字符串
     */
    QString generateOperationId() const;

    /**
     * @brief 将UI数据转换为cmd::Var对象
     * @param typeText 类型文本
     * @param value 原始值
     * @return 转换后的cmd::Var对象，失败时返回null
     */
    cmd::Var convertToVar(const QString &typeText, const QVariant &value) const;

    /**
     * @brief 发射操作完成信号（同时发射简化版和详细版）
     * @param operation 操作类型
     * @param success 是否成功
     * @param message 消息内容
     * @param id 相关变量ID
     */
    void emitOperationCompleted(const QString &operation, bool success, const QString &message, int id = -1);

    // ========== 服务器通信方法 ==========

    /**
     * @brief 批量同步变量到服务器
     * @param varTbl 要同步的变量表
     */
    void syncVariablesToServer(const cmd::VarTbl &varTbl);

    /**
     * @brief 从服务器刷新变量数据
     */
    void refreshVariablesFromServer();

    /**
     * @brief 同步所有本地变量到服务器
     */
    void syncAllVariablesToServer();
};

#endif // VAR_TBL_CONTROLLER_H
