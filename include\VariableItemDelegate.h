/**
 * @file VariableItemDelegate.h
 * @brief 简洁的变量表格项委托 - String直接编辑，List使用表单
 *
 * <AUTHOR> Framework
 * @date 2025-01-22
 */

#ifndef VARIABLE_ITEM_DELEGATE_H
#define VARIABLE_ITEM_DELEGATE_H

#include <QStyledItemDelegate>
#include <QWidget>
#include <QModelIndex>
#include <QPainter>
#include <QStyleOptionViewItem>
#include <QComboBox>
#include <QLineEdit>
#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QListWidget>
#include <QLabel>

#include "VariableManager.h"

/**
 * @brief 简洁的List编辑表单
 */
class ListEditForm : public QDialog
{
    Q_OBJECT

public:
    explicit ListEditForm(const QStringList &list, QWidget *parent = nullptr);
    QStringList getStringList() const;

private slots:
    void addItem();
    void removeItem();

private:
    void setupUI();

private:
    QListWidget *m_listWidget;
    QLineEdit *m_newItemEdit;
    QPushButton *m_addButton;
    QPushButton *m_removeButton;
};

/**
 * @brief 简洁的变量表格项委托
 *
 * 设计原则：
 * - String类型：直接使用QLineEdit编辑
 * - List类型：使用简洁的表单对话框
 * - 类型列：使用下拉框选择
 */
class VariableItemDelegate : public QStyledItemDelegate
{
    Q_OBJECT

public:
    explicit VariableItemDelegate(QObject *parent = nullptr);

    // 重写基类方法
    void paint(QPainter *painter, const QStyleOptionViewItem &option,
               const QModelIndex &index) const override;

    QWidget *createEditor(QWidget *parent, const QStyleOptionViewItem &option,
                         const QModelIndex &index) const override;

    void setEditorData(QWidget *editor, const QModelIndex &index) const override;

    void setModelData(QWidget *editor, QAbstractItemModel *model,
                     const QModelIndex &index) const override;

private:
    QString formatListDisplay(const QStringList &list) const;
    bool isListColumn(const QModelIndex &index) const;
    QString getVariableType(const QModelIndex &index) const;
};

#endif // VARIABLE_ITEM_DELEGATE_H
