#ifndef VAR_TBL_FILTER_PROXY_MODEL_H
#define VAR_TBL_FILTER_PROXY_MODEL_H

#include <QSortFilterProxyModel>
#include <QRegularExpression>
#include <QTimer>
#include <QThread>
#include <QMutex>
#include <QMap>
#include "VarTblModel.h"
#include "cmd/var.h"

/**
 * @brief VarTblFilterProxyModel - 用于过滤 VarTblModel 数据的代理模型
 *
 * 该类为 VarTblView 提供高级筛选功能，支持正则表达式、多条件筛选、数据缓存和多线程处理。
 * 提供延迟筛选、列选择、大小写敏感性等功能，优化用户体验和性能。
 */
class VarTblFilterProxyModel : public QSortFilterProxyModel
{
    Q_OBJECT

public:
    explicit VarTblFilterProxyModel(QObject *parent = nullptr);
    ~VarTblFilterProxyModel() override;

    /**
     * @brief 设置筛选条件
     * @param filter 筛选字符串
     * @param column 筛选列（-1 表示所有列）
     * @param useRegex 是否使用正则表达式
     */
    void setFilterCondition(const QString &filter, int column, bool useRegex);

    /**
     * @brief 设置筛选是否区分大小写
     * @param caseSensitive 是否区分大小写
     */
    void setCaseSensitivity(bool caseSensitive);

    /**
     * @brief 添加筛选历史记录
     * @param filter 筛选字符串
     */
    void addFilterHistory(const QString &filter);

    /**
     * @brief 获取筛选历史记录
     * @return 筛选历史记录列表
     */
    QStringList getFilterHistory() const;

    /**
     * @brief 清除筛选条件
     */
    void clearFilter();

    /**
     * @brief 更新数据缓存
     */
    void updateDataCache();

signals:
    /**
     * @brief 筛选历史记录更新信号
     */
    void filterHistoryUpdated();

protected:
    /**
     * @brief 自定义筛选逻辑
     * @param sourceRow 源模型中的行号
     * @param sourceParent 源模型中的父索引
     * @return 如果该行应显示，返回 true
     */
    bool filterAcceptsRow(int sourceRow, const QModelIndex &sourceParent) const override;

private slots:
    /**
     * @brief 处理延迟筛选
     */
    void onFilterTimeout();

    /**
     * @brief 处理异步筛选结果
     * @param filteredRows 筛选后的行索引列表
     */
    void onAsyncFilterCompleted(const QList<int> &filteredRows);

private:
    /**
     * @brief 异步筛选任务
     * @param rows 待筛选的行号列表
     */
    void performAsyncFilter(const QList<int> &rows);

    // 筛选条件结构
    struct FilterCondition {
        QString filter; // 筛选字符串
        int column;     // 筛选列（-1 表示所有列）
        bool useRegex;  // 是否使用正则表达式
    };

    QList<FilterCondition> m_conditions; // 多条件筛选列表
    QString m_pendingFilter;            // 等待应用的筛选字符串
    bool m_caseSensitive;               // 是否区分大小写
    QTimer *m_filterTimer;              // 延迟筛选定时器
    QThread *m_filterThread;            // 筛选线程
    mutable QMutex m_cacheMutex;                // 缓存访问互斥锁
    QMap<int, QStringList> m_dataCache; // 行数据缓存（行号 -> 列数据列表）
    QStringList m_filterHistory;        // 筛选历史记录
};

#endif // VAR_TBL_FILTER_PROXY_MODEL_H
