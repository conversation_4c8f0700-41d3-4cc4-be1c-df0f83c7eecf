#include "mvc/VarTblFilterProxyModel.h"
#include <QMutexLocker>
#include <QDebug>

VarTblFilterProxyModel::VarTblFilterProxyModel(QObject *parent)
    : QSortFilterProxyModel(parent)
    , m_pendingFilter("")
    , m_caseSensitive(false)
    , m_filterTimer(new QTimer(this))
    , m_filterThread(new QThread(this))
{
    qDebug() << "VarTblFilterProxyModel: 创建实例";

    // 设置筛选延迟定时器
    m_filterTimer->setSingleShot(true);
    m_filterTimer->setInterval(300); // 300ms 延迟
    connect(m_filterTimer, &QTimer::timeout, this, &VarTblFilterProxyModel::onFilterTimeout);

    // 初始化数据缓存
    updateDataCache();

    // 启动筛选线程
    m_filterThread->start();
}

/**
 * @brief 析构函数
 */
VarTblFilterProxyModel::~VarTblFilterProxyModel()
{
    m_filterThread->quit();
    m_filterThread->wait();
    delete m_filterThread;
    qDebug() << "VarTblFilterProxyModel: 销毁实例";
}

/**
 * @brief 设置筛选条件
 * @param filter 筛选字符串
 * @param column 筛选列（-1 表示所有列）
 * @param useRegex 是否使用正则表达式
 */
void VarTblFilterProxyModel::setFilterCondition(const QString &filter, int column, bool useRegex)
{
    FilterCondition condition = {filter, column, useRegex};
    m_conditions.clear(); // 暂时只支持单条件，未来可扩展
    m_conditions.append(condition);
    m_pendingFilter = filter;
    m_filterTimer->start();
    qDebug() << "VarTblFilterProxyModel: 设置筛选条件，字符串=" << filter << "列=" << column << "正则=" << useRegex;

    updateDataCache();
    invalidateFilter();
    // 添加到筛选历史记录（仅非空且非重复）
    addFilterHistory(filter);
}

/**
 * @brief 设置筛选是否区分大小写
 * @param caseSensitive 是否区分大小写
 */
void VarTblFilterProxyModel::setCaseSensitivity(bool caseSensitive)
{
    m_caseSensitive = caseSensitive;
    updateDataCache(); // 重新构建缓存以反映大小写敏感性
    invalidateFilter();
    qDebug() << "VarTblFilterProxyModel: 大小写敏感设置为" << caseSensitive;
}

/**
 * @brief 添加筛选历史记录
 * @param filter 筛选字符串
 */
void VarTblFilterProxyModel::addFilterHistory(const QString &filter)
{
    if (!filter.isEmpty() && !m_filterHistory.contains(filter, Qt::CaseSensitive)) {
        m_filterHistory.prepend(filter);
        if (m_filterHistory.size() > 10) { // 限制历史记录数量
            m_filterHistory.removeLast();
        }
        emit filterHistoryUpdated();
        qDebug() << "VarTblFilterProxyModel: 添加筛选历史记录" << filter;
    }
}

/**
 * @brief 获取筛选历史记录
 * @return 筛选历史记录列表
 */
QStringList VarTblFilterProxyModel::getFilterHistory() const
{
    return m_filterHistory;
}

/**
 * @brief 清除筛选条件
 */
void VarTblFilterProxyModel::clearFilter()
{
    m_conditions.clear();
    m_pendingFilter.clear();
    m_filterTimer->stop();
    invalidateFilter();
    qDebug() << "VarTblFilterProxyModel: 清除筛选条件";
}

/**
 * @brief 处理延迟筛选
 */
void VarTblFilterProxyModel::onFilterTimeout()
{
    if (m_conditions.isEmpty() || m_conditions.last().filter != m_pendingFilter) {
        updateDataCache(); // 确保缓存最新
        invalidateFilter();
        qDebug() << "VarTblFilterProxyModel: 应用筛选条件" << m_pendingFilter;
    }

    // 异步筛选
    VarTblModel *model = qobject_cast<VarTblModel *>(sourceModel());
    if (!model) {
        qDebug() << "VarTblFilterProxyModel: 无效的源模型";
        return;
    }

    QList<int> rows;
    for (int i = 0; i < model->rowCount(); ++i) {
        rows.append(i);
    }
    performAsyncFilter(rows);
}

/**
 * @brief 处理异步筛选结果
 * @param filteredRows 筛选后的行索引列表
 */
void VarTblFilterProxyModel::onAsyncFilterCompleted(const QList<int> &filteredRows)
{
    // 更新筛选结果（需要在主线程中调用）
    QMutexLocker locker(&m_cacheMutex);
    invalidateFilter();
    qDebug() << "VarTblFilterProxyModel: 异步筛选完成，匹配行数=" << filteredRows.size();
}

/**
 * @brief 更新数据缓存
 */
void VarTblFilterProxyModel::updateDataCache()
{
    QMutexLocker locker(&m_cacheMutex);
    m_dataCache.clear();

    VarTblModel *model = qobject_cast<VarTblModel *>(sourceModel());
    if (!model) {
        qDebug() << "VarTblFilterProxyModel: 无法更新缓存，无效的源模型";
        return;
    }

    for (int row = 0; row < model->rowCount(); ++row) {
        QStringList rowData;
        for (int col = 0; col < model->columnCount(); ++col) {
            QModelIndex index = model->index(row, col);
            QString data = model->data(index, Qt::DisplayRole).toString();
            rowData.append(m_caseSensitive ? data : data.toLower());
        }
        m_dataCache[row] = rowData;
    }
    qDebug() << "VarTblFilterProxyModel: 数据缓存更新，行数=" << m_dataCache.size();
}

/**
 * @brief 异步筛选任务
 * @param rows 待筛选的行号列表
 */
void VarTblFilterProxyModel::performAsyncFilter(const QList<int> &rows)
{
    // 创建异步任务
    auto *worker = new QObject;
    worker->moveToThread(m_filterThread);

    connect(worker, &QObject::destroyed, this, [worker]() {
        worker->deleteLater();
    });

    connect(m_filterThread, &QThread::started, worker, [this, rows, worker]() {
        QList<int> filteredRows;
        QMutexLocker locker(&m_cacheMutex);

        Qt::CaseSensitivity caseSensitivity = m_caseSensitive ? Qt::CaseSensitive : Qt::CaseInsensitive;

        for (int row : rows) {
            bool rowAccepted = true;
            for (const auto &condition : m_conditions) {
                bool conditionMet = false;
                QString filter = m_caseSensitive ? condition.filter : condition.filter.toLower();
                QRegularExpression regex(filter, m_caseSensitive ? QRegularExpression::NoPatternOption
                                                                : QRegularExpression::CaseInsensitiveOption);

                // 检查缓存中的数据
                const QStringList &rowData = m_dataCache.value(row);
                if (condition.column >= 0) {
                    if (condition.column < rowData.size()) {
                        QString data = rowData[condition.column];
                        conditionMet = condition.useRegex ? regex.match(data).hasMatch()
                                                         : data.contains(filter, caseSensitivity);
                    }
                } else {
                    for (const QString &data : rowData) {
                        if (condition.useRegex ? regex.match(data).hasMatch()
                                              : data.contains(filter, caseSensitivity)) {
                            conditionMet = true;
                            break;
                        }
                    }
                }
                if (!conditionMet) {
                    rowAccepted = false;
                    break;
                }
            }
            if (rowAccepted) {
                filteredRows.append(row);
            }
        }

        // 发送结果回主线程
        emit onAsyncFilterCompleted(filteredRows);
        worker->deleteLater();
    });

    m_filterThread->start();
}

/**
 * @brief 实现筛选逻辑
 * @param sourceRow 源模型中的行号
 * @param sourceParent 源模型中的父索引
 * @return 如果该行匹配筛选条件，返回 true
 */
bool VarTblFilterProxyModel::filterAcceptsRow(int sourceRow, const QModelIndex &sourceParent) const
{
    Q_UNUSED(sourceParent)

    QMutexLocker locker(&m_cacheMutex);
    if (m_conditions.isEmpty()) {
        return true;
    }

    const QStringList &rowData = m_dataCache.value(sourceRow);
    if (rowData.isEmpty()) {
        return false;
    }

    Qt::CaseSensitivity caseSensitivity = m_caseSensitive ? Qt::CaseSensitive : Qt::CaseInsensitive;

    for (const auto &condition : m_conditions) {
        bool conditionMet = false;
        QString filter = m_caseSensitive ? condition.filter : condition.filter.toLower();
        QRegularExpression regex(filter, m_caseSensitive ? QRegularExpression::NoPatternOption
                                                        : QRegularExpression::CaseInsensitiveOption);

        if (condition.column >= 0) {
            if (condition.column < rowData.size()) {
                QString data = rowData[condition.column];
                conditionMet = condition.useRegex ? regex.match(data).hasMatch()
                                                 : data.contains(filter, caseSensitivity);
            }
        } else {
            for (const QString &data : rowData) {
                if (condition.useRegex ? regex.match(data).hasMatch()
                                      : data.contains(filter, caseSensitivity)) {
                    conditionMet = true;
                    break;
                }
            }
        }
        if (!conditionMet) {
            return false; // 所有条件必须满足（AND 逻辑）
        }
    }

    return true;
}
