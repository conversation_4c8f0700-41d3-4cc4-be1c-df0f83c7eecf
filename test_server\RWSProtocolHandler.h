#ifndef RWS_PROTOCOL_HANDLER_H
#define RWS_PROTOCOL_HANDLER_H

#include <QObject>
#include <QByteArray>
#include <QList>
#include <QString>

/**
 * @brief RWS协议处理器 - 服务器端实现
 * 
 * 兼容 CommandTransceiver 的 RWS 策略系统
 * 支持三种协议：PreLength、EndWithNewLine、Simple
 */
class RWSProtocolHandler : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 协议类型枚举
     */
    enum ProtocolType {
        PreLength,          // 预长度协议（4字节长度头 + 数据）
        EndWithNewLine,     // 换行符结束协议
        Simple              // 简单协议（直接传输）
    };

    explicit RWSProtocolHandler(ProtocolType type = PreLength, QObject *parent = nullptr);
    ~RWSProtocolHandler();

    /**
     * @brief 设置协议类型
     * @param type 协议类型
     */
    void setProtocolType(ProtocolType type);

    /**
     * @brief 获取协议类型
     * @return 当前协议类型
     */
    ProtocolType getProtocolType() const { return m_protocolType; }

    /**
     * @brief 封装消息用于发送
     * @param message 原始消息数据
     * @return 封装后的数据
     */
    QByteArray wrapMessage(const QByteArray &message);

    /**
     * @brief 处理接收到的数据，解析出完整消息
     * @param newData 新接收的数据
     * @return 解析出的完整消息列表
     */
    QList<QByteArray> processReceivedData(const QByteArray &newData);

    /**
     * @brief 清空接收缓冲区
     */
    void clearBuffer();

    /**
     * @brief 尝试解压缩数据
     * @param data 可能被压缩的数据
     * @return 解压后的数据，如果不是压缩数据则返回原始数据
     */
    static QByteArray decompressData(const QByteArray &data);

    /**
     * @brief 检测数据是否被 qCompress 压缩
     * @param data 要检测的数据
     * @return true 如果数据被压缩
     */
    static bool isCompressedData(const QByteArray &data);

    /**
     * @brief 获取压缩统计信息
     * @return 压缩统计信息的映射
     */
    QVariantMap getCompressionStats() const;

    /**
     * @brief 获取协议名称
     * @return 协议名称字符串
     */
    QString getProtocolName() const;

    /**
     * @brief 协议类型转字符串
     */
    static QString protocolTypeToString(ProtocolType type);

    /**
     * @brief 字符串转协议类型
     */
    static ProtocolType stringToProtocolType(const QString &str);

private:
    // PreLength 协议处理
    QByteArray wrapPreLengthMessage(const QByteArray &message);
    QList<QByteArray> processPreLengthData(const QByteArray &newData);

    // EndWithNewLine 协议处理
    QByteArray wrapEndWithNewLineMessage(const QByteArray &message);
    QList<QByteArray> processEndWithNewLineData(const QByteArray &newData);

    // Simple 协议处理
    QByteArray wrapSimpleMessage(const QByteArray &message);
    QList<QByteArray> processSimpleData(const QByteArray &newData);

    // 辅助方法
    QList<QByteArray> extractPreLengthMessages();
    QList<QByteArray> extractEndWithNewLineMessages();
    bool isValidMessageLength(quint32 length) const;

private:
    ProtocolType m_protocolType;        // 当前协议类型
    QByteArray m_receiveBuffer;         // 接收缓冲区

    // 压缩统计
    mutable quint64 m_compressedPacketsReceived;    // 接收到的压缩包数量
    mutable quint64 m_totalCompressedBytes;         // 压缩数据总字节数
    mutable quint64 m_totalUncompressedBytes;       // 解压后数据总字节数

    // 协议配置
    static const int LENGTH_HEADER_SIZE = 4;           // PreLength协议长度头大小
    static const int MAX_MESSAGE_SIZE = 5 * 1024 * 1024; // 最大消息大小 (5MB)
    static const int MAX_LINE_LENGTH = 64 * 1024;      // 最大行长度 (64KB)
    static const QByteArray NEWLINE_DELIMITER;         // 换行符分隔符
};

#endif // RWS_PROTOCOL_HANDLER_H
