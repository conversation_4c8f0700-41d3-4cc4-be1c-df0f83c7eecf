#include "TestServer.h"
#include "QtCompatibility.h"
#include <QDebug>
#include <QJsonArray>
#include <QJsonDocument>
#include <QHostAddress>
#include <QCoreApplication>

TestServer::TestServer(QObject *parent)
    : QObject(parent)
    , m_tcpServer(new QTcpServer(this))
    , m_statisticsTimer(new QTimer(this))
    , m_totalConnections(0)
    , m_totalMessagesReceived(0)
    , m_totalMessagesSent(0)
    , m_totalBytesReceived(0)
    , m_totalBytesSent(0)
{
    // 连接TCP服务器信号
    connect(m_tcpServer, &QTcpServer::newConnection, this, &TestServer::onNewConnection);
    
    // 配置统计信息定时器
    m_statisticsTimer->setSingleShot(false);
    connect(m_statisticsTimer, &QTimer::timeout, this, &TestServer::printStatistics);
    
    qDebug() << "TestServer: 服务器已初始化";
}

TestServer::~TestServer()
{
    stopServer();
    qDebug() << "TestServer: 服务器已销毁";
}

bool TestServer::startServer(const ServerConfig &config)
{
    if (isRunning()) {
        qWarning() << "TestServer: 服务器已在运行";
        return false;
    }
    
    m_config = config;
    
    // 启动TCP服务器
    if (!m_tcpServer->listen(QHostAddress::Any, m_config.port)) {
        qCritical() << "TestServer: 无法启动服务器，端口:" << m_config.port 
                   << "错误:" << m_tcpServer->errorString();
        return false;
    }
    
    m_startTime = QDateTime::currentDateTime();
    
    // 启动统计信息定时器（每30秒打印一次）
    if (m_config.enableLogging) {
        m_statisticsTimer->start(30000);
    }
    
    logMessage(QString("服务器已启动 - 端口: %1, 协议: %2, 心跳: %3")
               .arg(m_config.port)
               .arg(RWSProtocolHandler::protocolTypeToString(m_config.protocolType))
               .arg(m_config.enableHeartbeat ? "启用" : "禁用"));
    
    emit serverStarted(m_config.port);
    return true;
}

void TestServer::stopServer()
{
    if (!isRunning()) {
        return;
    }
    
    // 停止接受新连接
    m_tcpServer->close();
    
    // 断开所有客户端连接
    QList<QUuid> connectionIds = m_connections.keys();
    for (const QUuid &id : connectionIds) {
        disconnectClient(id);
    }
    
    // 停止定时器
    m_statisticsTimer->stop();
    
    logMessage("服务器已停止");
    emit serverStopped();
}

bool TestServer::isRunning() const
{
    return m_tcpServer->isListening();
}

int TestServer::getConnectionCount() const
{
    return m_connections.size();
}

QJsonObject TestServer::getServerStatistics() const
{
    QJsonObject stats;
    stats["isRunning"] = isRunning();
    stats["port"] = static_cast<int>(m_config.port);
    stats["protocolType"] = RWSProtocolHandler::protocolTypeToString(m_config.protocolType);
    stats["enableHeartbeat"] = m_config.enableHeartbeat;
    stats["heartbeatInterval"] = m_config.heartbeatInterval;
    stats["maxConnections"] = m_config.maxConnections;
    stats["currentConnections"] = getConnectionCount();
    stats["totalConnections"] = m_totalConnections;
    stats["totalMessagesReceived"] = m_totalMessagesReceived;
    stats["totalMessagesSent"] = m_totalMessagesSent;
    stats["totalBytesReceived"] = m_totalBytesReceived;
    stats["totalBytesSent"] = m_totalBytesSent;
    
    if (isRunning()) {
        qint64 uptime = m_startTime.secsTo(QDateTime::currentDateTime());
        stats["uptimeSeconds"] = uptime;
        stats["startTime"] = m_startTime.toString(Qt::ISODate);
    }
    
    return stats;
}

QJsonArray TestServer::getConnectionsStatistics() const
{
    QJsonArray connections;
    for (auto it = m_connections.constBegin(); it != m_connections.constEnd(); ++it) {
        connections.append(it.value()->getStatistics());
    }
    return connections;
}

bool TestServer::sendMessageToClient(const QUuid &connectionId, const QJsonObject &message)
{
    auto it = m_connections.find(connectionId);
    if (it == m_connections.end()) {
        qWarning() << "TestServer: 未找到连接" << connectionId.toString();
        return false;
    }
    
    bool success = it.value()->sendJsonResponse(message);
    if (success) {
        m_totalMessagesSent++;
    }
    
    return success;
}

int TestServer::broadcastMessage(const QJsonObject &message)
{
    int successCount = 0;
    for (auto it = m_connections.begin(); it != m_connections.end(); ++it) {
        if (it.value()->sendJsonResponse(message)) {
            successCount++;
            m_totalMessagesSent++;
        }
    }
    
    logMessage(QString("广播消息已发送给 %1/%2 个客户端")
               .arg(successCount).arg(m_connections.size()));
    
    return successCount;
}

bool TestServer::disconnectClient(const QUuid &connectionId)
{
    auto it = m_connections.find(connectionId);
    if (it == m_connections.end()) {
        return false;
    }
    
    ClientConnection *connection = it.value();
    connection->disconnectClient();
    
    return true;
}

void TestServer::setProtocolType(RWSProtocolHandler::ProtocolType type)
{
    m_config.protocolType = type;
    logMessage(QString("默认协议类型已设置为: %1")
               .arg(RWSProtocolHandler::protocolTypeToString(type)));
}

void TestServer::printServerStatus()
{
    QJsonObject stats = getServerStatistics();
    QJsonDocument doc(stats);
    
    qInfo() << "========== 服务器状态 ==========";
    qInfo() << doc.toJson(QJsonDocument::Indented);
}

void TestServer::printConnectionList()
{
    qInfo() << "========== 连接列表 ==========";
    qInfo() << QString("当前连接数: %1").arg(getConnectionCount());
    
    for (auto it = m_connections.constBegin(); it != m_connections.constEnd(); ++it) {
        ClientConnection *conn = it.value();
        qInfo() << QString("连接ID: %1, 地址: %2, 协议: %3, 持续时间: %4秒")
                   .arg(conn->getConnectionId().toString())
                   .arg(conn->getClientAddress())
                   .arg(RWSProtocolHandler::protocolTypeToString(conn->getProtocolType()))
                   .arg(conn->getConnectionDuration());
    }
}

void TestServer::onNewConnection()
{
    while (m_tcpServer->hasPendingConnections()) {
        QTcpSocket *socket = m_tcpServer->nextPendingConnection();
        
        // 检查连接数限制
        if (m_connections.size() >= m_config.maxConnections) {
            qWarning() << "TestServer: 达到最大连接数限制，拒绝新连接";
            socket->disconnectFromHost();
            socket->deleteLater();
            continue;
        }
        
        // 创建客户端连接对象
        ClientConnection *connection = new ClientConnection(socket, m_config.protocolType, this);
        
        // 配置心跳检测
        if (m_config.enableHeartbeat) {
            connection->setHeartbeatEnabled(true, m_config.heartbeatInterval);
        }
        
        // 连接信号
        connect(connection, &ClientConnection::connectionStateChanged,
                this, &TestServer::onClientConnectionStateChanged);
        connect(connection, &ClientConnection::connectionDisconnected,
                this, &TestServer::onClientDisconnected);
        connect(connection, &ClientConnection::jsonCommandReceived,
                this, &TestServer::onClientJsonCommand);
        connect(connection, &ClientConnection::rawDataReceived,
                this, &TestServer::onClientRawData);
        connect(connection, &ClientConnection::connectionError,
                this, &TestServer::onClientConnectionError);
        
        // 添加到连接映射
        m_connections.insert(connection->getConnectionId(), connection);
        m_totalConnections++;
        
        logMessage(QString("新客户端已连接 - ID: %1, 地址: %2, 总连接数: %3")
                   .arg(connection->getConnectionId().toString())
                   .arg(connection->getClientAddress())
                   .arg(m_connections.size()));
        
        emit clientConnected(connection->getConnectionId(), connection->getClientAddress());
    }
}

void TestServer::onClientConnectionStateChanged(const QUuid &connectionId, ClientConnection::ConnectionState state)
{
    Q_UNUSED(state)
    logMessage(QString("客户端连接状态变化 - ID: %1, 状态: %2")
               .arg(connectionId.toString()).arg(static_cast<int>(state)));
}

void TestServer::onClientDisconnected(const QUuid &connectionId)
{
    auto it = m_connections.find(connectionId);
    if (it != m_connections.end()) {
        ClientConnection *connection = it.value();
        
        logMessage(QString("客户端已断开连接 - ID: %1, 地址: %2, 剩余连接数: %3")
                   .arg(connectionId.toString())
                   .arg(connection->getClientAddress())
                   .arg(m_connections.size() - 1));
        
        // 从映射中移除并删除对象
        m_connections.erase(it);
        connection->deleteLater();
        
        emit clientDisconnected(connectionId);
    }
}

void TestServer::onClientJsonCommand(const QUuid &connectionId, const QJsonObject &command)
{
    m_totalMessagesReceived++;

    logMessage(QString("接收到JSON命令 - ID: %1, 命令: %2")
               .arg(connectionId.toString())
               .arg(QT_JSON_TO_STRING(QJsonDocument(command))));

    emit jsonCommandReceived(connectionId, command);

    // 处理特殊命令
    if (handleSpecialCommand(connectionId, command)) {
        return;
    }

    // 自动响应
    if (m_config.enableAutoResponse) {
        QJsonObject response = createAutoResponse(command);
        sendMessageToClient(connectionId, response);
    }

    // 命令回显
    if (m_config.enableEcho) {
        QJsonObject echo = command;
        echo["echo"] = true;
        echo["timestamp"] = QDateTime::currentMSecsSinceEpoch();
        sendMessageToClient(connectionId, echo);
    }
}

void TestServer::onClientRawData(const QUuid &connectionId, const QByteArray &data)
{
    m_totalMessagesReceived++;
    m_totalBytesReceived += data.size();

    logMessage(QString("接收到原始数据 - ID: %1, 大小: %2 字节")
               .arg(connectionId.toString()).arg(data.size()));

    emit rawDataReceived(connectionId, data);

    // 原始数据回显
    if (m_config.enableEcho) {
        auto it = m_connections.find(connectionId);
        if (it != m_connections.end()) {
            it.value()->sendRawData(data);
        }
    }
}

void TestServer::onClientConnectionError(const QUuid &connectionId, const QString &error)
{
    logMessage(QString("客户端连接错误 - ID: %1, 错误: %2")
               .arg(connectionId.toString()).arg(error));
}

void TestServer::printStatistics()
{
    if (!m_config.enableLogging) {
        return;
    }

    qInfo() << "========== 服务器统计信息 ==========";
    qInfo() << QString("运行时间: %1 秒").arg(m_startTime.secsTo(QDateTime::currentDateTime()));
    qInfo() << QString("当前连接数: %1").arg(getConnectionCount());
    qInfo() << QString("总连接数: %1").arg(m_totalConnections);
    qInfo() << QString("总接收消息数: %1").arg(m_totalMessagesReceived);
    qInfo() << QString("总发送消息数: %1").arg(m_totalMessagesSent);
    qInfo() << QString("总接收字节数: %1").arg(m_totalBytesReceived);
    qInfo() << QString("总发送字节数: %1").arg(m_totalBytesSent);
}

QJsonObject TestServer::createAutoResponse(const QJsonObject &command)
{
    QJsonObject response;
    response["type"] = "response";
    response["timestamp"] = QDateTime::currentMSecsSinceEpoch();
    response["serverId"] = "CommandTransceiverTestServer";
    response["status"] = "success";

    // 如果命令包含ID，则回显
    if (command.contains("id")) {
        response["commandId"] = command.value("id");
    }

    // 如果命令包含UUID，则回显
    if (command.contains("uuid")) {
        response["commandUuid"] = command.value("uuid");
    }

    // 根据命令类型创建特定响应
    QString commandType = command.value("type").toString();
    if (commandType == "ping") {
        response["message"] = "pong";
    } else if (commandType == "echo") {
        response["message"] = command.value("message");
    } else if (commandType == "test") {
        response["message"] = "Test command received successfully";
        response["data"] = command.value("data");
    } else {
        response["message"] = QString("Command '%1' processed successfully").arg(commandType);
    }

    return response;
}

bool TestServer::handleSpecialCommand(const QUuid &connectionId, const QJsonObject &command)
{
    QString commandType = command.value("type").toString();

    if (commandType == "server_status") {
        // 服务器状态查询
        QJsonObject response = getServerStatistics();
        response["type"] = "server_status_response";
        sendMessageToClient(connectionId, response);
        return true;
    }

    if (commandType == "connection_list") {
        // 连接列表查询
        QJsonObject response;
        response["type"] = "connection_list_response";
        response["connections"] = getConnectionsStatistics();
        sendMessageToClient(connectionId, response);
        return true;
    }

    if (commandType == "set_protocol") {
        // 设置协议类型
        QString protocolStr = command.value("protocol").toString();
        RWSProtocolHandler::ProtocolType newType =
            RWSProtocolHandler::stringToProtocolType(protocolStr);

        auto it = m_connections.find(connectionId);
        if (it != m_connections.end()) {
            it.value()->setProtocolType(newType);

            QJsonObject response;
            response["type"] = "set_protocol_response";
            response["status"] = "success";
            response["protocol"] = RWSProtocolHandler::protocolTypeToString(newType);
            sendMessageToClient(connectionId, response);
        }
        return true;
    }

    if (commandType == "broadcast") {
        // 广播消息
        QJsonObject broadcastMsg = command.value("message").toObject();
        int sentCount = broadcastMessage(broadcastMsg);

        QJsonObject response;
        response["type"] = "broadcast_response";
        response["status"] = "success";
        response["sentToClients"] = sentCount;
        sendMessageToClient(connectionId, response);
        return true;
    }

    if (commandType == "heartbeat") {
        // 心跳命令
        QJsonObject response;
        response["type"] = "heartbeat_response";
        response["timestamp"] = QDateTime::currentMSecsSinceEpoch();
        if (command.contains("id")) {
            response["id"] = command.value("id");
        }
        sendMessageToClient(connectionId, response);
        return true;
    }

    if (commandType == "compression_stats") {
        // 压缩统计查询
        auto it = m_connections.find(connectionId);
        if (it != m_connections.end()) {
            QJsonObject response = it.value()->getCompressionStats();
            response["type"] = "compression_stats_response";
            sendMessageToClient(connectionId, response);
        } else {
            QJsonObject response;
            response["type"] = "compression_stats_response";
            response["error"] = "连接不存在";
            sendMessageToClient(connectionId, response);
        }
        return true;
    }

    return false;
}

void TestServer::logMessage(const QString &message)
{
    if (m_config.enableLogging) {
        QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
        qInfo() << QString("[%1] %2").arg(timestamp, message);
    }
}

TestServer::ServerConfig TestServer::parseCommandLine(const QCommandLineParser &parser)
{
    ServerConfig config;

    // 解析端口
    if (parser.isSet("port")) {
        bool ok;
        int port = parser.value("port").toInt(&ok);
        if (ok && port > 0 && port <= 65535) {
            config.port = static_cast<quint16>(port);
        } else {
            qWarning() << "无效的端口号，使用默认值:" << config.port;
        }
    }

    // 解析协议类型
    if (parser.isSet("protocol")) {
        QString protocolStr = parser.value("protocol").toLower();
        config.protocolType = RWSProtocolHandler::stringToProtocolType(protocolStr);
    }

    // 解析心跳设置
    if (parser.isSet("heartbeat")) {
        config.enableHeartbeat = true;
        if (parser.isSet("heartbeat-interval")) {
            bool ok;
            int interval = parser.value("heartbeat-interval").toInt(&ok);
            if (ok && interval > 0) {
                config.heartbeatInterval = interval;
            }
        }
    }

    // 解析最大连接数
    if (parser.isSet("max-connections")) {
        bool ok;
        int maxConn = parser.value("max-connections").toInt(&ok);
        if (ok && maxConn > 0) {
            config.maxConnections = maxConn;
        }
    }

    // 解析日志设置
    if (parser.isSet("no-logging")) {
        config.enableLogging = false;
    }

    // 解析回显设置
    if (parser.isSet("no-echo")) {
        config.enableEcho = false;
    }

    // 解析自动响应设置
    if (parser.isSet("no-auto-response")) {
        config.enableAutoResponse = false;
    }

    return config;
}

void TestServer::setupCommandLineOptions(QCommandLineParser &parser)
{
    parser.setApplicationDescription("CommandTransceiver 测试服务器");
    parser.addHelpOption();
    parser.addVersionOption();

    // 端口选项
    parser.addOption(QCommandLineOption(
        {"p", "port"},
        "服务器监听端口 (默认: 8080)",
        "port",
        "8080"
    ));

    // 协议类型选项
    parser.addOption(QCommandLineOption(
        {"t", "protocol"},
        "通信协议类型: prelength, endwithnewline, simple (默认: prelength)",
        "protocol",
        "prelength"
    ));

    // 心跳选项
    parser.addOption(QCommandLineOption(
        "heartbeat",
        "启用心跳检测"
    ));

    parser.addOption(QCommandLineOption(
        "heartbeat-interval",
        "心跳间隔时间，毫秒 (默认: 30000)",
        "ms",
        "30000"
    ));

    // 最大连接数选项
    parser.addOption(QCommandLineOption(
        {"m", "max-connections"},
        "最大客户端连接数 (默认: 100)",
        "count",
        "100"
    ));

    // 日志选项
    parser.addOption(QCommandLineOption(
        "no-logging",
        "禁用日志输出"
    ));

    // 回显选项
    parser.addOption(QCommandLineOption(
        "no-echo",
        "禁用命令回显"
    ));

    // 自动响应选项
    parser.addOption(QCommandLineOption(
        "no-auto-response",
        "禁用自动响应"
    ));
}
