#ifndef QT_COMPATIBILITY_H
#define QT_COMPATIBILITY_H

#include <QtGlobal>

/**
 * @brief Qt 版本兼容性定义
 * 
 * 处理不同 Qt 版本之间的 API 差异
 */

// Qt 5.15 引入了 errorOccurred 信号，替代了 error 信号
#if QT_VERSION >= QT_VERSION_CHECK(5, 15, 0)
    #define QT_SOCKET_ERROR_SIGNAL errorOccurred
#else
    #define QT_SOCKET_ERROR_SIGNAL error
#endif

// Qt 6.0 中一些 API 发生了变化
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
    #define QT_SKIP_EMPTY_PARTS Qt::SkipEmptyParts
#else
    #define QT_SKIP_EMPTY_PARTS QString::SkipEmptyParts
#endif

// 字符串转换兼容性宏
#define QT_JSON_TO_STRING(jsonDoc) QString::fromUtf8((jsonDoc).toJson(QJsonDocument::Compact))

#endif // QT_COMPATIBILITY_H
