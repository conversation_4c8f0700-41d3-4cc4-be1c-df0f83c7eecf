/****************************************************************************
** Meta object code from reading C++ file 'CommandTransceiverRefactored.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../include/CommandTransceiverRefactored.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'CommandTransceiverRefactored.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CommandTransceiverRefactored_t {
    QByteArrayData data[39];
    char stringdata0[738];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CommandTransceiverRefactored_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CommandTransceiverRefactored_t qt_meta_stringdata_CommandTransceiverRefactored = {
    {
QT_MOC_LITERAL(0, 0, 28), // "CommandTransceiverRefactored"
QT_MOC_LITERAL(1, 29, 11), // "initialized"
QT_MOC_LITERAL(2, 41, 0), // ""
QT_MOC_LITERAL(3, 42, 22), // "connectionStateChanged"
QT_MOC_LITERAL(4, 65, 15), // "ConnectionState"
QT_MOC_LITERAL(5, 81, 5), // "state"
QT_MOC_LITERAL(6, 87, 23), // "commandResponseReceived"
QT_MOC_LITERAL(7, 111, 15), // "CommandResponse"
QT_MOC_LITERAL(8, 127, 8), // "response"
QT_MOC_LITERAL(9, 136, 15), // "commandReceived"
QT_MOC_LITERAL(10, 152, 7), // "command"
QT_MOC_LITERAL(11, 160, 4), // "uuid"
QT_MOC_LITERAL(12, 165, 13), // "replyReceived"
QT_MOC_LITERAL(13, 179, 5), // "reply"
QT_MOC_LITERAL(14, 185, 13), // "errorOccurred"
QT_MOC_LITERAL(15, 199, 5), // "error"
QT_MOC_LITERAL(16, 205, 26), // "onNetworkWorkerInitialized"
QT_MOC_LITERAL(17, 232, 32), // "onDataProcessorWorkerInitialized"
QT_MOC_LITERAL(18, 265, 31), // "onNetworkWorkerCleanupCompleted"
QT_MOC_LITERAL(19, 297, 37), // "onDataProcessorWorkerCleanupC..."
QT_MOC_LITERAL(20, 335, 31), // "onNetworkConnectionStateChanged"
QT_MOC_LITERAL(21, 367, 30), // "NetworkWorker::ConnectionState"
QT_MOC_LITERAL(22, 398, 24), // "onNetworkRawDataReceived"
QT_MOC_LITERAL(23, 423, 4), // "data"
QT_MOC_LITERAL(24, 428, 14), // "onNetworkError"
QT_MOC_LITERAL(25, 443, 27), // "NetworkWorker::NetworkError"
QT_MOC_LITERAL(26, 471, 11), // "description"
QT_MOC_LITERAL(27, 483, 17), // "onNetworkDataSent"
QT_MOC_LITERAL(28, 501, 12), // "bytesWritten"
QT_MOC_LITERAL(29, 514, 23), // "onNetworkDataSendFailed"
QT_MOC_LITERAL(30, 538, 30), // "onDataProcessorCommandReceived"
QT_MOC_LITERAL(31, 569, 28), // "onDataProcessorReplyReceived"
QT_MOC_LITERAL(32, 598, 26), // "onDataProcessorJsonEncoded"
QT_MOC_LITERAL(33, 625, 11), // "encodedData"
QT_MOC_LITERAL(34, 637, 12), // "originalJson"
QT_MOC_LITERAL(35, 650, 20), // "onDataProcessorError"
QT_MOC_LITERAL(36, 671, 33), // "DataProcessorWorker::ProcessE..."
QT_MOC_LITERAL(37, 705, 11), // "errorString"
QT_MOC_LITERAL(38, 717, 20) // "onSyncCommandTimeout"

    },
    "CommandTransceiverRefactored\0initialized\0"
    "\0connectionStateChanged\0ConnectionState\0"
    "state\0commandResponseReceived\0"
    "CommandResponse\0response\0commandReceived\0"
    "command\0uuid\0replyReceived\0reply\0"
    "errorOccurred\0error\0onNetworkWorkerInitialized\0"
    "onDataProcessorWorkerInitialized\0"
    "onNetworkWorkerCleanupCompleted\0"
    "onDataProcessorWorkerCleanupCompleted\0"
    "onNetworkConnectionStateChanged\0"
    "NetworkWorker::ConnectionState\0"
    "onNetworkRawDataReceived\0data\0"
    "onNetworkError\0NetworkWorker::NetworkError\0"
    "description\0onNetworkDataSent\0"
    "bytesWritten\0onNetworkDataSendFailed\0"
    "onDataProcessorCommandReceived\0"
    "onDataProcessorReplyReceived\0"
    "onDataProcessorJsonEncoded\0encodedData\0"
    "originalJson\0onDataProcessorError\0"
    "DataProcessorWorker::ProcessError\0"
    "errorString\0onSyncCommandTimeout"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CommandTransceiverRefactored[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      20,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,  114,    2, 0x06 /* Public */,
       3,    1,  115,    2, 0x06 /* Public */,
       6,    1,  118,    2, 0x06 /* Public */,
       9,    2,  121,    2, 0x06 /* Public */,
      12,    2,  126,    2, 0x06 /* Public */,
      14,    1,  131,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      16,    0,  134,    2, 0x08 /* Private */,
      17,    0,  135,    2, 0x08 /* Private */,
      18,    0,  136,    2, 0x08 /* Private */,
      19,    0,  137,    2, 0x08 /* Private */,
      20,    1,  138,    2, 0x08 /* Private */,
      22,    1,  141,    2, 0x08 /* Private */,
      24,    2,  144,    2, 0x08 /* Private */,
      27,    1,  149,    2, 0x08 /* Private */,
      29,    1,  152,    2, 0x08 /* Private */,
      30,    2,  155,    2, 0x08 /* Private */,
      31,    2,  160,    2, 0x08 /* Private */,
      32,    2,  165,    2, 0x08 /* Private */,
      35,    2,  170,    2, 0x08 /* Private */,
      38,    0,  175,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 4,    5,
    QMetaType::Void, 0x80000000 | 7,    8,
    QMetaType::Void, QMetaType::QJsonObject, QMetaType::QUuid,   10,   11,
    QMetaType::Void, QMetaType::QJsonObject, QMetaType::QUuid,   13,   11,
    QMetaType::Void, QMetaType::QString,   15,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 21,    5,
    QMetaType::Void, QMetaType::QByteArray,   23,
    QMetaType::Void, 0x80000000 | 25, QMetaType::QString,   15,   26,
    QMetaType::Void, QMetaType::LongLong,   28,
    QMetaType::Void, QMetaType::QString,   15,
    QMetaType::Void, QMetaType::QJsonObject, QMetaType::QUuid,   10,   11,
    QMetaType::Void, QMetaType::QJsonObject, QMetaType::QUuid,   13,   11,
    QMetaType::Void, QMetaType::QByteArray, QMetaType::QJsonObject,   33,   34,
    QMetaType::Void, 0x80000000 | 36, QMetaType::QString,   15,   37,
    QMetaType::Void,

       0        // eod
};

void CommandTransceiverRefactored::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<CommandTransceiverRefactored *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->initialized(); break;
        case 1: _t->connectionStateChanged((*reinterpret_cast< ConnectionState(*)>(_a[1]))); break;
        case 2: _t->commandResponseReceived((*reinterpret_cast< const CommandResponse(*)>(_a[1]))); break;
        case 3: _t->commandReceived((*reinterpret_cast< const QJsonObject(*)>(_a[1])),(*reinterpret_cast< const QUuid(*)>(_a[2]))); break;
        case 4: _t->replyReceived((*reinterpret_cast< const QJsonObject(*)>(_a[1])),(*reinterpret_cast< const QUuid(*)>(_a[2]))); break;
        case 5: _t->errorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 6: _t->onNetworkWorkerInitialized(); break;
        case 7: _t->onDataProcessorWorkerInitialized(); break;
        case 8: _t->onNetworkWorkerCleanupCompleted(); break;
        case 9: _t->onDataProcessorWorkerCleanupCompleted(); break;
        case 10: _t->onNetworkConnectionStateChanged((*reinterpret_cast< NetworkWorker::ConnectionState(*)>(_a[1]))); break;
        case 11: _t->onNetworkRawDataReceived((*reinterpret_cast< const QByteArray(*)>(_a[1]))); break;
        case 12: _t->onNetworkError((*reinterpret_cast< NetworkWorker::NetworkError(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 13: _t->onNetworkDataSent((*reinterpret_cast< qint64(*)>(_a[1]))); break;
        case 14: _t->onNetworkDataSendFailed((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 15: _t->onDataProcessorCommandReceived((*reinterpret_cast< const QJsonObject(*)>(_a[1])),(*reinterpret_cast< const QUuid(*)>(_a[2]))); break;
        case 16: _t->onDataProcessorReplyReceived((*reinterpret_cast< const QJsonObject(*)>(_a[1])),(*reinterpret_cast< const QUuid(*)>(_a[2]))); break;
        case 17: _t->onDataProcessorJsonEncoded((*reinterpret_cast< const QByteArray(*)>(_a[1])),(*reinterpret_cast< const QJsonObject(*)>(_a[2]))); break;
        case 18: _t->onDataProcessorError((*reinterpret_cast< DataProcessorWorker::ProcessError(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 19: _t->onSyncCommandTimeout(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 10:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< NetworkWorker::ConnectionState >(); break;
            }
            break;
        case 12:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< NetworkWorker::NetworkError >(); break;
            }
            break;
        case 18:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< DataProcessorWorker::ProcessError >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (CommandTransceiverRefactored::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandTransceiverRefactored::initialized)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (CommandTransceiverRefactored::*)(ConnectionState );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandTransceiverRefactored::connectionStateChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (CommandTransceiverRefactored::*)(const CommandResponse & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandTransceiverRefactored::commandResponseReceived)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (CommandTransceiverRefactored::*)(const QJsonObject & , const QUuid & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandTransceiverRefactored::commandReceived)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (CommandTransceiverRefactored::*)(const QJsonObject & , const QUuid & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandTransceiverRefactored::replyReceived)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (CommandTransceiverRefactored::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CommandTransceiverRefactored::errorOccurred)) {
                *result = 5;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject CommandTransceiverRefactored::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_CommandTransceiverRefactored.data,
    qt_meta_data_CommandTransceiverRefactored,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *CommandTransceiverRefactored::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CommandTransceiverRefactored::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CommandTransceiverRefactored.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int CommandTransceiverRefactored::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 20)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 20;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 20)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 20;
    }
    return _id;
}

// SIGNAL 0
void CommandTransceiverRefactored::initialized()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void CommandTransceiverRefactored::connectionStateChanged(ConnectionState _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void CommandTransceiverRefactored::commandResponseReceived(const CommandResponse & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void CommandTransceiverRefactored::commandReceived(const QJsonObject & _t1, const QUuid & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void CommandTransceiverRefactored::replyReceived(const QJsonObject & _t1, const QUuid & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void CommandTransceiverRefactored::errorOccurred(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
