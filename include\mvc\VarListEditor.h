#ifndef VAR_LIST_EDITOR_H
#define VAR_LIST_EDITOR_H

#include <QWidget>
#include <QListWidget>
#include <QPushButton>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QListWidgetItem>

/**
 * @brief 列表变量编辑器组件
 * 
 * 提供可视化的列表项管理功能：
 * - 显示列表项
 * - 添加/删除列表项
 * - 上移/下移列表项
 * - 编辑列表项
 * - 快速添加功能
 */
class VarListEditor : public QWidget
{
    Q_OBJECT

public:
    explicit VarListEditor(QWidget *parent = nullptr);
    ~VarListEditor() override = default;
    
    // ========== 数据接口 ==========
    
    /**
     * @brief 设置列表数据
     * @param list 字符串列表
     */
    void setStringList(const QStringList &list);
    
    /**
     * @brief 获取列表数据
     * @return 字符串列表
     */
    QStringList getStringList() const;
    
    /**
     * @brief 清空列表
     */
    void clear();
    
    // ========== 状态接口 ==========
    
    /**
     * @brief 设置只读模式
     * @param readOnly 是否只读
     */
    void setReadOnly(bool readOnly);
    
    /**
     * @brief 获取只读状态
     * @return 是否只读
     */
    bool isReadOnly() const { return m_readOnly; }
    
    /**
     * @brief 检查是否为空
     * @return 是否为空列表
     */
    bool isEmpty() const;
    
    /**
     * @brief 获取项目数量
     * @return 列表项数量
     */
    int itemCount() const;

signals:
    /**
     * @brief 列表内容变化信号
     */
    void listChanged();
    
    /**
     * @brief 项目数量变化信号
     * @param count 新的项目数量
     */
    void itemCountChanged(int count);

protected:
    // ========== 事件处理 ==========
    void keyPressEvent(QKeyEvent *event) override;
    bool eventFilter(QObject *watched, QEvent *event) override;

private slots:
    // ========== 按钮事件处理 ==========
    void onAddButtonClicked();
    void onDeleteButtonClicked();
    void onMoveUpButtonClicked();
    void onMoveDownButtonClicked();
    void onEditButtonClicked();
    void onClearButtonClicked();
    void onQuickAddButtonClicked();

    // ========== 列表事件处理 ==========
    void onListItemDoubleClicked(QListWidgetItem *item);
    void onListSelectionChanged();
    void onListItemChanged(QListWidgetItem *item);
    void onQuickAddReturnPressed();

private:
    // ========== UI初始化 ==========
    void setupUI();
    void connectSignals();
    
    // ========== 状态更新 ==========
    void updateButtonStates();
    void emitChangeSignals();
    
    // ========== 列表操作 ==========
    void addListItem(const QString &text);
    void editListItem(QListWidgetItem *item);
    int getCurrentRow() const;

    // ========== 安全性检查 ==========
    bool isItemValid(QListWidgetItem *item) const;
    
    // ========== UI组件 ==========
    
    // 主布局
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_contentLayout;
    
    // 列表显示区域
    QListWidget *m_listWidget;
    
    // 操作按钮区域
    QWidget *m_buttonWidget;
    QVBoxLayout *m_buttonLayout;
    QPushButton *m_addButton;
    QPushButton *m_deleteButton;
    QPushButton *m_moveUpButton;
    QPushButton *m_moveDownButton;
    QPushButton *m_editButton;
    QPushButton *m_clearButton;
    
    // 快速添加区域
    QWidget *m_quickAddWidget;
    QHBoxLayout *m_quickAddLayout;
    QLabel *m_quickAddLabel;
    QLineEdit *m_quickAddEdit;
    QPushButton *m_quickAddButton;
    
    // ========== 状态变量 ==========
    bool m_readOnly;
};

#endif // VAR_LIST_EDITOR_H
