/****************************************************************************
** Meta object code from reading C++ file 'ClientConnection.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../ClientConnection.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ClientConnection.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ClientConnection_t {
    QByteArrayData data[19];
    char stringdata0[285];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ClientConnection_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ClientConnection_t qt_meta_stringdata_ClientConnection = {
    {
QT_MOC_LITERAL(0, 0, 16), // "ClientConnection"
QT_MOC_LITERAL(1, 17, 22), // "connectionStateChanged"
QT_MOC_LITERAL(2, 40, 0), // ""
QT_MOC_LITERAL(3, 41, 12), // "connectionId"
QT_MOC_LITERAL(4, 54, 15), // "ConnectionState"
QT_MOC_LITERAL(5, 70, 5), // "state"
QT_MOC_LITERAL(6, 76, 19), // "jsonCommandReceived"
QT_MOC_LITERAL(7, 96, 7), // "command"
QT_MOC_LITERAL(8, 104, 15), // "rawDataReceived"
QT_MOC_LITERAL(9, 120, 4), // "data"
QT_MOC_LITERAL(10, 125, 15), // "connectionError"
QT_MOC_LITERAL(11, 141, 5), // "error"
QT_MOC_LITERAL(12, 147, 22), // "connectionDisconnected"
QT_MOC_LITERAL(13, 170, 17), // "onSocketReadyRead"
QT_MOC_LITERAL(14, 188, 20), // "onSocketDisconnected"
QT_MOC_LITERAL(15, 209, 13), // "onSocketError"
QT_MOC_LITERAL(16, 223, 28), // "QAbstractSocket::SocketError"
QT_MOC_LITERAL(17, 252, 18), // "onHeartbeatTimeout"
QT_MOC_LITERAL(18, 271, 13) // "sendHeartbeat"

    },
    "ClientConnection\0connectionStateChanged\0"
    "\0connectionId\0ConnectionState\0state\0"
    "jsonCommandReceived\0command\0rawDataReceived\0"
    "data\0connectionError\0error\0"
    "connectionDisconnected\0onSocketReadyRead\0"
    "onSocketDisconnected\0onSocketError\0"
    "QAbstractSocket::SocketError\0"
    "onHeartbeatTimeout\0sendHeartbeat"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ClientConnection[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      10,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   64,    2, 0x06 /* Public */,
       6,    2,   69,    2, 0x06 /* Public */,
       8,    2,   74,    2, 0x06 /* Public */,
      10,    2,   79,    2, 0x06 /* Public */,
      12,    1,   84,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      13,    0,   87,    2, 0x08 /* Private */,
      14,    0,   88,    2, 0x08 /* Private */,
      15,    1,   89,    2, 0x08 /* Private */,
      17,    0,   92,    2, 0x08 /* Private */,
      18,    0,   93,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QUuid, 0x80000000 | 4,    3,    5,
    QMetaType::Void, QMetaType::QUuid, QMetaType::QJsonObject,    3,    7,
    QMetaType::Void, QMetaType::QUuid, QMetaType::QByteArray,    3,    9,
    QMetaType::Void, QMetaType::QUuid, QMetaType::QString,    3,   11,
    QMetaType::Void, QMetaType::QUuid,    3,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 16,   11,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void ClientConnection::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ClientConnection *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->connectionStateChanged((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< ConnectionState(*)>(_a[2]))); break;
        case 1: _t->jsonCommandReceived((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< const QJsonObject(*)>(_a[2]))); break;
        case 2: _t->rawDataReceived((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< const QByteArray(*)>(_a[2]))); break;
        case 3: _t->connectionError((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 4: _t->connectionDisconnected((*reinterpret_cast< const QUuid(*)>(_a[1]))); break;
        case 5: _t->onSocketReadyRead(); break;
        case 6: _t->onSocketDisconnected(); break;
        case 7: _t->onSocketError((*reinterpret_cast< QAbstractSocket::SocketError(*)>(_a[1]))); break;
        case 8: _t->onHeartbeatTimeout(); break;
        case 9: _t->sendHeartbeat(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 7:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QAbstractSocket::SocketError >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ClientConnection::*)(const QUuid & , ConnectionState );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ClientConnection::connectionStateChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ClientConnection::*)(const QUuid & , const QJsonObject & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ClientConnection::jsonCommandReceived)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ClientConnection::*)(const QUuid & , const QByteArray & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ClientConnection::rawDataReceived)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ClientConnection::*)(const QUuid & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ClientConnection::connectionError)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ClientConnection::*)(const QUuid & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ClientConnection::connectionDisconnected)) {
                *result = 4;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ClientConnection::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_ClientConnection.data,
    qt_meta_data_ClientConnection,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ClientConnection::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ClientConnection::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ClientConnection.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ClientConnection::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    }
    return _id;
}

// SIGNAL 0
void ClientConnection::connectionStateChanged(const QUuid & _t1, ConnectionState _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ClientConnection::jsonCommandReceived(const QUuid & _t1, const QJsonObject & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ClientConnection::rawDataReceived(const QUuid & _t1, const QByteArray & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ClientConnection::connectionError(const QUuid & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void ClientConnection::connectionDisconnected(const QUuid & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
