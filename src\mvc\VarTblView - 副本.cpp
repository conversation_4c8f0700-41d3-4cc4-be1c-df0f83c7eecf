/**
 * @file VarTblView.cpp
 * @brief VarTbl表格视图实现
 * 
 * <AUTHOR> Framework
 * @date 2025-01-23
 */

#include "mvc/VarTblView.h"
#include <QDebug>
#include <QMessageBox>
#include <QFileDialog>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QTextStream>
#include <QApplication>
#include <QClipboard>
#include <QKeySequence>
#include <QShortcut>
#include <QTimer>

VarTblView::VarTblView(QWidget *parent)
    : QWidget(parent)
    , m_model(nullptr)
    , m_controller(nullptr)
    , m_readOnly(false)
    , m_tableEditMode(false)
    , m_autoRefreshTimer(new QTimer(this))
    , m_autoRefreshEnabled(false)
{
    qDebug() << "VarTblView: 创建实例";
    
    setupUI();
    connectSignals();
    setupTableEditDelegate();

    // 设置自动刷新定时器
    m_autoRefreshTimer->setSingleShot(false);
    connect(m_autoRefreshTimer, &QTimer::timeout, this, &VarTblView::onAutoRefreshTimeout);
}

VarTblView::~VarTblView()
{
    qDebug() << "VarTblView: 销毁实例";
}

// ========== 组件设置 ==========

void VarTblView::setModel(VarTblModel *model)
{
    if (m_model == model) {
        return;
    }
    
    m_model = model;

    if (m_tableView && m_model) {
        m_tableView->setModel(m_model);

        // 连接选择变化信号
        if (m_tableView->selectionModel()) {
            connect(m_tableView->selectionModel(), &QItemSelectionModel::selectionChanged,
                    this, &VarTblView::onTableSelectionChanged);
        }

        // 设置列宽
        m_tableView->setColumnWidth(VarTblModel::ColumnId, 80);
        m_tableView->setColumnWidth(VarTblModel::ColumnLabel, 150);
        m_tableView->setColumnWidth(VarTblModel::ColumnType, 100);
        m_tableView->setColumnWidth(VarTblModel::ColumnValue, 200);
        m_tableView->setColumnWidth(VarTblModel::ColumnLastModified, 150);

        // 同步表格编辑模式状态到新模型
        m_model->setTableEditMode(m_tableEditMode);
    }
    
    updateStatusBar();
    qDebug() << "VarTblView: 设置数据模型" << model;
}

void VarTblView::setController(VarTblController *controller)
{
    if (m_controller == controller) {
        return;
    }
    
    if (m_controller) {
        disconnect(m_controller, nullptr, this, nullptr);
    }
    
    m_controller = controller;
    
    if (m_controller) {
        // 连接View操作信号到Controller槽
        connect(this, &VarTblView::addVariableRequested,
                m_controller, &VarTblController::onAddVariableRequested);
        connect(this, &VarTblView::editVariableRequested,
                m_controller, &VarTblController::onEditVariableRequested);
        connect(this, &VarTblView::deleteVariableRequested,
                m_controller, &VarTblController::onDeleteVariableRequested);
        connect(this, &VarTblView::dataSubmitted,
                m_controller, &VarTblController::onDataSubmitted);
        connect(this, &VarTblView::editCancelled,
                m_controller, &VarTblController::onEditCancelled);
        connect(this, &VarTblView::refreshDataRequested,
                m_controller, &VarTblController::onRefreshDataRequested);
        connect(this, &VarTblView::syncDataRequested,
                m_controller, &VarTblController::onSyncDataRequested);
        connect(this, &VarTblView::connectToServerRequested,
                m_controller, &VarTblController::onConnectToServerRequested);
        connect(this, &VarTblView::disconnectFromServerRequested,
                m_controller, &VarTblController::onDisconnectFromServerRequested);

        // 编辑模式变化信号连接已移除，现在只使用表格编辑模式
        connect(m_controller, QOverload<bool, const QString&>::of(&VarTblController::operationCompleted),
                this, &VarTblView::onOperationCompleted);
        connect(m_controller, &VarTblController::connectionStatusChanged,
                this, &VarTblView::onConnectionStatusChanged);
        connect(m_controller, QOverload<bool>::of(&VarTblController::syncStatusChanged),
                this, &VarTblView::onSyncStatusChanged);
        connect(m_controller, &VarTblController::busyStatusChanged,
                this, &VarTblView::onBusyStatusChanged);
        connect(m_controller, &VarTblController::showMessage,
                this, &VarTblView::onShowMessage);
    }
    
    updateToolbarState();
    updateConnectionStatus(m_controller ? m_controller->isConnected() : false);
    
    qDebug() << "VarTblView: 设置控制器" << controller;
}

// ========== UI配置 ==========

void VarTblView::setToolbarVisible(bool visible)
{
    if (m_toolbar) {
        m_toolbar->setVisible(visible);
    }
}

// setEditPanelVisible方法已移除，现在只使用表格编辑模式

void VarTblView::setStatusBarVisible(bool visible)
{
    if (m_statusWidget) {
        m_statusWidget->setVisible(visible);
    }
}

void VarTblView::setAutoRefresh(bool enabled, int intervalMs)
{
    m_autoRefreshEnabled = enabled;
    
    if (enabled && intervalMs > 0) {
        m_autoRefreshTimer->start(intervalMs);
        qDebug() << "VarTblView: 启用自动刷新，间隔" << intervalMs << "毫秒";
    } else {
        m_autoRefreshTimer->stop();
        qDebug() << "VarTblView: 禁用自动刷新";
    }
}

void VarTblView::setReadOnly(bool readOnly)
{
    m_readOnly = readOnly;
    updateToolbarState();
    
    if (m_tableView) {
        m_tableView->setEditTriggers(readOnly ? QAbstractItemView::NoEditTriggers : 
                                               QAbstractItemView::DoubleClicked);
    }
    
    qDebug() << "VarTblView: 设置只读模式" << readOnly;
}

// ========== 选择和导航 ==========

QList<int> VarTblView::getSelectedVariableIds() const
{
    QList<int> ids;
    
    if (!m_tableView || !m_model) {
        return ids;
    }
    
    QItemSelectionModel *selectionModel = m_tableView->selectionModel();
    if (!selectionModel) {
        return ids;
    }
    
    QModelIndexList selectedRows = selectionModel->selectedRows();
    for (const QModelIndex &index : selectedRows) {
        int id = m_model->getVariableIdByRow(index.row());
        if (id >= 0) {
            ids.append(id);
        }
    }
    
    return ids;
}

void VarTblView::selectVariable(int id)
{
    if (!m_tableView || !m_model) {
        return;
    }
    
    int row = m_model->getRowByVariableId(id);
    if (row >= 0) {
        QModelIndex index = m_model->index(row, 0);
        m_tableView->selectionModel()->select(index, QItemSelectionModel::ClearAndSelect | QItemSelectionModel::Rows);
        m_tableView->scrollTo(index);
    }
}

void VarTblView::clearSelection()
{
    if (m_tableView && m_tableView->selectionModel()) {
        m_tableView->selectionModel()->clearSelection();
    }
}

// ========== 表格编辑模式控制 ==========

void VarTblView::setTableEditMode(bool enabled)
{
    if (m_tableEditMode != enabled) {
        m_tableEditMode = enabled;
        updateTableEditMode();
        qDebug() << "VarTblView: 设置表格编辑模式为" << enabled;
    }
}

void VarTblView::toggleTableEditMode()
{
    setTableEditMode(!m_tableEditMode);
}

// ========== 数据导入导出 ==========

bool VarTblView::exportData(const QString &filePath, const QString &format)
{
    if (!m_model) {
        showError(tr("数据模型未设置"));
        return false;
    }
    
    try {
        if (format.toLower() == "json") {
            // 导出为JSON格式
            cmd::VarTbl varTbl = m_model->getVarTbl();
            QJsonObject jsonObj = varTbl.toJsonObj();
            
            QJsonDocument doc(jsonObj);
            QByteArray data = doc.toJson();
            
            QFile file(filePath);
            if (!file.open(QIODevice::WriteOnly)) {
                showError(tr("无法创建文件: %1").arg(file.errorString()));
                return false;
            }
            
            file.write(data);
            file.close();
            
            showSuccess(tr("数据导出成功"));
            return true;
        } else if (format.toLower() == "csv") {
            // 导出为CSV格式
            QFile file(filePath);
            if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                showError(tr("无法创建文件: %1").arg(file.errorString()));
                return false;
            }
            
            QTextStream stream(&file);
            stream.setCodec("UTF-8");
            
            // 写入表头
            stream << "ID,标签,类型,值,最后修改时间\n";
            
            // 写入数据
            cmd::VarTbl varTbl = m_model->getVarTbl();
            for (auto it = varTbl.cbegin(); it != varTbl.cend(); ++it) {
                int id = it->first;
                const auto &item = it->second;
                
                QString value;
                if (item.obj.isString()) {
                    value = QString("\"%1\"").arg(item.obj.toString().replace("\"", "\"\""));
                } else if (item.obj.isList()) {
                    QStringList list = item.obj.toList();
                    value = QString("\"[%1]\"").arg(list.join(", "));
                }
                
                QString escapedLabel = item.label;
                escapedLabel.replace("\"", "\"\"");

                stream << QString("%1,\"%2\",\"%3\",%4,\"%5\"\n")
                          .arg(id)
                          .arg(escapedLabel)
                          .arg(getVariableTypeString(item.obj))
                          .arg(value)
                          .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
            }
            
            file.close();
            showSuccess(tr("数据导出成功"));
            return true;
        } else {
            showError(tr("不支持的导出格式: %1").arg(format));
            return false;
        }
    } catch (const std::exception &e) {
        showError(tr("导出失败: %1").arg(e.what()));
        return false;
    }
}

bool VarTblView::importData(const QString &filePath, const QString &format)
{
    if (!m_controller) {
        showError(tr("控制器未设置"));
        return false;
    }
    
    try {
        if (format.toLower() == "json") {
            // 从JSON格式导入
            QFile file(filePath);
            if (!file.open(QIODevice::ReadOnly)) {
                showError(tr("无法打开文件: %1").arg(file.errorString()));
                return false;
            }
            
            QByteArray data = file.readAll();
            file.close();
            
            QJsonParseError error;
            QJsonDocument doc = QJsonDocument::fromJson(data, &error);
            if (error.error != QJsonParseError::NoError) {
                showError(tr("JSON解析失败: %1").arg(error.errorString()));
                return false;
            }
            
            cmd::VarTbl varTbl(doc.object());
            m_controller->updateVariables(varTbl, false); // 不同步到服务器
            
            showSuccess(tr("数据导入成功"));
            return true;
        } else {
            showError(tr("不支持的导入格式: %1").arg(format));
            return false;
        }
    } catch (const std::exception &e) {
        showError(tr("导入失败: %1").arg(e.what()));
        return false;
    }
}

// ========== UI事件处理 ==========

void VarTblView::onAddButtonClicked()
{
    if (m_readOnly || !m_tableEditMode) {
        showMessage(tr("请先启用表格编辑模式"));
        return;
    }

    // 在表格编辑模式下，发出添加请求信号，由Controller处理
    emit addVariableRequested();

    qDebug() << "VarTblView: 在表格编辑模式下请求添加新变量";
}

void VarTblView::onEditButtonClicked()
{
    if (m_readOnly || !m_tableEditMode) {
        showMessage(tr("请先启用表格编辑模式"));
        return;
    }

    QList<int> selectedIds = getSelectedVariableIds();
    if (selectedIds.size() == 1) {
        // 在表格编辑模式下，定位到选中的变量并开始编辑
        int id = selectedIds.first();
        int row = m_model->getRowByVariableId(id);
        if (row >= 0 && m_tableView) {
            QModelIndex editIndex = m_model->index(row, VarTblModel::ColumnLabel);
            m_tableView->setCurrentIndex(editIndex);
            m_tableView->edit(editIndex);
            showMessage(tr("请在表格中编辑变量"));
        }
    } else {
        showMessage(tr("请选择一个变量进行编辑"));
    }
}

void VarTblView::onDeleteButtonClicked()
{
    if (m_readOnly || !m_tableEditMode) {
        showMessage(tr("请先启用表格编辑模式"));
        return;
    }

    QList<int> selectedIds = getSelectedVariableIds();
    if (selectedIds.isEmpty()) {
        showMessage(tr("请选择要删除的变量"));
        return;
    }

    // 使用非阻塞的确认对话框，避免可能的卡死
    QMessageBox *msgBox = new QMessageBox(this);
    msgBox->setWindowTitle(tr("确认删除"));
    msgBox->setText(tr("确定要删除选中的 %1 个变量吗？").arg(selectedIds.size()));
    msgBox->setStandardButtons(QMessageBox::Yes | QMessageBox::No);
    msgBox->setDefaultButton(QMessageBox::No);
    msgBox->setAttribute(Qt::WA_DeleteOnClose);

    // 使用非阻塞方式显示对话框
    connect(msgBox, &QMessageBox::finished, this, [this, selectedIds](int result) {
        if (result == QMessageBox::Yes) {
            // 在表格编辑模式下，发出删除请求信号，由Controller处理
            emit deleteVariableRequested(selectedIds);
            qDebug() << "VarTblView: 在表格编辑模式下请求删除变量" << selectedIds;
        }
    });

    msgBox->show();
}

void VarTblView::onRefreshButtonClicked()
{
    emit refreshDataRequested();
}

void VarTblView::onSyncButtonClicked()
{
    emit syncDataRequested();
}

void VarTblView::onImportButtonClicked()
{
    QString filePath = QFileDialog::getOpenFileName(this, tr("导入数据"), 
                                                   QString(), tr("JSON文件 (*.json);;CSV文件 (*.csv)"));
    if (!filePath.isEmpty()) {
        QString format = filePath.endsWith(".csv", Qt::CaseInsensitive) ? "csv" : "json";
        importData(filePath, format);
    }
}

void VarTblView::onExportButtonClicked()
{
    QString filePath = QFileDialog::getSaveFileName(this, tr("导出数据"), 
                                                   QString(), tr("JSON文件 (*.json);;CSV文件 (*.csv)"));
    if (!filePath.isEmpty()) {
        QString format = filePath.endsWith(".csv", Qt::CaseInsensitive) ? "csv" : "json";
        exportData(filePath, format);
    }
}

void VarTblView::onConnectButtonClicked()
{
    // 发出连接请求信号，由Controller处理连接逻辑
    emit connectToServerRequested();
}

void VarTblView::onDisconnectButtonClicked()
{
    emit disconnectFromServerRequested();
}

void VarTblView::onTableEditButtonClicked()
{
    m_tableEditMode = !m_tableEditMode;
    updateTableEditMode();
    qDebug() << "VarTblView: 切换表格编辑模式为" << m_tableEditMode;
}

// ========== 表格事件 ==========

void VarTblView::onTableSelectionChanged()
{
    QList<int> selectedIds = getSelectedVariableIds();
    emit selectionChanged(selectedIds);
    updateToolbarState();
}

void VarTblView::onTableDoubleClicked(const QModelIndex &index)
{
    // 双击编辑现在由表格编辑模式处理
    // 如果未启用表格编辑模式，则不响应双击
    if (m_readOnly || !index.isValid() || !m_tableEditMode) {
        return;
    }

    // 在表格编辑模式下，双击会自动触发单元格编辑
    // 这里不需要额外处理，QTableView会自动处理
    qDebug() << "VarTblView: 表格编辑模式下的双击事件，行=" << index.row() << "列=" << index.column();
}

void VarTblView::onTableContextMenuRequested(const QPoint &pos)
{
    if (!m_contextMenu) {
        return;
    }
    
    QModelIndex index = m_tableView->indexAt(pos);
    if (index.isValid()) {
        m_contextMenu->exec(m_tableView->mapToGlobal(pos));
    }
}

// 编辑面板事件处理已移除，现在只使用表格编辑模式

// ========== 表格编辑相关槽 ==========

void VarTblView::onItemDelegateListEditingFinished(const QModelIndex& index, const QStringList& newList)
{
    if (!m_model || !index.isValid()) {
        return;
    }

    // 获取变量ID和相关信息
    int row = index.row();
    int id = m_model->getVariableIdByRow(row);
    if (id < 0) {
        showError(tr("无法获取变量ID"));
        return;
    }

    // 获取当前变量的标签
    QString label;
    cmd::Var currentVar;
    if (!m_model->getVariable(id, currentVar, label)) {
        showError(tr("无法获取变量信息"));
        return;
    }

    // 通过Controller处理列表数据更新
    emit dataSubmitted(id, label, tr("列表"), QVariant(newList));

    qDebug() << "VarTblView: 列表编辑完成，ID=" << id << "新数据=" << newList;
}

void VarTblView::onItemDelegateCellDataChanged(const QModelIndex& index, const QVariant& newValue)
{
    if (!m_model || !index.isValid()) {
        return;
    }

    int row = index.row();
    int column = index.column();
    int id = m_model->getVariableIdByRow(row);

    if (id < 0) {
        showError(tr("无法获取变量ID"));
        return;
    }

    // 获取当前变量的完整信息
    QString currentLabel;
    cmd::Var currentVar;
    if (!m_model->getVariable(id, currentVar, currentLabel)) {
        showError(tr("无法获取变量信息"));
        return;
    }

    // 根据编辑的列，构造完整的数据提交请求
    QString label = currentLabel;
    QString typeText = getVariableTypeString(currentVar);
    QVariant value = currentVar.isString() ? QVariant(currentVar.toString()) : QVariant(currentVar.toList());
    int newId = id;

    switch (column) {
    case VarTblModel::ColumnId:
        newId = newValue.toInt();
        break;
    case VarTblModel::ColumnLabel:
        label = newValue.toString();
        break;
    case VarTblModel::ColumnType:
        typeText = newValue.toString();
        // 类型变化时，需要重置值
        if (typeText == tr("字符串")) {
            value = QString("");
        } else if (typeText == tr("列表")) {
            value = QStringList();
        }
        break;
    case VarTblModel::ColumnValue:
        value = newValue;
        break;
    }

    // 通过Controller处理数据更新
    if (newId != id) {
        // ID变化，需要特殊处理
        emit dataSubmitted(newId, label, typeText, value);
        // 删除旧ID的变量
        emit deleteVariableRequested(QList<int>() << id);
    } else {
        // 普通更新
        emit dataSubmitted(id, label, typeText, value);
    }

    qDebug() << "VarTblView: 单元格数据变化，ID=" << id << "列=" << column << "新值=" << newValue;
}

void VarTblView::onItemDelegateEditingError(const QString& message)
{
    showError(message);
}

// ========== Controller UI状态更新槽 ==========

void VarTblView::onOperationCompleted(bool success, const QString &message)
{
    if (success) {
        showSuccess(message);
    } else {
        showError(message);
    }

    updateStatusBar();
    updateToolbarState();
}

void VarTblView::onConnectionStatusChanged(bool connected)
{
    updateConnectionStatus(connected);
    updateToolbarState();
}

void VarTblView::onSyncStatusChanged(bool syncing)
{
    if (m_progressBar) {
        m_progressBar->setVisible(syncing);
    }

    updateStatusBar();
}

void VarTblView::onBusyStatusChanged(bool busy)
{
    // 可以在这里禁用/启用相关UI组件
    if (m_toolbar) {
        m_toolbar->setEnabled(!busy);
    }

    // 更新鼠标光标
    if (busy) {
        setCursor(Qt::WaitCursor);
    } else {
        unsetCursor();
    }
}

void VarTblView::onShowMessage(const QString &message, bool isError)
{
    if (isError) {
        showError(message);
    } else {
        showMessage(message);
    }
}

void VarTblView::onAutoRefreshTimeout()
{
    if (m_autoRefreshEnabled && m_controller && m_controller->isConnected()) {
        emit refreshDataRequested();
    }
}

// ========== UI初始化 ==========

void VarTblView::setupUI()
{
    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(5);

    // 创建主分割器
    m_mainSplitter = new QSplitter(Qt::Vertical, this);
    m_mainLayout->addWidget(m_mainSplitter);

    setupToolbar();
    setupTableView();
    setupStatusBar();
    setupContextMenu();

    // 设置分割器比例
    m_mainSplitter->setStretchFactor(0, 1); // 表格占主要空间
    m_mainSplitter->setStretchFactor(1, 0); // 编辑面板固定高度
}

void VarTblView::setupToolbar()
{
    m_toolbar = new QToolBar(this);
    m_toolbar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    m_mainLayout->insertWidget(0, m_toolbar);

    // 创建操作按钮
    m_addAction = m_toolbar->addAction(tr("添加"));
    m_addAction->setIcon(QIcon(":/icons/add.png"));
    m_addAction->setShortcut(QKeySequence::New);
    m_addAction->setEnabled(false);  // 初始状态禁用，需要启用表格编辑模式

    m_editAction = m_toolbar->addAction(tr("编辑"));
    m_editAction->setIcon(QIcon(":/icons/edit.png"));
    m_editAction->setEnabled(false);

    m_deleteAction = m_toolbar->addAction(tr("删除"));
    m_deleteAction->setIcon(QIcon(":/icons/delete.png"));
    m_deleteAction->setShortcut(QKeySequence::Delete);
    m_deleteAction->setEnabled(false);

    m_toolbar->addSeparator();

    // 表格编辑模式切换按钮
    m_tableEditAction = m_toolbar->addAction(tr("表格编辑"));
    m_tableEditAction->setIcon(QIcon(":/icons/table_edit.png"));
    m_tableEditAction->setCheckable(true);
    m_tableEditAction->setChecked(false);
    m_tableEditAction->setToolTip(tr("启用/禁用表格内直接编辑"));

    m_toolbar->addSeparator();

    m_refreshAction = m_toolbar->addAction(tr("刷新"));
    m_refreshAction->setIcon(QIcon(":/icons/refresh.png"));
    m_refreshAction->setShortcut(QKeySequence::Refresh);

    m_syncAction = m_toolbar->addAction(tr("同步"));
    m_syncAction->setIcon(QIcon(":/icons/sync.png"));

    m_toolbar->addSeparator();

    m_importAction = m_toolbar->addAction(tr("导入"));
    m_importAction->setIcon(QIcon(":/icons/import.png"));

    m_exportAction = m_toolbar->addAction(tr("导出"));
    m_exportAction->setIcon(QIcon(":/icons/export.png"));

    m_toolbar->addSeparator();

    m_connectAction = m_toolbar->addAction(tr("连接"));
    m_connectAction->setIcon(QIcon(":/icons/connect.png"));

    m_disconnectAction = m_toolbar->addAction(tr("断开"));
    m_disconnectAction->setIcon(QIcon(":/icons/disconnect.png"));
    m_disconnectAction->setEnabled(false);
}

void VarTblView::setupTableView()
{
    m_tableView = new QTableView(this);
    m_mainSplitter->addWidget(m_tableView);

    // 设置表格属性
    m_tableView->setAlternatingRowColors(true);
    m_tableView->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_tableView->setSelectionMode(QAbstractItemView::ExtendedSelection);
    m_tableView->setSortingEnabled(true);
    m_tableView->setContextMenuPolicy(Qt::CustomContextMenu);

    // 设置表头
    QHeaderView *horizontalHeader = m_tableView->horizontalHeader();
    horizontalHeader->setStretchLastSection(true);
    horizontalHeader->setSectionResizeMode(QHeaderView::Interactive);

    QHeaderView *verticalHeader = m_tableView->verticalHeader();
    verticalHeader->setVisible(false);
}

// setupEditPanel方法已移除，现在只使用表格编辑模式

void VarTblView::setupStatusBar()
{
    m_statusWidget = new QWidget(this);
    m_statusLayout = new QHBoxLayout(m_statusWidget);
    m_statusLayout->setContentsMargins(5, 2, 5, 2);
    m_mainLayout->addWidget(m_statusWidget);

    m_statusLabel = new QLabel(tr("就绪"), m_statusWidget);
    m_statusLayout->addWidget(m_statusLabel);

    m_progressBar = new QProgressBar(m_statusWidget);
    m_progressBar->setVisible(false);
    m_progressBar->setRange(0, 0); // 无限进度条
    m_statusLayout->addWidget(m_progressBar);

    m_statusLayout->addStretch();

    m_countLabel = new QLabel(tr("变量: 0"), m_statusWidget);
    m_statusLayout->addWidget(m_countLabel);

    m_connectionLabel = new QLabel(tr("未连接"), m_statusWidget);
    m_connectionLabel->setStyleSheet("color: red;");
    m_statusLayout->addWidget(m_connectionLabel);
}

void VarTblView::setupContextMenu()
{
    m_contextMenu = new QMenu(this);

    m_contextMenu->addAction(m_addAction);
    m_contextMenu->addAction(m_editAction);
    m_contextMenu->addAction(m_deleteAction);
    m_contextMenu->addSeparator();
    m_contextMenu->addAction(m_refreshAction);
    m_contextMenu->addAction(m_syncAction);
}

void VarTblView::connectSignals()
{
    // 工具栏信号
    connect(m_addAction, &QAction::triggered, this, &VarTblView::onAddButtonClicked);
    connect(m_editAction, &QAction::triggered, this, &VarTblView::onEditButtonClicked);
    connect(m_deleteAction, &QAction::triggered, this, &VarTblView::onDeleteButtonClicked);
    connect(m_refreshAction, &QAction::triggered, this, &VarTblView::onRefreshButtonClicked);
    connect(m_syncAction, &QAction::triggered, this, &VarTblView::onSyncButtonClicked);
    connect(m_importAction, &QAction::triggered, this, &VarTblView::onImportButtonClicked);
    connect(m_exportAction, &QAction::triggered, this, &VarTblView::onExportButtonClicked);
    connect(m_connectAction, &QAction::triggered, this, &VarTblView::onConnectButtonClicked);
    connect(m_disconnectAction, &QAction::triggered, this, &VarTblView::onDisconnectButtonClicked);
    connect(m_tableEditAction, &QAction::triggered, this, &VarTblView::onTableEditButtonClicked);

    // 表格信号
    if (m_tableView) {
        connect(m_tableView, &QTableView::doubleClicked, this, &VarTblView::onTableDoubleClicked);
        connect(m_tableView, &QTableView::customContextMenuRequested, this, &VarTblView::onTableContextMenuRequested);
    }

    // 编辑面板信号连接已移除，现在只使用表格编辑模式
}

void VarTblView::setupTableEditDelegate()
{
    // 创建表格编辑器代理
    m_itemDelegate = new VarTblItemDelegate(this);

    // 连接代理信号
    connect(m_itemDelegate, &VarTblItemDelegate::listEditingFinished,
            this, &VarTblView::onItemDelegateListEditingFinished);
    connect(m_itemDelegate, &VarTblItemDelegate::cellDataChanged,
            this, &VarTblView::onItemDelegateCellDataChanged);
    connect(m_itemDelegate, &VarTblItemDelegate::editingError,
            this, &VarTblView::onItemDelegateEditingError);

    // 设置代理到表格视图
    if (m_tableView) {
        m_tableView->setItemDelegate(m_itemDelegate);
    }

    qDebug() << "VarTblView: 表格编辑代理设置完成";
}

// ========== UI更新 ==========

void VarTblView::updateToolbarState()
{
    QList<int> selectedIds = getSelectedVariableIds();
    bool hasSelection = !selectedIds.isEmpty();
    bool singleSelection = selectedIds.size() == 1;
    bool connected = m_controller ? m_controller->isConnected() : false;

    // 编辑相关按钮只有在表格编辑模式下才可用
    bool canEdit = !m_readOnly && m_tableEditMode;

    // 编辑和删除按钮需要选择项且在表格编辑模式下
    m_editAction->setEnabled(canEdit && singleSelection);
    m_deleteAction->setEnabled(canEdit && hasSelection);

    // 添加按钮只在表格编辑模式下可用
    m_addAction->setEnabled(canEdit);

    // 刷新和同步需要连接
    m_refreshAction->setEnabled(connected);
    m_syncAction->setEnabled(connected);

    // 连接状态按钮
    m_connectAction->setEnabled(!connected);
    m_disconnectAction->setEnabled(connected);

    // 表格编辑按钮始终可用（除非只读）
    if (m_tableEditAction) {
        m_tableEditAction->setEnabled(!m_readOnly);
    }
}

// updateEditPanel方法已移除，现在只使用表格编辑模式

void VarTblView::updateStatusBar()
{
    if (!m_statusLabel || !m_countLabel) {
        return;
    }

    // 更新变量计数
    int count = m_model ? m_model->getVariableCount() : 0;
    m_countLabel->setText(tr("变量: %1").arg(count));

    // 更新状态信息
    if (m_controller) {
        VarTblController::SyncStatus syncStatus = m_controller->getSyncStatus();
        switch (syncStatus) {
        case VarTblController::SyncStatus::Idle:
            m_statusLabel->setText(tr("就绪"));
            break;
        case VarTblController::SyncStatus::Syncing:
            m_statusLabel->setText(tr("同步中..."));
            break;
        case VarTblController::SyncStatus::Error:
            m_statusLabel->setText(tr("同步错误"));
            break;
        }
    } else {
        m_statusLabel->setText(tr("控制器未设置"));
    }
}

void VarTblView::updateConnectionStatus(bool connected)
{
    if (!m_connectionLabel) {
        return;
    }

    if (connected) {
        m_connectionLabel->setText(tr("已连接"));
        m_connectionLabel->setStyleSheet("color: green;");
    } else {
        m_connectionLabel->setText(tr("未连接"));
        m_connectionLabel->setStyleSheet("color: red;");
    }
}

void VarTblView::updateTableEditMode()
{
    if (!m_itemDelegate || !m_tableEditAction) {
        return;
    }

    // 更新代理的编辑状态
    m_itemDelegate->setEditingEnabled(m_tableEditMode);

    // 更新模型的编辑状态
    if (m_model) {
        m_model->setTableEditMode(m_tableEditMode);
    }

    // 更新按钮状态
    m_tableEditAction->setChecked(m_tableEditMode);

    // 更新工具栏按钮的启用状态
    updateToolbarState();

    // 更新表格外观
    if (m_tableView) {
        if (m_tableEditMode) {
            // 编辑模式：设置不同的样式表示可编辑
            m_tableView->setStyleSheet("QTableView { background-color: #f0f8ff; }");
            m_tableView->setToolTip(tr("表格编辑模式已启用，双击单元格进行编辑"));
        } else {
            // 只读模式：恢复默认样式
            m_tableView->setStyleSheet("");
            m_tableView->setToolTip(tr("表格编辑模式已禁用"));
        }
    }

    // 更新状态栏信息
    if (m_statusLabel) {
        if (m_tableEditMode) {
            m_statusLabel->setText(tr("表格编辑模式已启用"));
        } else {
            m_statusLabel->setText(tr("表格编辑模式已禁用"));
        }
    }

    qDebug() << "VarTblView: 表格编辑模式更新为" << m_tableEditMode;
}

// 编辑功能已移除，现在只使用表格编辑模式

// 编辑面板相关方法已移除，现在只使用表格编辑模式

// 编辑器相关方法已移除，现在只使用表格编辑模式

// setValueToEditorDelayed方法已移除，现在只使用表格编辑模式

// ========== 辅助方法 ==========

QString VarTblView::formatVariableValue(const cmd::Var &var) const
{
    if (var.isString()) {
        QString str = var.toString();
        if (str.length() > 50) {
            return str.left(47) + "...";
        }
        return str;
    } else if (var.isList()) {
        QStringList list = var.toList();
        if (list.isEmpty()) {
            return tr("[]");
        } else if (list.size() == 1) {
            return QString("[%1]").arg(list.first());
        } else {
            return QString("[%1, ...] (%2项)").arg(list.first()).arg(list.size());
        }
    }

    return tr("未知");
}

QString VarTblView::getVariableTypeString(const cmd::Var &var) const
{
    switch (var.type()) {
    case cmd::Var::Type::String:
        return tr("字符串");
    case cmd::Var::Type::List:
        return tr("列表");
    default:
        return tr("未知");
    }
}

// stringToVariableType方法已移除 - 类型转换逻辑已移至Controller层

void VarTblView::showMessage(const QString &message, int timeout)
{
    if (m_statusLabel) {
        QString originalText = m_statusLabel->text();
        m_statusLabel->setText(message);

        // 使用定时器恢复原始文本
        QTimer::singleShot(timeout, this, [this, originalText]() {
            if (m_statusLabel) {
                m_statusLabel->setText(originalText);
            }
        });
    }

    qDebug() << "VarTblView: 显示消息:" << message;
}

void VarTblView::showError(const QString &error)
{
    // 使用非阻塞的状态栏消息替代模态对话框，避免可能的卡死
    showMessage(tr("错误: %1").arg(error), 5000);

    // 可选：在调试模式下仍然使用模态对话框
    #ifdef QT_DEBUG
    // 使用QTimer延迟显示，避免在信号槽处理中直接显示模态对话框
    QTimer::singleShot(0, this, [this, error]() {
        QMessageBox::warning(this, tr("错误"), error);
    });
    #endif

    qDebug() << "VarTblView: 显示错误:" << error;
}

void VarTblView::showSuccess(const QString &message)
{
    showMessage(message, 3000);
    qDebug() << "VarTblView: 显示成功:" << message;
}

// 验证逻辑已移至Controller层，View层不再进行数据验证

int VarTblView::generateNewVariableId() const
{
    if (!m_model) {
        return 1;
    }

    QList<int> ids = m_model->getAllVariableIds();
    if (ids.isEmpty()) {
        return 1;
    }

    // 找到最大ID并加1
    int maxId = *std::max_element(ids.begin(), ids.end());
    return maxId + 1;
}
