@echo off
REM CommandTransceiver 测试服务器编译脚本
REM 适用于 Windows 环境

echo ========================================
echo   CommandTransceiver 测试服务器编译
echo ========================================

REM 检查是否存在 qmake
where qmake >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 qmake，请确保 Qt 已正确安装并添加到 PATH
    echo 请检查以下路径是否在 PATH 中:
    echo   - Qt安装目录\bin (例如: C:\Qt\5.15.2\msvc2019_64\bin)
    pause
    exit /b 1
)

echo 找到 qmake:
qmake -version

REM 检查 Qt 版本
echo.
echo 检查 Qt 版本兼容性...
echo 需要 Qt 5.12 或更高版本

REM 创建构建目录
if not exist "build" mkdir build
if not exist "bin" mkdir bin
if not exist "bin\debug" mkdir bin\debug
if not exist "bin\release" mkdir bin\release

echo.
echo 正在生成 Makefile...
qmake test_server.pro
if %errorlevel% neq 0 (
    echo 错误: qmake 执行失败
    pause
    exit /b 1
)

echo.
echo 正在编译项目...

REM 检查是否有 nmake (Visual Studio) 或 mingw32-make (MinGW)
where nmake >nul 2>nul
if %errorlevel% equ 0 (
    echo 使用 nmake 编译...
    nmake
    set MAKE_RESULT=%errorlevel%
) else (
    where mingw32-make >nul 2>nul
    if %errorlevel% equ 0 (
        echo 使用 mingw32-make 编译...
        mingw32-make
        set MAKE_RESULT=%errorlevel%
    ) else (
        where make >nul 2>nul
        if %errorlevel% equ 0 (
            echo 使用 make 编译...
            make
            set MAKE_RESULT=%errorlevel%
        ) else (
            echo 错误: 未找到合适的编译工具 (nmake, mingw32-make, make)
            echo 请确保已安装 Visual Studio 或 MinGW
            pause
            exit /b 1
        )
    )
)

if %MAKE_RESULT% neq 0 (
    echo.
    echo 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo ========================================
echo   编译成功！
echo ========================================

REM 查找生成的可执行文件
if exist "bin\debug\CommandTransceiverTestServer_d.exe" (
    echo Debug 版本: bin\debug\CommandTransceiverTestServer_d.exe
)
if exist "bin\release\CommandTransceiverTestServer.exe" (
    echo Release 版本: bin\release\CommandTransceiverTestServer.exe
)
if exist "debug\CommandTransceiverTestServer_d.exe" (
    echo Debug 版本: debug\CommandTransceiverTestServer_d.exe
)
if exist "release\CommandTransceiverTestServer.exe" (
    echo Release 版本: release\CommandTransceiverTestServer.exe
)

echo.
echo 使用方法:
echo   基本启动: CommandTransceiverTestServer.exe
echo   指定端口: CommandTransceiverTestServer.exe -p 9090
echo   查看帮助: CommandTransceiverTestServer.exe --help
echo.

pause
