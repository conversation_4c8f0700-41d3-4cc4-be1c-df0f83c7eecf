@echo off
echo ========================================
echo  CommandTransceiver GUI 服务器构建
echo ========================================

REM 检查Qt环境
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到qmake，请确保Qt环境已正确配置
    pause
    exit /b 1
)

REM 清理构建目录
if exist build rmdir /s /q build
mkdir build

REM 进入构建目录
cd build

REM 生成Makefile
echo 正在生成Makefile...
qmake -spec win32-g++ ../test_server.pro
if %errorlevel% neq 0 (
    echo qmake失败，尝试使用默认spec...
    qmake ../test_server.pro
    if %errorlevel% neq 0 (
        echo qmake失败！
        cd ..
        pause
        exit /b 1
    )
)

REM 编译项目
echo 正在编译...
mingw32-make
if %errorlevel% neq 0 (
    echo mingw32-make失败，尝试使用make...
    make
    if %errorlevel% neq 0 (
        echo 编译失败！
        cd ..
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo  编译成功！
echo ========================================

REM 查找生成的可执行文件
for /r . %%i in (*.exe) do (
    echo 可执行文件: %%i
)

cd ..
echo.
echo 运行方式:
echo   GUI模式: build\debug\CommandTransceiverTestServerGUI.exe
echo   控制台模式: build\debug\CommandTransceiverTestServerGUI.exe -c
echo ========================================
pause
