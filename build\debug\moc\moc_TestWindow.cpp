/****************************************************************************
** Meta object code from reading C++ file 'TestWindow.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../include/TestWindow.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'TestWindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_TestWindow_t {
    QByteArrayData data[21];
    char stringdata0[317];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_TestWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_TestWindow_t qt_meta_stringdata_TestWindow = {
    {
QT_MOC_LITERAL(0, 0, 10), // "TestWindow"
QT_MOC_LITERAL(1, 11, 17), // "onTestAddVariable"
QT_MOC_LITERAL(2, 29, 0), // ""
QT_MOC_LITERAL(3, 30, 18), // "onTestEditVariable"
QT_MOC_LITERAL(4, 49, 20), // "onTestDeleteVariable"
QT_MOC_LITERAL(5, 70, 17), // "onTestRefreshData"
QT_MOC_LITERAL(6, 88, 14), // "onTestSyncData"
QT_MOC_LITERAL(7, 103, 13), // "onTestConnect"
QT_MOC_LITERAL(8, 117, 16), // "onTestDisconnect"
QT_MOC_LITERAL(9, 134, 19), // "onTestTableEditMode"
QT_MOC_LITERAL(10, 154, 17), // "onTestAddListData"
QT_MOC_LITERAL(11, 172, 18), // "onTestTableEditing"
QT_MOC_LITERAL(12, 191, 20), // "onOperationCompleted"
QT_MOC_LITERAL(13, 212, 7), // "success"
QT_MOC_LITERAL(14, 220, 7), // "message"
QT_MOC_LITERAL(15, 228, 25), // "onConnectionStatusChanged"
QT_MOC_LITERAL(16, 254, 9), // "connected"
QT_MOC_LITERAL(17, 264, 19), // "onSyncStatusChanged"
QT_MOC_LITERAL(18, 284, 7), // "syncing"
QT_MOC_LITERAL(19, 292, 19), // "onBusyStatusChanged"
QT_MOC_LITERAL(20, 312, 4) // "busy"

    },
    "TestWindow\0onTestAddVariable\0\0"
    "onTestEditVariable\0onTestDeleteVariable\0"
    "onTestRefreshData\0onTestSyncData\0"
    "onTestConnect\0onTestDisconnect\0"
    "onTestTableEditMode\0onTestAddListData\0"
    "onTestTableEditing\0onOperationCompleted\0"
    "success\0message\0onConnectionStatusChanged\0"
    "connected\0onSyncStatusChanged\0syncing\0"
    "onBusyStatusChanged\0busy"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_TestWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      14,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   84,    2, 0x08 /* Private */,
       3,    0,   85,    2, 0x08 /* Private */,
       4,    0,   86,    2, 0x08 /* Private */,
       5,    0,   87,    2, 0x08 /* Private */,
       6,    0,   88,    2, 0x08 /* Private */,
       7,    0,   89,    2, 0x08 /* Private */,
       8,    0,   90,    2, 0x08 /* Private */,
       9,    0,   91,    2, 0x08 /* Private */,
      10,    0,   92,    2, 0x08 /* Private */,
      11,    0,   93,    2, 0x08 /* Private */,
      12,    2,   94,    2, 0x08 /* Private */,
      15,    1,   99,    2, 0x08 /* Private */,
      17,    1,  102,    2, 0x08 /* Private */,
      19,    1,  105,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,   13,   14,
    QMetaType::Void, QMetaType::Bool,   16,
    QMetaType::Void, QMetaType::Bool,   18,
    QMetaType::Void, QMetaType::Bool,   20,

       0        // eod
};

void TestWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<TestWindow *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onTestAddVariable(); break;
        case 1: _t->onTestEditVariable(); break;
        case 2: _t->onTestDeleteVariable(); break;
        case 3: _t->onTestRefreshData(); break;
        case 4: _t->onTestSyncData(); break;
        case 5: _t->onTestConnect(); break;
        case 6: _t->onTestDisconnect(); break;
        case 7: _t->onTestTableEditMode(); break;
        case 8: _t->onTestAddListData(); break;
        case 9: _t->onTestTableEditing(); break;
        case 10: _t->onOperationCompleted((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 11: _t->onConnectionStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 12: _t->onSyncStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 13: _t->onBusyStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject TestWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_TestWindow.data,
    qt_meta_data_TestWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *TestWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TestWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_TestWindow.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int TestWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 14;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
