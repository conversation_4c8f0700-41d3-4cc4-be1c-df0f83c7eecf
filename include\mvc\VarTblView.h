#ifndef VAR_TBL_VIEW_H
#define VAR_TBL_VIEW_H

#include <QWidget>
#include <QTableView>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QToolBar>
#include <QPushButton>
#include <QLabel>
#include <QProgressBar>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QCompleter>
#include <QSplitter>
#include <QHeaderView>
#include <QItemSelectionModel>
#include <QMenu>
#include <QAction>
#include <QTimer>
#include <QStatusBar>

// 引入相关组件
#include "VarTblModel.h"
#include "VarTblController.h"
#include "VarTblItemDelegate.h"
#include "VarTblFilterProxyModel.h"
#include "cmd/var.h"

/**
 * @brief VarTblView - 变量表格视图类
 *
 * 纯 View 层实现，负责 UI 显示和用户交互，遵循 MVC 设计原则。
 * 通过信号槽与 Controller 通信，提供丰富的用户界面功能，包括表格编辑、数据导入导出和高级筛选。
 */
class VarTblView : public QWidget
{
    Q_OBJECT

public:
    explicit VarTblView(QWidget *parent = nullptr);
    ~VarTblView() override;

    // ========== 组件设置 ==========

    /**
     * @brief 设置数据模型
     * @param model VarTbl 数据模型
     */
    void setModel(VarTblModel *model);

    /**
     * @brief 设置控制器
     * @param controller VarTbl 控制器
     */
    void setController(VarTblController *controller);

    // ========== UI 配置 ==========

    /**
     * @brief 设置是否显示工具栏
     * @param visible 是否显示
     */
    void setToolbarVisible(bool visible);

    /**
     * @brief 设置是否显示状态栏
     * @param visible 是否显示
     */
    void setStatusBarVisible(bool visible);

    /**
     * @brief 设置是否启用自动刷新
     * @param enabled 是否启用
     * @param intervalMs 刷新间隔（毫秒）
     */
    void setAutoRefresh(bool enabled, int intervalMs = 5000);

    /**
     * @brief 设置表格是否只读
     * @param readOnly 是否只读
     */
    void setReadOnly(bool readOnly);

    // ========== 选择和导航 ==========

    /**
     * @brief 获取当前选中的变量 ID 列表
     * @return 变量 ID 列表
     */
    QList<int> getSelectedVariableIds() const;

    /**
     * @brief 选中指定变量
     * @param id 变量 ID
     */
    void selectVariable(int id);

    /**
     * @brief 清除选择
     */
    void clearSelection();

    // ========== 数据导入导出 ==========

    /**
     * @brief 导出数据到文件
     * @param filePath 文件路径
     * @param format 导出格式（json/csv）
     * @return 是否成功
     */
    bool exportData(const QString &filePath, const QString &format = "json");

    /**
     * @brief 从文件导入数据
     * @param filePath 文件路径
     * @param format 导入格式（json/csv）
     * @return 是否成功
     */
    bool importData(const QString &filePath, const QString &format = "json");

    // ========== 表格编辑模式控制 ==========

    /**
     * @brief 设置表格编辑模式
     * @param enabled 是否启用表格编辑模式
     */
    void setTableEditMode(bool enabled);

    /**
     * @brief 获取表格编辑模式状态
     * @return 是否启用表格编辑模式
     */
    bool isTableEditMode() const { return m_tableEditMode; }

    /**
     * @brief 切换表格编辑模式
     */
    void toggleTableEditMode();

signals:
    // ========== 用户操作信号 ==========

    /**
     * @brief 请求添加变量
     */
    void addVariableRequested();

    /**
     * @brief 请求编辑变量
     * @param id 变量 ID
     */
    void editVariableRequested(int id);

    /**
     * @brief 请求删除变量
     * @param ids 变量 ID 列表
     */
    void deleteVariableRequested(const QList<int> &ids);

    /**
     * @brief 数据提交信号（编辑完成时发出）
     * @param id 变量 ID
     * @param label 变量标签
     * @param typeText 变量类型文本
     * @param value 变量值
     */
    void dataSubmitted(int id, const QString &label, const QString &typeText, const QVariant &value);

    /**
     * @brief 编辑取消信号
     */
    void editCancelled();

    /**
     * @brief 请求刷新数据
     */
    void refreshDataRequested();

    /**
     * @brief 请求同步数据
     */
    void syncDataRequested();

    /**
     * @brief 请求连接服务器
     */
    void connectToServerRequested();

    /**
     * @brief 请求断开连接
     */
    void disconnectFromServerRequested();

    /**
     * @brief 选择变化信号
     * @param selectedIds 选中的变量 ID 列表
     */
    void selectionChanged(const QList<int> &selectedIds);

private slots:
    // ========== UI 事件处理 ==========

    void onAddButtonClicked();
    void onEditButtonClicked();
    void onDeleteButtonClicked();
    void onRefreshButtonClicked();
    void onSyncButtonClicked();
    void onImportButtonClicked();
    void onExportButtonClicked();
    void onConnectButtonClicked();
    void onDisconnectButtonClicked();
    void onTableEditButtonClicked();

    // ========== 表格事件 ==========

    void onTableSelectionChanged();
    void onTableDoubleClicked(const QModelIndex &index);
    void onTableContextMenuRequested(const QPoint &pos);

    // ========== 表格编辑相关槽 ==========

    void onItemDelegateListEditingFinished(const QModelIndex &index, const QStringList &newList);
    void onItemDelegateCellDataChanged(const QModelIndex &index, const QVariant &newValue);
    void onItemDelegateEditingError(const QString &message);

    // ========== Controller UI 状态更新槽 ==========

    void onOperationCompleted(bool success, const QString &message);
    void onConnectionStatusChanged(bool connected);
    void onSyncStatusChanged(bool syncing);
    void onBusyStatusChanged(bool busy);
    void onShowMessage(const QString &message, bool isError);
    void onAutoRefreshTimeout();

    // ========== 筛选相关槽 ==========

    void updateFilterHistory();

private:
    // ========== UI 组件 ==========

    QVBoxLayout *m_mainLayout;
    QSplitter *m_mainSplitter;
    QToolBar *m_toolbar;
    QAction *m_addAction;
    QAction *m_editAction;
    QAction *m_deleteAction;
    QAction *m_refreshAction;
    QAction *m_syncAction;
    QAction *m_importAction;
    QAction *m_exportAction;
    QAction *m_connectAction;
    QAction *m_disconnectAction;
    QAction *m_tableEditAction;
    QTableView *m_tableView;
    QMenu *m_contextMenu;
    VarTblItemDelegate *m_itemDelegate;
    QWidget *m_statusWidget;
    QHBoxLayout *m_statusLayout;
    QLabel *m_statusLabel;
    QProgressBar *m_progressBar;
    QLabel *m_countLabel;
    QLabel *m_connectionLabel;

    // 筛选相关组件
    QLineEdit *m_filterInput;         // 筛选输入框
    QComboBox *m_filterColumnCombo;   // 筛选列选择下拉框
    QCheckBox *m_caseSensitiveCheck;  // 大小写敏感复选框
    QCheckBox *m_regexCheck;          // 正则表达式复选框
    QPushButton *m_clearFilterButton; // 筛选重置按钮
    QCompleter *m_filterCompleter;    // 筛选输入自动补全

    // ========== 核心组件 ==========

    VarTblModel *m_model;
    VarTblController *m_controller;
    VarTblFilterProxyModel *m_filterProxyModel;

    // ========== 状态管理 ==========

    bool m_readOnly;
    bool m_tableEditMode;
    QTimer *m_autoRefreshTimer;
    bool m_autoRefreshEnabled;

    // ========== UI 初始化 ==========

    void setupUI();
    void setupToolbar();
    void setupTableView();
    void setupStatusBar();
    void setupContextMenu();
    void connectSignals();
    void setupTableEditDelegate();
    void setupFilterUI();
    void connectFilterSignals();

    // ========== UI 更新 ==========

    void updateToolbarState();
    void updateStatusBar();
    void updateConnectionStatus(bool connected);
    void updateTableEditMode();

    // ========== 辅助方法 ==========

    QString formatVariableValue(const cmd::Var &var) const;
    QString getVariableTypeString(const cmd::Var &var) const;
    void showMessage(const QString &message, int timeout = 3000);
    void showError(const QString &error);
    void showSuccess(const QString &message);
    int generateNewVariableId() const;
};

#endif // VAR_TBL_VIEW_H
