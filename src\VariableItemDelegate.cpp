/**
 * @file VariableItemDelegate.cpp
 * @brief 简洁的变量表格项委托实现
 *
 * <AUTHOR> Framework
 * @date 2025-01-22
 */

#include "VariableItemDelegate.h"
#include <QApplication>
#include <QInputDialog>
#include <QMessageBox>
#include <QComboBox>
#include <QLineEdit>
#include <QDebug>

// ========== ListEditForm 实现 ==========

ListEditForm::ListEditForm(const QStringList &list, QWidget *parent)
    : QDialog(parent)
{
    setWindowTitle("编辑列表");
    setModal(true);
    resize(350, 400);

    setupUI();

    // 填充现有数据
    for (const QString &item : list) {
        m_listWidget->addItem(item);
    }
}

QStringList ListEditForm::getStringList() const
{
    QStringList result;
    for (int i = 0; i < m_listWidget->count(); ++i) {
        QListWidgetItem *item = m_listWidget->item(i);
        if (item && !item->text().trimmed().isEmpty()) {
            result.append(item->text().trimmed());
        }
    }
    return result;
}

void ListEditForm::setupUI()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    // 标题
    QLabel *titleLabel = new QLabel("编辑列表项", this);
    titleLabel->setStyleSheet("font-weight: bold; font-size: 12pt; margin-bottom: 10px;");
    mainLayout->addWidget(titleLabel);

    // 添加新项目区域
    QHBoxLayout *addLayout = new QHBoxLayout();
    addLayout->addWidget(new QLabel("新项目:", this));

    m_newItemEdit = new QLineEdit(this);
    m_newItemEdit->setPlaceholderText("输入新的列表项");
    addLayout->addWidget(m_newItemEdit, 1);

    m_addButton = new QPushButton("添加", this);
    addLayout->addWidget(m_addButton);

    mainLayout->addLayout(addLayout);

    // 现有项目列表
    mainLayout->addWidget(new QLabel("现有项目:", this));
    m_listWidget = new QListWidget(this);
    m_listWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    mainLayout->addWidget(m_listWidget);

    // 删除按钮
    QHBoxLayout *removeLayout = new QHBoxLayout();
    removeLayout->addStretch();
    m_removeButton = new QPushButton("删除选中项", this);
    removeLayout->addWidget(m_removeButton);
    mainLayout->addLayout(removeLayout);

    // 对话框按钮
    QHBoxLayout *dialogButtonLayout = new QHBoxLayout();
    dialogButtonLayout->addStretch();
    QPushButton *okButton = new QPushButton("确定", this);
    QPushButton *cancelButton = new QPushButton("取消", this);
    dialogButtonLayout->addWidget(okButton);
    dialogButtonLayout->addWidget(cancelButton);
    mainLayout->addLayout(dialogButtonLayout);

    // 连接信号
    connect(m_addButton, &QPushButton::clicked, this, &ListEditForm::addItem);
    connect(m_removeButton, &QPushButton::clicked, this, &ListEditForm::removeItem);
    connect(m_newItemEdit, &QLineEdit::returnPressed, this, &ListEditForm::addItem);
    connect(okButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);
}

void ListEditForm::addItem()
{
    QString text = m_newItemEdit->text().trimmed();
    if (!text.isEmpty()) {
        m_listWidget->addItem(text);
        m_newItemEdit->clear();
        m_newItemEdit->setFocus();
    }
}

void ListEditForm::removeItem()
{
    int currentRow = m_listWidget->currentRow();
    if (currentRow >= 0) {
        delete m_listWidget->takeItem(currentRow);
    }
}

// ========== VariableItemDelegate 实现 ==========

VariableItemDelegate::VariableItemDelegate(QObject *parent)
    : QStyledItemDelegate(parent)
{
}

void VariableItemDelegate::paint(QPainter *painter, const QStyleOptionViewItem &option,
                                const QModelIndex &index) const
{
    if (index.column() == VariableManager::ColumnValue && isListColumn(index)) {
        // List类型的特殊渲染
        QVariant data = index.data(Qt::DisplayRole);
        QStringList list = data.toStringList();

        QStyleOptionViewItem opt = option;
        initStyleOption(&opt, index);

        // 绘制背景
        QStyle *style = opt.widget ? opt.widget->style() : QApplication::style();
        style->drawControl(QStyle::CE_ItemViewItem, &opt, painter, opt.widget);

        // 绘制List内容
        QString displayText = formatListDisplay(list);
        QRect textRect = opt.rect.adjusted(4, 2, -4, -2);

        painter->save();
        painter->setPen(opt.palette.color(QPalette::Text));
        painter->drawText(textRect, Qt::AlignLeft | Qt::AlignVCenter, displayText);
        painter->restore();
    } else {
        // 使用默认渲染
        QStyledItemDelegate::paint(painter, option, index);
    }
}

QWidget *VariableItemDelegate::createEditor(QWidget *parent, const QStyleOptionViewItem &option,
                                           const QModelIndex &index) const
{
    if (index.column() == VariableManager::ColumnType) {
        // 类型列使用下拉框
        QComboBox *comboBox = new QComboBox(parent);
        comboBox->addItems({"String", "List"});
        comboBox->setFrame(false);
        return comboBox;
    }
    else if (index.column() == VariableManager::ColumnValue && isListColumn(index)) {
        // List类型：不创建内联编辑器，由双击事件处理
        // 这样可以避免双窗口问题
        return nullptr;
    }

    // String类型和其他列使用默认编辑器（QLineEdit）
    return QStyledItemDelegate::createEditor(parent, option, index);
}

void VariableItemDelegate::setEditorData(QWidget *editor, const QModelIndex &index) const
{
    if (index.column() == VariableManager::ColumnType) {
        // 类型列的下拉框
        QComboBox *comboBox = qobject_cast<QComboBox*>(editor);
        if (comboBox) {
            QString currentType = index.data(Qt::DisplayRole).toString();
            int typeIndex = comboBox->findText(currentType);
            if (typeIndex >= 0) {
                comboBox->setCurrentIndex(typeIndex);
            }
        }
    }
    else {
        // String类型和其他列使用默认处理
        QStyledItemDelegate::setEditorData(editor, index);
    }
}

void VariableItemDelegate::setModelData(QWidget *editor, QAbstractItemModel *model,
                                       const QModelIndex &index) const
{
    if (index.column() == VariableManager::ColumnType) {
        // 类型列的下拉框 - 简化处理，不进行业务逻辑判断
        QComboBox *comboBox = qobject_cast<QComboBox*>(editor);
        if (comboBox) {
            QString newType = comboBox->currentText();
            model->setData(index, newType, Qt::EditRole);
            // 类型改变时的值重置逻辑应该在Model或Controller中处理
        }
    }
    else {
        // String类型和其他列使用默认处理
        QStyledItemDelegate::setModelData(editor, model, index);
    }
}

QString VariableItemDelegate::formatListDisplay(const QStringList &list) const
{
    if (list.isEmpty()) {
        return "(空列表，双击编辑)";
    }

    // 直接显示逗号分隔的内容，更简洁直观
    QString result = list.join(", ");

    // 如果内容太长，进行截断
    const int maxLength = 100;
    if (result.length() > maxLength) {
        result = result.left(maxLength - 3) + "...";
    }

    return result;
}

bool VariableItemDelegate::isListColumn(const QModelIndex &index) const
{
    return getVariableType(index) == "List";
}

QString VariableItemDelegate::getVariableType(const QModelIndex &index) const
{
    if (!index.isValid()) {
        return QString();
    }
    
    // 获取同一行的类型列数据
    QModelIndex typeIndex = index.sibling(index.row(), VariableManager::ColumnType);
    return typeIndex.data(Qt::DisplayRole).toString();
}


