/****************************************************************************
** Meta object code from reading C++ file 'VariableTableView.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../include/VariableTableView.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'VariableTableView.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_VariableTableView_t {
    QByteArrayData data[25];
    char stringdata0[410];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_VariableTableView_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_VariableTableView_t qt_meta_stringdata_VariableTableView = {
    {
QT_MOC_LITERAL(0, 0, 17), // "VariableTableView"
QT_MOC_LITERAL(1, 18, 16), // "selectionChanged"
QT_MOC_LITERAL(2, 35, 0), // ""
QT_MOC_LITERAL(3, 36, 13), // "selectedCount"
QT_MOC_LITERAL(4, 50, 13), // "statusMessage"
QT_MOC_LITERAL(5, 64, 7), // "message"
QT_MOC_LITERAL(6, 72, 18), // "onAddButtonClicked"
QT_MOC_LITERAL(7, 91, 21), // "onDeleteButtonClicked"
QT_MOC_LITERAL(8, 113, 22), // "onRefreshButtonClicked"
QT_MOC_LITERAL(9, 136, 21), // "onImportButtonClicked"
QT_MOC_LITERAL(10, 158, 21), // "onExportButtonClicked"
QT_MOC_LITERAL(11, 180, 19), // "onEditButtonClicked"
QT_MOC_LITERAL(12, 200, 23), // "onTableSelectionChanged"
QT_MOC_LITERAL(13, 224, 20), // "onTableDoubleClicked"
QT_MOC_LITERAL(14, 245, 11), // "QModelIndex"
QT_MOC_LITERAL(15, 257, 5), // "index"
QT_MOC_LITERAL(16, 263, 20), // "onEditConfirmClicked"
QT_MOC_LITERAL(17, 284, 19), // "onEditCancelClicked"
QT_MOC_LITERAL(18, 304, 17), // "onEditTypeChanged"
QT_MOC_LITERAL(19, 322, 20), // "onOperationCompleted"
QT_MOC_LITERAL(20, 343, 9), // "operation"
QT_MOC_LITERAL(21, 353, 7), // "success"
QT_MOC_LITERAL(22, 361, 19), // "onSyncStatusChanged"
QT_MOC_LITERAL(23, 381, 7), // "syncing"
QT_MOC_LITERAL(24, 389, 20) // "onAutoRefreshTimeout"

    },
    "VariableTableView\0selectionChanged\0\0"
    "selectedCount\0statusMessage\0message\0"
    "onAddButtonClicked\0onDeleteButtonClicked\0"
    "onRefreshButtonClicked\0onImportButtonClicked\0"
    "onExportButtonClicked\0onEditButtonClicked\0"
    "onTableSelectionChanged\0onTableDoubleClicked\0"
    "QModelIndex\0index\0onEditConfirmClicked\0"
    "onEditCancelClicked\0onEditTypeChanged\0"
    "onOperationCompleted\0operation\0success\0"
    "onSyncStatusChanged\0syncing\0"
    "onAutoRefreshTimeout"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_VariableTableView[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      16,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   94,    2, 0x06 /* Public */,
       4,    1,   97,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       6,    0,  100,    2, 0x08 /* Private */,
       7,    0,  101,    2, 0x08 /* Private */,
       8,    0,  102,    2, 0x08 /* Private */,
       9,    0,  103,    2, 0x08 /* Private */,
      10,    0,  104,    2, 0x08 /* Private */,
      11,    0,  105,    2, 0x08 /* Private */,
      12,    0,  106,    2, 0x08 /* Private */,
      13,    1,  107,    2, 0x08 /* Private */,
      16,    0,  110,    2, 0x08 /* Private */,
      17,    0,  111,    2, 0x08 /* Private */,
      18,    0,  112,    2, 0x08 /* Private */,
      19,    3,  113,    2, 0x08 /* Private */,
      22,    1,  120,    2, 0x08 /* Private */,
      24,    0,  123,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void, QMetaType::QString,    5,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 14,   15,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool, QMetaType::QString,   20,   21,    5,
    QMetaType::Void, QMetaType::Bool,   23,
    QMetaType::Void,

       0        // eod
};

void VariableTableView::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<VariableTableView *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->selectionChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 1: _t->statusMessage((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->onAddButtonClicked(); break;
        case 3: _t->onDeleteButtonClicked(); break;
        case 4: _t->onRefreshButtonClicked(); break;
        case 5: _t->onImportButtonClicked(); break;
        case 6: _t->onExportButtonClicked(); break;
        case 7: _t->onEditButtonClicked(); break;
        case 8: _t->onTableSelectionChanged(); break;
        case 9: _t->onTableDoubleClicked((*reinterpret_cast< const QModelIndex(*)>(_a[1]))); break;
        case 10: _t->onEditConfirmClicked(); break;
        case 11: _t->onEditCancelClicked(); break;
        case 12: _t->onEditTypeChanged(); break;
        case 13: _t->onOperationCompleted((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 14: _t->onSyncStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 15: _t->onAutoRefreshTimeout(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (VariableTableView::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VariableTableView::selectionChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (VariableTableView::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VariableTableView::statusMessage)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject VariableTableView::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_VariableTableView.data,
    qt_meta_data_VariableTableView,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *VariableTableView::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *VariableTableView::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_VariableTableView.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int VariableTableView::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 16)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 16;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 16)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 16;
    }
    return _id;
}

// SIGNAL 0
void VariableTableView::selectionChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void VariableTableView::statusMessage(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
