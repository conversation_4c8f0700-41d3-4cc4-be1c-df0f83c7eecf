/****************************************************************************
** Meta object code from reading C++ file 'VarTblConfig.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../include/mvc/VarTblConfig.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'VarTblConfig.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_VarTblConfig_t {
    QByteArrayData data[9];
    char stringdata0[97];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_VarTblConfig_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_VarTblConfig_t qt_meta_stringdata_VarTblConfig = {
    {
QT_MOC_LITERAL(0, 0, 12), // "VarTblConfig"
QT_MOC_LITERAL(1, 13, 13), // "configChanged"
QT_MOC_LITERAL(2, 27, 0), // ""
QT_MOC_LITERAL(3, 28, 17), // "serverModeChanged"
QT_MOC_LITERAL(4, 46, 10), // "ServerMode"
QT_MOC_LITERAL(5, 57, 4), // "mode"
QT_MOC_LITERAL(6, 62, 11), // "Synchronous"
QT_MOC_LITERAL(7, 74, 12), // "Asynchronous"
QT_MOC_LITERAL(8, 87, 9) // "LocalOnly"

    },
    "VarTblConfig\0configChanged\0\0"
    "serverModeChanged\0ServerMode\0mode\0"
    "Synchronous\0Asynchronous\0LocalOnly"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_VarTblConfig[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       2,   14, // methods
       0,    0, // properties
       1,   28, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   24,    2, 0x06 /* Public */,
       3,    1,   25,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 4,    5,

 // enums: name, alias, flags, count, data
       4,    4, 0x2,    3,   33,

 // enum data: key, value
       6, uint(VarTblConfig::ServerMode::Synchronous),
       7, uint(VarTblConfig::ServerMode::Asynchronous),
       8, uint(VarTblConfig::ServerMode::LocalOnly),

       0        // eod
};

void VarTblConfig::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<VarTblConfig *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->configChanged(); break;
        case 1: _t->serverModeChanged((*reinterpret_cast< ServerMode(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (VarTblConfig::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblConfig::configChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (VarTblConfig::*)(ServerMode );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblConfig::serverModeChanged)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject VarTblConfig::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_VarTblConfig.data,
    qt_meta_data_VarTblConfig,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *VarTblConfig::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *VarTblConfig::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_VarTblConfig.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int VarTblConfig::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 2)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 2)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 2;
    }
    return _id;
}

// SIGNAL 0
void VarTblConfig::configChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void VarTblConfig::serverModeChanged(ServerMode _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
