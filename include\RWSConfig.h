#ifndef RWS_CONFIG_H
#define RWS_CONFIG_H

#include <QByteArray>
#include "IRWSStrategy.h"

/**
 * @brief 预长度策略配置
 */
struct PreLengthConfig
{
    int maxMessageSize = 5 * 1024 * 1024;      // 最大消息大小 (5MB)
};

/**
 * @brief 换行符结束策略配置
 */
struct EndWithNewLineConfig
{
    QByteArray delimiter = "\n";               // 分隔符
    int maxLineLength = 64 * 1024;             // 最大行长度 (64KB)
};

/**
 * @brief 简单策略配置
 */
struct SimpleConfig
{
    int chunkSize = 4096;                      // 数据块大小 (4KB)
};

/**
 * @brief 简化版读写策略配置类
 */
class RWSConfig
{
public:
    /**
     * @brief 构造函数
     */
    RWSConfig() : m_strategyType(IRWSStrategy::PreLength) {}

    /**
     * @brief 获取策略类型
     */
    IRWSStrategy::StrategyType strategyType() const { return m_strategyType; }

    /**
     * @brief 设置策略类型
     */
    void setStrategyType(IRWSStrategy::StrategyType type) { m_strategyType = type; }

    /**
     * @brief 获取预长度策略配置
     */
    const PreLengthConfig& preLengthConfig() const { return m_preLengthConfig; }

    /**
     * @brief 获取换行符结束策略配置
     */
    const EndWithNewLineConfig& endWithNewLineConfig() const { return m_endWithNewLineConfig; }

    /**
     * @brief 获取简单策略配置
     */
    const SimpleConfig& simpleConfig() const { return m_simpleConfig; }

    /**
     * @brief 创建默认配置
     */
    static RWSConfig createDefault() {
        RWSConfig config;
        config.setStrategyType(IRWSStrategy::PreLength);
        return config;
    }

    /**
     * @brief 创建换行符结束策略配置
     */
    static RWSConfig createEndWithNewLine() {
        RWSConfig config;
        config.setStrategyType(IRWSStrategy::EndWithNewLine);
        return config;
    }

    /**
     * @brief 创建简单策略配置
     */
    static RWSConfig createSimple() {
        RWSConfig config;
        config.setStrategyType(IRWSStrategy::Simple);
        return config;
    }

private:
    IRWSStrategy::StrategyType m_strategyType;
    PreLengthConfig m_preLengthConfig;
    EndWithNewLineConfig m_endWithNewLineConfig;
    SimpleConfig m_simpleConfig;
};

#endif // RWS_CONFIG_H
