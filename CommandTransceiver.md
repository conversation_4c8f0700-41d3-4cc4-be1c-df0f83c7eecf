QT += core network widgets

TARGET = CommandTransceiver
TEMPLATE = app

# C++标准
CONFIG += c++17

# 构建配置
CONFIG += warn_on
CONFIG += thread
CONFIG += console

# 预处理器定义
DEFINES += QT_DEPRECATED_WARNINGS
#DEFINES += COMMANDTRANSCEIVER_VERSION=\\\"$$VERSION\\\"
#DEFINES += COMMANDTRANSCEIVER_MVC_SUPPORT

# 包含路径
INCLUDEPATH += $$PWD/include

# ada3模块路径配置（可选）
ADA3_ROOT = $$PWD/..

# 检查ada3路径是否存在（可选集成）
exists($$ADA3_ROOT) {
    message("Found ada3 at: $$ADA3_ROOT")

    # ada3模块包含路径
    exists($$ADA3_ROOT/include) {
        INCLUDEPATH += $$ADA3_ROOT/include
        message("Added ada3 include path: $$ADA3_ROOT/include")
        DEFINES += COMMANDTRANSCEIVER_ADA3_SUPPORT
        DEFINES += COMMANDTRANSCEIVER_VARTBL_SUPPORT
    }

    # 库文件路径
    exists($$ADA3_ROOT/bin) {
        LIBS += -L$$ADA3_ROOT/bin
        message("Added ada3 library path: $$ADA3_ROOT/bin")

        # ada3模块链接
        LIBS += -lcore
        LIBS += -lcmd
        LIBS += -lutility

        # 可选的logger模块
        exists($$ADA3_ROOT/bin/logger*) {
            LIBS += -llogger
            DEFINES += COMMANDTRANSCEIVER_LOGGER_SUPPORT
        }

        message("Linked ada3 libraries: core, cmd, utility")
    }
} else {
    message("Ada3 not found - building without ada3 support")
}


HEADERS += \
    # 核心通信框架
    include/CommandTransceiverRefactored.h \
    include/NetworkWorker.h \
    include/DataProcessorWorker.h \
    \
    # RWS策略系统
    include/IRWSStrategy.h \
    include/RWSConfig.h \
    include/RWSStrategyFactory.h \
    include/PreLengthRWSStrategy.h \
    include/EndWithNewLineRWSStrategy.h \
    include/SimpleRWSStrategy.h \
    \
    # VarTbl MVC架构系统
    include/mvc/VarTblModel.h \
    include/mvc/VarTblController.h \
    include/mvc/VarTblView.h \
    include/mvc/VarListEditor.h \
    include/mvc/VarTblItemDelegate.h \
    include/mvc/VarTblFilterProxyModel.h \
    \
    # MVC架构重构测试
    include/TestWindow.h \


SOURCES += \
    main_test_mvc_refactor.cpp \          # MVC重构测试程序
    # 核心通信框架
    src/CommandTransceiverRefactored.cpp \
    src/NetworkWorker.cpp \
    src/DataProcessorWorker.cpp \
    \
    # RWS策略系统
    src/RWSConfig.cpp \
    src/RWSStrategyFactory.cpp \
    src/PreLengthRWSStrategy.cpp \
    src/EndWithNewLineRWSStrategy.cpp \
    src/SimpleRWSStrategy.cpp \
    \
    # VarTbl MVC架构系统
    src/mvc/VarTblModel.cpp \
    src/mvc/VarTblController.cpp \
    src/mvc/VarTblView.cpp \
    src/mvc/VarListEditor.cpp \
    src/mvc/VarTblItemDelegate.cpp \
    src/mvc/VarTblFilterProxyModel.cpp \
    \
    # MVC架构重构测试
    src/TestWindow.cpp \

# ========== 构建配置选项 ==========

# ========== 输出目录配置 ==========

CONFIG(debug, debug|release) {
    DESTDIR = $$PWD/bin/debug
    OBJECTS_DIR = $$PWD/build/debug/obj
    MOC_DIR = $$PWD/build/debug/moc
    RCC_DIR = $$PWD/build/debug/rcc
    UI_DIR = $$PWD/build/debug/ui
    TARGET = $${TARGET}_d
} else {
    DESTDIR = $$PWD/bin/release
    OBJECTS_DIR = $$PWD/build/release/obj
    MOC_DIR = $$PWD/build/release/moc
    RCC_DIR = $$PWD/build/release/rcc
    UI_DIR = $$PWD/build/release/ui
}

# 创建输出目录
!exists($$DESTDIR) {
    system($$QMAKE_MKDIR $$shell_path($$DESTDIR))
}

# ========== 安装配置 ==========

target.path = $$[QT_INSTALL_BINS]
headers.path = $$[QT_INSTALL_HEADERS]/CommandTransceiver
headers.files = $$HEADERS

INSTALLS += target headers

# ========== 清理配置 ==========

QMAKE_CLEAN += $$TARGET

# 额外的清理文件
win32 {
    QMAKE_CLEAN += *.pdb *.ilk *.exp *.lib
}

unix {
    QMAKE_CLEAN += *.so *.a
}

# ========== Qt部署配置 ==========

#win32 {
#    # 获取Qt安装路径
#    QT_DEPLOY_BIN = $$[QT_INSTALL_BINS]/windeployqt.exe

#    exists($$QT_DEPLOY_BIN) {
#        # 部署目标
#        CONFIG(debug, debug|release) {
#            DEPLOY_TARGET = $$shell_path($$DESTDIR/$${TARGET}.exe)
#            DEPLOY_COMMAND = $$shell_path($$QT_DEPLOY_BIN) --debug --force $$shell_quote($$DEPLOY_TARGET)
#        } else {
#            DEPLOY_TARGET = $$shell_path($$DESTDIR/$${TARGET}.exe)
#            DEPLOY_COMMAND = $$shell_path($$QT_DEPLOY_BIN) --release --force $$shell_quote($$DEPLOY_TARGET)
#        }

#        # 添加部署步骤到构建过程
#        deploy.commands = $$DEPLOY_COMMAND
#        deploy.depends = $$TARGET

#        # 确保部署在链接后执行
#        QMAKE_POST_LINK += $$DEPLOY_COMMAND

#        message("Qt deployment will be performed automatically after build")
#    } else {
#        message("windeployqt.exe not found - manual deployment required")
#    }
#}
