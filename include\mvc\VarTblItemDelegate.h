#ifndef VAR_TBL_ITEM_DELEGATE_H
#define VAR_TBL_ITEM_DELEGATE_H

#include <QStyledItemDelegate>
#include <QWidget>
#include <QModelIndex>
#include <QStyleOptionViewItem>
#include <QAbstractItemModel>
#include <QSpinBox>
#include <QLineEdit>
#include <QComboBox>
#include <QPushButton>

// 引入相关组件
#include "VarTblModel.h"
#include "VarListEditor.h"

// 引入ada3数据结构
#include "cmd/var.h"

/**
 * @brief VarTbl表格项编辑器代理
 * 
 * 功能特性：
 * - 为不同列提供专门的编辑器
 * - 支持字符串和列表类型的内联编辑
 * - 列表类型使用对话框编辑器
 * - 数据验证和错误处理
 */
class VarTblItemDelegate : public QStyledItemDelegate
{
    Q_OBJECT

public:
    explicit VarTblItemDelegate(QObject *parent = nullptr);
    ~VarTblItemDelegate() override = default;

    // ========== QStyledItemDelegate接口实现 ==========
    
    /**
     * @brief 创建编辑器
     * @param parent 父窗口
     * @param option 样式选项
     * @param index 模型索引
     * @return 编辑器控件
     */
    QWidget* createEditor(QWidget* parent, 
                         const QStyleOptionViewItem& option, 
                         const QModelIndex& index) const override;
    
    /**
     * @brief 设置编辑器数据
     * @param editor 编辑器控件
     * @param index 模型索引
     */
    void setEditorData(QWidget* editor, const QModelIndex& index) const override;
    
    /**
     * @brief 将编辑器数据设置到模型
     * @param editor 编辑器控件
     * @param model 数据模型
     * @param index 模型索引
     */
    void setModelData(QWidget* editor, 
                     QAbstractItemModel* model, 
                     const QModelIndex& index) const override;
    
    /**
     * @brief 更新编辑器几何形状
     * @param editor 编辑器控件
     * @param option 样式选项
     * @param index 模型索引
     */
    void updateEditorGeometry(QWidget* editor,
                             const QStyleOptionViewItem& option,
                             const QModelIndex& index) const override;

    // ========== 配置接口 ==========
    
    /**
     * @brief 设置是否启用编辑
     * @param enabled 是否启用
     */
    void setEditingEnabled(bool enabled);
    
    /**
     * @brief 获取编辑启用状态
     * @return 是否启用编辑
     */
    bool isEditingEnabled() const { return m_editingEnabled; }

signals:
    /**
     * @brief 列表编辑完成信号
     * @param index 模型索引
     * @param newList 新的列表数据
     */
    void listEditingFinished(const QModelIndex& index, const QStringList& newList);

    /**
     * @brief 单元格数据变化信号
     * @param index 模型索引
     * @param newValue 新值
     */
    void cellDataChanged(const QModelIndex& index, const QVariant& newValue);

    /**
     * @brief 编辑错误信号
     * @param message 错误消息
     */
    void editingError(const QString& message);

private slots:
    /**
     * @brief 处理列表编辑按钮点击
     */
    void onListEditButtonClicked();

private:
    // ========== 编辑器创建方法 ==========
    
    /**
     * @brief 创建ID列编辑器
     * @param parent 父窗口
     * @return QSpinBox编辑器
     */
    QWidget* createIdEditor(QWidget* parent) const;
    
    /**
     * @brief 创建标签列编辑器
     * @param parent 父窗口
     * @return QLineEdit编辑器
     */
    QWidget* createLabelEditor(QWidget* parent) const;
    
    /**
     * @brief 创建类型列编辑器
     * @param parent 父窗口
     * @return QComboBox编辑器
     */
    QWidget* createTypeEditor(QWidget* parent) const;
    
    /**
     * @brief 创建值列编辑器
     * @param parent 父窗口
     * @param index 模型索引
     * @return 值编辑器控件
     */
    QWidget* createValueEditor(QWidget* parent, const QModelIndex& index) const;
    
    // ========== 数据处理方法 ==========
    
    /**
     * @brief 获取变量类型
     * @param index 模型索引
     * @return 变量类型
     */
    cmd::Var::Type getVariableType(const QModelIndex& index) const;
    
    /**
     * @brief 验证输入数据
     * @param column 列号
     * @param value 输入值
     * @return 是否有效
     */
    bool validateInput(int column, const QVariant& value) const;
    
    /**
     * @brief 格式化显示文本
     * @param var 变量对象
     * @return 格式化后的文本
     */
    QString formatDisplayText(const cmd::Var& var) const;

private:
    // ========== 状态管理 ==========
    
    bool m_editingEnabled;              // 是否启用编辑
    mutable QModelIndex m_currentIndex; // 当前编辑的索引（用于列表编辑）
};

#endif // VAR_TBL_ITEM_DELEGATE_H
