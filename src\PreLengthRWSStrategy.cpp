#include "PreLengthRWSStrategy.h"
#include <QDataStream>
#include <QDebug>

PreLengthRWSStrategy::PreLengthRWSStrategy(const PreLengthConfig &config)
    : m_config(config)
{
    qDebug() << "PreLengthRWSStrategy: 初始化完成";
}

PreLengthRWSStrategy::~PreLengthRWSStrategy()
{
    qDebug() << "PreLengthRWSStrategy: 析构";
}

QByteArray PreLengthRWSStrategy::wrapMessage(const QByteArray &message)
{
    if (message.isEmpty()) {
        return QByteArray();
    }

    // 创建带长度头的数据包
    QByteArray packet;
    QDataStream stream(&packet, QIODevice::WriteOnly);
    stream.setByteOrder(QDataStream::BigEndian);

    // 写入长度头（4字节）
    stream << static_cast<quint32>(message.size());

    // 写入消息内容
    packet.append(message);

    return packet;
}

QList<QByteArray> PreLengthRWSStrategy::processReceivedData(const QByteArray &newData)
{
    if (newData.isEmpty()) {
        return QList<QByteArray>();
    }

    // 添加新数据到缓冲区
    m_receiveBuffer.append(newData);

    // 提取完整消息
    return extractCompleteMessages();
}

void PreLengthRWSStrategy::clearBuffer()
{
    m_receiveBuffer.clear();
    qDebug() << "PreLengthRWSStrategy: 缓冲区已清空";
}

QList<QByteArray> PreLengthRWSStrategy::extractCompleteMessages()
{
    QList<QByteArray> messages;

    while (m_receiveBuffer.size() >= LENGTH_HEADER_SIZE) {
        // 读取长度头
        QDataStream stream(m_receiveBuffer);
        stream.setByteOrder(QDataStream::BigEndian);

        quint32 messageLength;
        stream >> messageLength;

        // 验证消息长度
        if (!isValidMessageLength(messageLength)) {
            qWarning() << "PreLengthRWSStrategy: 无效的消息长度:" << messageLength;
            m_receiveBuffer.clear(); // 清空缓冲区，避免错误传播
            break;
        }

        // 检查是否有完整消息
        if (m_receiveBuffer.size() < LENGTH_HEADER_SIZE + messageLength) {
            break; // 数据不完整，等待更多数据
        }

        // 提取消息
        QByteArray message = m_receiveBuffer.mid(LENGTH_HEADER_SIZE, messageLength);
        messages.append(message);

        // 从缓冲区移除已处理的数据
        m_receiveBuffer.remove(0, LENGTH_HEADER_SIZE + messageLength);
    }

    return messages;
}

bool PreLengthRWSStrategy::isValidMessageLength(quint32 length) const
{
    return length > 0 && length <= static_cast<quint32>(m_config.maxMessageSize);
}


