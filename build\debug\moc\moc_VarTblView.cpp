/****************************************************************************
** Meta object code from reading C++ file 'VarTblView.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../include/mvc/VarTblView.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#include <QtCore/QList>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'VarTblView.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_VarTblView_t {
    QByteArrayData data[53];
    char stringdata0[883];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_VarTblView_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_VarTblView_t qt_meta_stringdata_VarTblView = {
    {
QT_MOC_LITERAL(0, 0, 10), // "VarTblView"
QT_MOC_LITERAL(1, 11, 20), // "addVariableRequested"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 21), // "editVariableRequested"
QT_MOC_LITERAL(4, 55, 2), // "id"
QT_MOC_LITERAL(5, 58, 23), // "deleteVariableRequested"
QT_MOC_LITERAL(6, 82, 10), // "QList<int>"
QT_MOC_LITERAL(7, 93, 3), // "ids"
QT_MOC_LITERAL(8, 97, 13), // "dataSubmitted"
QT_MOC_LITERAL(9, 111, 5), // "label"
QT_MOC_LITERAL(10, 117, 8), // "typeText"
QT_MOC_LITERAL(11, 126, 5), // "value"
QT_MOC_LITERAL(12, 132, 13), // "editCancelled"
QT_MOC_LITERAL(13, 146, 20), // "refreshDataRequested"
QT_MOC_LITERAL(14, 167, 17), // "syncDataRequested"
QT_MOC_LITERAL(15, 185, 24), // "connectToServerRequested"
QT_MOC_LITERAL(16, 210, 29), // "disconnectFromServerRequested"
QT_MOC_LITERAL(17, 240, 16), // "selectionChanged"
QT_MOC_LITERAL(18, 257, 11), // "selectedIds"
QT_MOC_LITERAL(19, 269, 18), // "onAddButtonClicked"
QT_MOC_LITERAL(20, 288, 19), // "onEditButtonClicked"
QT_MOC_LITERAL(21, 308, 21), // "onDeleteButtonClicked"
QT_MOC_LITERAL(22, 330, 22), // "onRefreshButtonClicked"
QT_MOC_LITERAL(23, 353, 19), // "onSyncButtonClicked"
QT_MOC_LITERAL(24, 373, 21), // "onImportButtonClicked"
QT_MOC_LITERAL(25, 395, 21), // "onExportButtonClicked"
QT_MOC_LITERAL(26, 417, 22), // "onConnectButtonClicked"
QT_MOC_LITERAL(27, 440, 25), // "onDisconnectButtonClicked"
QT_MOC_LITERAL(28, 466, 24), // "onTableEditButtonClicked"
QT_MOC_LITERAL(29, 491, 23), // "onTableSelectionChanged"
QT_MOC_LITERAL(30, 515, 20), // "onTableDoubleClicked"
QT_MOC_LITERAL(31, 536, 11), // "QModelIndex"
QT_MOC_LITERAL(32, 548, 5), // "index"
QT_MOC_LITERAL(33, 554, 27), // "onTableContextMenuRequested"
QT_MOC_LITERAL(34, 582, 3), // "pos"
QT_MOC_LITERAL(35, 586, 33), // "onItemDelegateListEditingFini..."
QT_MOC_LITERAL(36, 620, 7), // "newList"
QT_MOC_LITERAL(37, 628, 29), // "onItemDelegateCellDataChanged"
QT_MOC_LITERAL(38, 658, 8), // "newValue"
QT_MOC_LITERAL(39, 667, 26), // "onItemDelegateEditingError"
QT_MOC_LITERAL(40, 694, 7), // "message"
QT_MOC_LITERAL(41, 702, 20), // "onOperationCompleted"
QT_MOC_LITERAL(42, 723, 7), // "success"
QT_MOC_LITERAL(43, 731, 25), // "onConnectionStatusChanged"
QT_MOC_LITERAL(44, 757, 9), // "connected"
QT_MOC_LITERAL(45, 767, 19), // "onSyncStatusChanged"
QT_MOC_LITERAL(46, 787, 7), // "syncing"
QT_MOC_LITERAL(47, 795, 19), // "onBusyStatusChanged"
QT_MOC_LITERAL(48, 815, 4), // "busy"
QT_MOC_LITERAL(49, 820, 13), // "onShowMessage"
QT_MOC_LITERAL(50, 834, 7), // "isError"
QT_MOC_LITERAL(51, 842, 20), // "onAutoRefreshTimeout"
QT_MOC_LITERAL(52, 863, 19) // "updateFilterHistory"

    },
    "VarTblView\0addVariableRequested\0\0"
    "editVariableRequested\0id\0"
    "deleteVariableRequested\0QList<int>\0"
    "ids\0dataSubmitted\0label\0typeText\0value\0"
    "editCancelled\0refreshDataRequested\0"
    "syncDataRequested\0connectToServerRequested\0"
    "disconnectFromServerRequested\0"
    "selectionChanged\0selectedIds\0"
    "onAddButtonClicked\0onEditButtonClicked\0"
    "onDeleteButtonClicked\0onRefreshButtonClicked\0"
    "onSyncButtonClicked\0onImportButtonClicked\0"
    "onExportButtonClicked\0onConnectButtonClicked\0"
    "onDisconnectButtonClicked\0"
    "onTableEditButtonClicked\0"
    "onTableSelectionChanged\0onTableDoubleClicked\0"
    "QModelIndex\0index\0onTableContextMenuRequested\0"
    "pos\0onItemDelegateListEditingFinished\0"
    "newList\0onItemDelegateCellDataChanged\0"
    "newValue\0onItemDelegateEditingError\0"
    "message\0onOperationCompleted\0success\0"
    "onConnectionStatusChanged\0connected\0"
    "onSyncStatusChanged\0syncing\0"
    "onBusyStatusChanged\0busy\0onShowMessage\0"
    "isError\0onAutoRefreshTimeout\0"
    "updateFilterHistory"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_VarTblView[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      33,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
      10,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,  179,    2, 0x06 /* Public */,
       3,    1,  180,    2, 0x06 /* Public */,
       5,    1,  183,    2, 0x06 /* Public */,
       8,    4,  186,    2, 0x06 /* Public */,
      12,    0,  195,    2, 0x06 /* Public */,
      13,    0,  196,    2, 0x06 /* Public */,
      14,    0,  197,    2, 0x06 /* Public */,
      15,    0,  198,    2, 0x06 /* Public */,
      16,    0,  199,    2, 0x06 /* Public */,
      17,    1,  200,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      19,    0,  203,    2, 0x08 /* Private */,
      20,    0,  204,    2, 0x08 /* Private */,
      21,    0,  205,    2, 0x08 /* Private */,
      22,    0,  206,    2, 0x08 /* Private */,
      23,    0,  207,    2, 0x08 /* Private */,
      24,    0,  208,    2, 0x08 /* Private */,
      25,    0,  209,    2, 0x08 /* Private */,
      26,    0,  210,    2, 0x08 /* Private */,
      27,    0,  211,    2, 0x08 /* Private */,
      28,    0,  212,    2, 0x08 /* Private */,
      29,    0,  213,    2, 0x08 /* Private */,
      30,    1,  214,    2, 0x08 /* Private */,
      33,    1,  217,    2, 0x08 /* Private */,
      35,    2,  220,    2, 0x08 /* Private */,
      37,    2,  225,    2, 0x08 /* Private */,
      39,    1,  230,    2, 0x08 /* Private */,
      41,    2,  233,    2, 0x08 /* Private */,
      43,    1,  238,    2, 0x08 /* Private */,
      45,    1,  241,    2, 0x08 /* Private */,
      47,    1,  244,    2, 0x08 /* Private */,
      49,    2,  247,    2, 0x08 /* Private */,
      51,    0,  252,    2, 0x08 /* Private */,
      52,    0,  253,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    4,
    QMetaType::Void, 0x80000000 | 6,    7,
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::QString, QMetaType::QVariant,    4,    9,   10,   11,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 6,   18,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 31,   32,
    QMetaType::Void, QMetaType::QPoint,   34,
    QMetaType::Void, 0x80000000 | 31, QMetaType::QStringList,   32,   36,
    QMetaType::Void, 0x80000000 | 31, QMetaType::QVariant,   32,   38,
    QMetaType::Void, QMetaType::QString,   40,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,   42,   40,
    QMetaType::Void, QMetaType::Bool,   44,
    QMetaType::Void, QMetaType::Bool,   46,
    QMetaType::Void, QMetaType::Bool,   48,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,   40,   50,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void VarTblView::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<VarTblView *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->addVariableRequested(); break;
        case 1: _t->editVariableRequested((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 2: _t->deleteVariableRequested((*reinterpret_cast< const QList<int>(*)>(_a[1]))); break;
        case 3: _t->dataSubmitted((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3])),(*reinterpret_cast< const QVariant(*)>(_a[4]))); break;
        case 4: _t->editCancelled(); break;
        case 5: _t->refreshDataRequested(); break;
        case 6: _t->syncDataRequested(); break;
        case 7: _t->connectToServerRequested(); break;
        case 8: _t->disconnectFromServerRequested(); break;
        case 9: _t->selectionChanged((*reinterpret_cast< const QList<int>(*)>(_a[1]))); break;
        case 10: _t->onAddButtonClicked(); break;
        case 11: _t->onEditButtonClicked(); break;
        case 12: _t->onDeleteButtonClicked(); break;
        case 13: _t->onRefreshButtonClicked(); break;
        case 14: _t->onSyncButtonClicked(); break;
        case 15: _t->onImportButtonClicked(); break;
        case 16: _t->onExportButtonClicked(); break;
        case 17: _t->onConnectButtonClicked(); break;
        case 18: _t->onDisconnectButtonClicked(); break;
        case 19: _t->onTableEditButtonClicked(); break;
        case 20: _t->onTableSelectionChanged(); break;
        case 21: _t->onTableDoubleClicked((*reinterpret_cast< const QModelIndex(*)>(_a[1]))); break;
        case 22: _t->onTableContextMenuRequested((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        case 23: _t->onItemDelegateListEditingFinished((*reinterpret_cast< const QModelIndex(*)>(_a[1])),(*reinterpret_cast< const QStringList(*)>(_a[2]))); break;
        case 24: _t->onItemDelegateCellDataChanged((*reinterpret_cast< const QModelIndex(*)>(_a[1])),(*reinterpret_cast< const QVariant(*)>(_a[2]))); break;
        case 25: _t->onItemDelegateEditingError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 26: _t->onOperationCompleted((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 27: _t->onConnectionStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 28: _t->onSyncStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 29: _t->onBusyStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 30: _t->onShowMessage((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 31: _t->onAutoRefreshTimeout(); break;
        case 32: _t->updateFilterHistory(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 2:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QList<int> >(); break;
            }
            break;
        case 9:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QList<int> >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (VarTblView::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblView::addVariableRequested)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (VarTblView::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblView::editVariableRequested)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (VarTblView::*)(const QList<int> & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblView::deleteVariableRequested)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (VarTblView::*)(int , const QString & , const QString & , const QVariant & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblView::dataSubmitted)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (VarTblView::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblView::editCancelled)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (VarTblView::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblView::refreshDataRequested)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (VarTblView::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblView::syncDataRequested)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (VarTblView::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblView::connectToServerRequested)) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (VarTblView::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblView::disconnectFromServerRequested)) {
                *result = 8;
                return;
            }
        }
        {
            using _t = void (VarTblView::*)(const QList<int> & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&VarTblView::selectionChanged)) {
                *result = 9;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject VarTblView::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_VarTblView.data,
    qt_meta_data_VarTblView,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *VarTblView::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *VarTblView::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_VarTblView.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int VarTblView::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 33)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 33;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 33)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 33;
    }
    return _id;
}

// SIGNAL 0
void VarTblView::addVariableRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void VarTblView::editVariableRequested(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void VarTblView::deleteVariableRequested(const QList<int> & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void VarTblView::dataSubmitted(int _t1, const QString & _t2, const QString & _t3, const QVariant & _t4)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t4))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void VarTblView::editCancelled()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void VarTblView::refreshDataRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void VarTblView::syncDataRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void VarTblView::connectToServerRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void VarTblView::disconnectFromServerRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}

// SIGNAL 9
void VarTblView::selectionChanged(const QList<int> & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 9, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
