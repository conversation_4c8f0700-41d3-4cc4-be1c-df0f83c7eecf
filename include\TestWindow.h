#ifndef TEST_WINDOW_H
#define TEST_WINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>

// 前向声明
class VarTblManager;
class VarTblView;
class VarTblModel;
class VarTblController;
class CommandTransceiverRefactored;

class TestWindow : public QMainWindow
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit TestWindow(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~TestWindow() override = default;

private slots:
    /**
     * @brief 测试添加变量功能
     */
    void onTestAddVariable();
    
    /**
     * @brief 测试编辑变量功能
     */
    void onTestEditVariable();
    
    /**
     * @brief 测试删除变量功能
     */
    void onTestDeleteVariable();
    
    /**
     * @brief 测试刷新数据功能
     */
    void onTestRefreshData();
    
    /**
     * @brief 测试同步数据功能
     */
    void onTestSyncData();
    
    /**
     * @brief 测试连接功能
     */
    void onTestConnect();
    
    /**
     * @brief 测试断开连接功能
     */
    void onTestDisconnect();

    /**
     * @brief 测试表格编辑模式切换
     */
    void onTestTableEditMode();

    /**
     * @brief 测试添加列表类型数据
     */
    void onTestAddListData();

    /**
     * @brief 测试表格编辑功能
     */
    void onTestTableEditing();
    
    /**
     * @brief 响应操作完成信号
     * @param success 是否成功
     * @param message 消息内容
     */
    void onOperationCompleted(bool success, const QString &message);
    
    /**
     * @brief 响应连接状态变化信号
     * @param connected 是否已连接
     */
    void onConnectionStatusChanged(bool connected);
    
    /**
     * @brief 响应同步状态变化信号
     * @param syncing 是否正在同步
     */
    void onSyncStatusChanged(bool syncing);
    
    /**
     * @brief 响应忙碌状态变化信号
     * @param busy 是否忙碌
     */
    void onBusyStatusChanged(bool busy);

private:
    /**
     * @brief 设置用户界面
     */
    void setupUI();
    
    /**
     * @brief 设置MVC组件
     */
    void setupMVC();
    
    /**
     * @brief 连接信号槽
     */
    void connectSignals();
    
    /**
     * @brief 添加测试数据
     */
    void addTestData();
    
    /**
     * @brief 更新状态显示
     * @param message 状态消息
     * @param isError 是否为错误状态
     */
    void updateStatus(const QString &message, bool isError = false);

private:
    // ========== MVC组件 ==========
    VarTblModel *m_model = nullptr;
    VarTblController *m_controller = nullptr;
    VarTblView *m_view = nullptr;
    CommandTransceiverRefactored *m_transceiver = nullptr;
    
    // ========== UI组件 ==========
    QWidget *m_centralWidget = nullptr;
    QVBoxLayout *m_mainLayout = nullptr;
    
    // 测试按钮
    QHBoxLayout *m_buttonLayout = nullptr;
    QPushButton *m_addBtn = nullptr;
    QPushButton *m_editBtn = nullptr;
    QPushButton *m_deleteBtn = nullptr;
    QPushButton *m_refreshBtn = nullptr;
    QPushButton *m_syncBtn = nullptr;
    QPushButton *m_connectBtn = nullptr;
    QPushButton *m_disconnectBtn = nullptr;

    // 表格编辑测试按钮
    QPushButton *m_tableEditBtn = nullptr;
    QPushButton *m_addListDataBtn = nullptr;
    QPushButton *m_testTableEditingBtn = nullptr;
    
    // 状态显示
    QHBoxLayout *m_statusLayout = nullptr;
    QLabel *m_statusLabel = nullptr;
    QLabel *m_connectionLabel = nullptr;
    QLabel *m_syncLabel = nullptr;
    QLabel *m_busyLabel = nullptr;
};

#endif // TEST_WINDOW_H
