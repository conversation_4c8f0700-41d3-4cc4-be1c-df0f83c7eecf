/****************************************************************************
** Meta object code from reading C++ file 'NetworkWorker.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../include/NetworkWorker.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'NetworkWorker.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_NetworkWorker_t {
    QByteArrayData data[48];
    char stringdata0[670];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_NetworkWorker_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_NetworkWorker_t qt_meta_stringdata_NetworkWorker = {
    {
QT_MOC_LITERAL(0, 0, 13), // "NetworkWorker"
QT_MOC_LITERAL(1, 14, 11), // "initialized"
QT_MOC_LITERAL(2, 26, 0), // ""
QT_MOC_LITERAL(3, 27, 16), // "cleanupCompleted"
QT_MOC_LITERAL(4, 44, 22), // "connectionStateChanged"
QT_MOC_LITERAL(5, 67, 30), // "NetworkWorker::ConnectionState"
QT_MOC_LITERAL(6, 98, 5), // "state"
QT_MOC_LITERAL(7, 104, 15), // "rawDataReceived"
QT_MOC_LITERAL(8, 120, 4), // "data"
QT_MOC_LITERAL(9, 125, 12), // "networkError"
QT_MOC_LITERAL(10, 138, 12), // "NetworkError"
QT_MOC_LITERAL(11, 151, 5), // "error"
QT_MOC_LITERAL(12, 157, 11), // "description"
QT_MOC_LITERAL(13, 169, 8), // "dataSent"
QT_MOC_LITERAL(14, 178, 12), // "bytesWritten"
QT_MOC_LITERAL(15, 191, 14), // "dataSendFailed"
QT_MOC_LITERAL(16, 206, 18), // "rwsStrategyChanged"
QT_MOC_LITERAL(17, 225, 26), // "IRWSStrategy::StrategyType"
QT_MOC_LITERAL(18, 252, 4), // "type"
QT_MOC_LITERAL(19, 257, 12), // "strategyName"
QT_MOC_LITERAL(20, 270, 10), // "initialize"
QT_MOC_LITERAL(21, 281, 7), // "cleanup"
QT_MOC_LITERAL(22, 289, 15), // "connectToServer"
QT_MOC_LITERAL(23, 305, 4), // "host"
QT_MOC_LITERAL(24, 310, 4), // "port"
QT_MOC_LITERAL(25, 315, 9), // "timeoutMs"
QT_MOC_LITERAL(26, 325, 20), // "disconnectFromServer"
QT_MOC_LITERAL(27, 346, 8), // "sendData"
QT_MOC_LITERAL(28, 355, 16), // "setAutoReconnect"
QT_MOC_LITERAL(29, 372, 7), // "enabled"
QT_MOC_LITERAL(30, 380, 10), // "intervalMs"
QT_MOC_LITERAL(31, 391, 12), // "setHeartbeat"
QT_MOC_LITERAL(32, 404, 4), // "stop"
QT_MOC_LITERAL(33, 409, 18), // "setRWSStrategyType"
QT_MOC_LITERAL(34, 428, 20), // "setRWSStrategyConfig"
QT_MOC_LITERAL(35, 449, 9), // "RWSConfig"
QT_MOC_LITERAL(36, 459, 6), // "config"
QT_MOC_LITERAL(37, 466, 22), // "getCurrentStrategyType"
QT_MOC_LITERAL(38, 489, 17), // "onSocketConnected"
QT_MOC_LITERAL(39, 507, 20), // "onSocketDisconnected"
QT_MOC_LITERAL(40, 528, 13), // "onSocketError"
QT_MOC_LITERAL(41, 542, 28), // "QAbstractSocket::SocketError"
QT_MOC_LITERAL(42, 571, 17), // "onSocketReadyRead"
QT_MOC_LITERAL(43, 589, 20), // "onSocketBytesWritten"
QT_MOC_LITERAL(44, 610, 5), // "bytes"
QT_MOC_LITERAL(45, 616, 16), // "onReconnectTimer"
QT_MOC_LITERAL(46, 633, 16), // "onHeartbeatTimer"
QT_MOC_LITERAL(47, 650, 19) // "onConnectionTimeout"

    },
    "NetworkWorker\0initialized\0\0cleanupCompleted\0"
    "connectionStateChanged\0"
    "NetworkWorker::ConnectionState\0state\0"
    "rawDataReceived\0data\0networkError\0"
    "NetworkError\0error\0description\0dataSent\0"
    "bytesWritten\0dataSendFailed\0"
    "rwsStrategyChanged\0IRWSStrategy::StrategyType\0"
    "type\0strategyName\0initialize\0cleanup\0"
    "connectToServer\0host\0port\0timeoutMs\0"
    "disconnectFromServer\0sendData\0"
    "setAutoReconnect\0enabled\0intervalMs\0"
    "setHeartbeat\0stop\0setRWSStrategyType\0"
    "setRWSStrategyConfig\0RWSConfig\0config\0"
    "getCurrentStrategyType\0onSocketConnected\0"
    "onSocketDisconnected\0onSocketError\0"
    "QAbstractSocket::SocketError\0"
    "onSocketReadyRead\0onSocketBytesWritten\0"
    "bytes\0onReconnectTimer\0onHeartbeatTimer\0"
    "onConnectionTimeout"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_NetworkWorker[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      30,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       8,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,  164,    2, 0x06 /* Public */,
       3,    0,  165,    2, 0x06 /* Public */,
       4,    1,  166,    2, 0x06 /* Public */,
       7,    1,  169,    2, 0x06 /* Public */,
       9,    2,  172,    2, 0x06 /* Public */,
      13,    1,  177,    2, 0x06 /* Public */,
      15,    1,  180,    2, 0x06 /* Public */,
      16,    2,  183,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      20,    0,  188,    2, 0x0a /* Public */,
      21,    0,  189,    2, 0x0a /* Public */,
      22,    3,  190,    2, 0x0a /* Public */,
      22,    2,  197,    2, 0x2a /* Public | MethodCloned */,
      26,    0,  202,    2, 0x0a /* Public */,
      27,    1,  203,    2, 0x0a /* Public */,
      28,    2,  206,    2, 0x0a /* Public */,
      28,    1,  211,    2, 0x2a /* Public | MethodCloned */,
      31,    2,  214,    2, 0x0a /* Public */,
      31,    1,  219,    2, 0x2a /* Public | MethodCloned */,
      32,    0,  222,    2, 0x0a /* Public */,
      33,    1,  223,    2, 0x0a /* Public */,
      34,    1,  226,    2, 0x0a /* Public */,
      37,    0,  229,    2, 0x0a /* Public */,
      38,    0,  230,    2, 0x08 /* Private */,
      39,    0,  231,    2, 0x08 /* Private */,
      40,    1,  232,    2, 0x08 /* Private */,
      42,    0,  235,    2, 0x08 /* Private */,
      43,    1,  236,    2, 0x08 /* Private */,
      45,    0,  239,    2, 0x08 /* Private */,
      46,    0,  240,    2, 0x08 /* Private */,
      47,    0,  241,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 5,    6,
    QMetaType::Void, QMetaType::QByteArray,    8,
    QMetaType::Void, 0x80000000 | 10, QMetaType::QString,   11,   12,
    QMetaType::Void, QMetaType::LongLong,   14,
    QMetaType::Void, QMetaType::QString,   11,
    QMetaType::Void, 0x80000000 | 17, QMetaType::QString,   18,   19,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::UShort, QMetaType::Int,   23,   24,   25,
    QMetaType::Void, QMetaType::QString, QMetaType::UShort,   23,   24,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QByteArray,    8,
    QMetaType::Void, QMetaType::Bool, QMetaType::Int,   29,   30,
    QMetaType::Void, QMetaType::Bool,   29,
    QMetaType::Void, QMetaType::Bool, QMetaType::Int,   29,   30,
    QMetaType::Void, QMetaType::Bool,   29,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 17,   18,
    QMetaType::Void, 0x80000000 | 35,   36,
    0x80000000 | 17,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 41,   11,
    QMetaType::Void,
    QMetaType::Void, QMetaType::LongLong,   44,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void NetworkWorker::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<NetworkWorker *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->initialized(); break;
        case 1: _t->cleanupCompleted(); break;
        case 2: _t->connectionStateChanged((*reinterpret_cast< NetworkWorker::ConnectionState(*)>(_a[1]))); break;
        case 3: _t->rawDataReceived((*reinterpret_cast< const QByteArray(*)>(_a[1]))); break;
        case 4: _t->networkError((*reinterpret_cast< NetworkError(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 5: _t->dataSent((*reinterpret_cast< qint64(*)>(_a[1]))); break;
        case 6: _t->dataSendFailed((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->rwsStrategyChanged((*reinterpret_cast< IRWSStrategy::StrategyType(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 8: _t->initialize(); break;
        case 9: _t->cleanup(); break;
        case 10: _t->connectToServer((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 11: _t->connectToServer((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2]))); break;
        case 12: _t->disconnectFromServer(); break;
        case 13: _t->sendData((*reinterpret_cast< const QByteArray(*)>(_a[1]))); break;
        case 14: _t->setAutoReconnect((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 15: _t->setAutoReconnect((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 16: _t->setHeartbeat((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 17: _t->setHeartbeat((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 18: _t->stop(); break;
        case 19: _t->setRWSStrategyType((*reinterpret_cast< IRWSStrategy::StrategyType(*)>(_a[1]))); break;
        case 20: _t->setRWSStrategyConfig((*reinterpret_cast< const RWSConfig(*)>(_a[1]))); break;
        case 21: { IRWSStrategy::StrategyType _r = _t->getCurrentStrategyType();
            if (_a[0]) *reinterpret_cast< IRWSStrategy::StrategyType*>(_a[0]) = std::move(_r); }  break;
        case 22: _t->onSocketConnected(); break;
        case 23: _t->onSocketDisconnected(); break;
        case 24: _t->onSocketError((*reinterpret_cast< QAbstractSocket::SocketError(*)>(_a[1]))); break;
        case 25: _t->onSocketReadyRead(); break;
        case 26: _t->onSocketBytesWritten((*reinterpret_cast< qint64(*)>(_a[1]))); break;
        case 27: _t->onReconnectTimer(); break;
        case 28: _t->onHeartbeatTimer(); break;
        case 29: _t->onConnectionTimeout(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 2:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< NetworkWorker::ConnectionState >(); break;
            }
            break;
        case 24:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QAbstractSocket::SocketError >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (NetworkWorker::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&NetworkWorker::initialized)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (NetworkWorker::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&NetworkWorker::cleanupCompleted)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (NetworkWorker::*)(NetworkWorker::ConnectionState );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&NetworkWorker::connectionStateChanged)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (NetworkWorker::*)(const QByteArray & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&NetworkWorker::rawDataReceived)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (NetworkWorker::*)(NetworkError , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&NetworkWorker::networkError)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (NetworkWorker::*)(qint64 );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&NetworkWorker::dataSent)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (NetworkWorker::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&NetworkWorker::dataSendFailed)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (NetworkWorker::*)(IRWSStrategy::StrategyType , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&NetworkWorker::rwsStrategyChanged)) {
                *result = 7;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject NetworkWorker::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_NetworkWorker.data,
    qt_meta_data_NetworkWorker,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *NetworkWorker::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *NetworkWorker::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_NetworkWorker.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int NetworkWorker::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 30)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 30;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 30)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 30;
    }
    return _id;
}

// SIGNAL 0
void NetworkWorker::initialized()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void NetworkWorker::cleanupCompleted()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void NetworkWorker::connectionStateChanged(NetworkWorker::ConnectionState _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void NetworkWorker::rawDataReceived(const QByteArray & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void NetworkWorker::networkError(NetworkError _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void NetworkWorker::dataSent(qint64 _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void NetworkWorker::dataSendFailed(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void NetworkWorker::rwsStrategyChanged(IRWSStrategy::StrategyType _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
