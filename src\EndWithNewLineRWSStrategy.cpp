#include "EndWithNewLineRWSStrategy.h"
#include <QDebug>

const QByteArray EndWithNewLineRWSStrategy::DEFAULT_DELIMITER = "\n";

EndWithNewLineRWSStrategy::EndWithNewLineRWSStrategy(const EndWithNewLineConfig &config)
    : m_config(config)
{
    qDebug() << "EndWithNewLineRWSStrategy: 初始化完成，分隔符:" << m_config.delimiter;
}

EndWithNewLineRWSStrategy::~EndWithNewLineRWSStrategy()
{
    qDebug() << "EndWithNewLineRWSStrategy: 析构";
}

QByteArray EndWithNewLineRWSStrategy::wrapMessage(const QByteArray &message)
{
    if (message.isEmpty()) {
        return QByteArray();
    }

    // 添加分隔符
    QByteArray packet = message;
    packet.append(m_config.delimiter);

    return packet;
}

QList<QByteArray> EndWithNewLineRWSStrategy::processReceivedData(const QByteArray &newData)
{
    if (newData.isEmpty()) {
        return QList<QByteArray>();
    }

    // 添加新数据到缓冲区
    m_receiveBuffer.append(newData);

    // 提取完整行
    return extractCompleteLines();
}

void EndWithNewLineRWSStrategy::clearBuffer()
{
    m_receiveBuffer.clear();
    qDebug() << "EndWithNewLineRWSStrategy: 缓冲区已清空";
}

QList<QByteArray> EndWithNewLineRWSStrategy::extractCompleteLines()
{
    QList<QByteArray> lines;

    int delimiterPos;
    while ((delimiterPos = m_receiveBuffer.indexOf(m_config.delimiter)) != -1) {
        // 提取一行数据（不包含分隔符）
        QByteArray line = m_receiveBuffer.left(delimiterPos);

        // 验证行长度
        if (isValidLineLength(line.size())) {
            lines.append(line);
        } else {
            qWarning() << "EndWithNewLineRWSStrategy: 行长度超出限制:" << line.size();
        }

        // 从缓冲区移除已处理的数据（包含分隔符）
        m_receiveBuffer.remove(0, delimiterPos + m_config.delimiter.size());
    }

    return lines;
}

bool EndWithNewLineRWSStrategy::isValidLineLength(int lineLength) const
{
    return lineLength <= m_config.maxLineLength;
}


