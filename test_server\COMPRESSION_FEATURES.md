# CommandTransceiver 测试服务器 - 压缩功能

## 概述

本文档描述了 CommandTransceiver 测试服务器新增的数据压缩功能。该功能允许服务器自动检测和处理客户端发送的压缩数据，完全兼容 Qt 的 qCompress/qUncompress 函数。

## 功能特性

### 🗜️ 自动压缩检测
- 自动识别 qCompress 格式的压缩数据
- 透明解压缩，不影响现有协议处理
- 兼容非压缩数据，保持向后兼容性

### 📊 压缩统计
- 实时统计压缩数据包数量
- 记录压缩前后的数据大小
- 计算压缩率和节省的带宽

### 🔧 协议兼容性
- 支持所有三种 RWS 协议（PreLength、EndWithNewLine、Simple）
- 压缩检测在协议处理之前进行
- 不改变现有的协议处理逻辑

## 技术实现

### 数据流程
```
CommandTransceiver 客户端:
JSON对象 → QJsonDocument::toBinaryData() → qCompress() → PreLength协议封装

测试服务器:
PreLength协议解析 → qUncompress() → JSON解析
```

### 压缩格式
```
qCompress 格式（Qt标准）：
[4字节原始长度(大端序)] + [zlib压缩数据]
```

### 检测算法
1. 协议解析后提取消息数据
2. 尝试使用 qUncompress 解压缩
3. 如果解压缩成功，则认为是压缩数据
4. 否则按未压缩数据处理

### 核心类和方法

#### RWSProtocolHandler
- `isCompressedData(const QByteArray& data)` - 检测是否为压缩数据
- `decompressData(const QByteArray& data)` - 解压缩数据
- `getCompressionStats()` - 获取压缩统计信息

#### ClientConnection
- `getCompressionStats()` - 获取连接的压缩统计

#### TestServer
- 支持 `compression_stats` 特殊命令查询压缩统计

## 使用方法

### 服务器端
服务器会自动处理压缩数据，无需额外配置。

### 客户端发送压缩数据

#### C++ (CommandTransceiver)
```cpp
// CommandTransceiver 框架自动处理压缩
QJsonObject command;
command["type"] = "test";
command["data"] = "your data here";

// 框架内部流程：
// 1. QJsonDocument doc(command);
// 2. QByteArray binaryData = doc.toBinaryData();
// 3. QByteArray compressed = qCompress(binaryData);
// 4. 通过网络发送

commandTransceiver->sendCommand(command);
```

#### Python (测试客户端)
```python
import zlib
import struct
import json

def simulate_commandtransceiver_format(json_data, use_compression=True):
    # 模拟 QJsonDocument::toBinaryData()
    json_str = json.dumps(json_data, ensure_ascii=False)
    binary_data = json_str.encode('utf-8')

    if use_compression:
        # Qt qCompress 格式
        compressed = zlib.compress(binary_data)
        return struct.pack('>I', len(binary_data)) + compressed
    else:
        return binary_data

# 使用示例
data = {"type": "test", "data": "your data here"}
compressed_data = simulate_commandtransceiver_format(data, True)
# 通过 PreLength 协议发送
```

### 查询压缩统计
```json
{
    "type": "compression_stats"
}
```

响应示例：
```json
{
    "type": "compression_stats_response",
    "connectionId": "connection-uuid",
    "compressedPacketsReceived": 5,
    "totalCompressedBytes": 1024,
    "totalUncompressedBytes": 4096,
    "compressionRatio": 75.0
}
```

## 测试工具

### 1. 基本测试客户端
```bash
# 启用压缩功能的测试
python test_client.py 8080
```

### 2. 压缩功能演示
```bash
# 运行压缩对比演示
python compression_demo.py 8080
```

### 3. 测试场景
- 高重复性数据压缩测试
- 随机数据压缩测试
- 结构化JSON数据压缩测试
- 压缩统计查询测试

## 性能影响

### 优势
- 减少网络带宽使用
- 对重复性数据压缩效果显著
- 透明处理，不影响现有代码

### 考虑因素
- 增加少量CPU开销用于解压缩
- 对于小数据包，压缩可能不划算
- 压缩检测有轻微的性能开销

## 兼容性

### Qt 版本
- 支持 Qt 5.12 及以上版本
- 使用 QtCompatibility.h 处理版本差异

### 协议兼容性
- 完全向后兼容非压缩数据
- 支持混合使用压缩和非压缩数据
- 不影响现有的协议处理逻辑

## 故障排除

### 常见问题

1. **压缩数据无法识别**
   - 检查数据格式是否符合 qCompress 标准
   - 确认前4字节是否为正确的原始长度

2. **解压缩失败**
   - 验证压缩数据的完整性
   - 检查是否使用了正确的压缩算法（zlib）

3. **统计信息不准确**
   - 确认查询的是正确的连接ID
   - 检查是否有多个连接混合统计

### 调试方法
- 启用详细日志输出
- 使用压缩演示工具验证功能
- 检查服务器控制台的压缩统计信息

## 未来扩展

### 可能的改进
- 支持不同的压缩算法
- 可配置的压缩阈值
- 压缩率优化建议
- 更详细的性能统计

### API 扩展
- 压缩配置命令
- 实时压缩率监控
- 压缩性能分析工具
