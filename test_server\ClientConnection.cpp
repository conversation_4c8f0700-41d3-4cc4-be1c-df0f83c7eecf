#include "ClientConnection.h"
#include "QtCompatibility.h"
#include <QDebug>
#include <QJsonParseError>
#include <QHostAddress>
#include <QAbstractSocket>

ClientConnection::ClientConnection(QTcpSocket *socket, 
                                 RWSProtocolHandler::ProtocolType protocolType,
                                 QObject *parent)
    : QObject(parent)
    , m_connectionId(QUuid::createUuid())
    , m_socket(socket)
    , m_protocolHandler(new RWSProtocolHandler(protocolType, this))
    , m_connectionState(Disconnected)
    , m_connectTime(QDateTime::currentDateTime())
    , m_heartbeatTimer(new QTimer(this))
    , m_heartbeatTimeoutTimer(new QTimer(this))
    , m_heartbeatEnabled(false)
    , m_heartbeatInterval(30000)
    , m_bytesReceived(0)
    , m_bytesSent(0)
    , m_messagesReceived(0)
    , m_messagesSent(0)
    , m_jsonCommandsReceived(0)
    , m_errorsCount(0)
{
    // 设置socket父对象
    m_socket->setParent(this);
    
    // 连接socket信号
    connect(m_socket, &QTcpSocket::readyRead, this, &ClientConnection::onSocketReadyRead);
    connect(m_socket, &QTcpSocket::disconnected, this, &ClientConnection::onSocketDisconnected);
    // Qt 版本兼容性处理
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::QT_SOCKET_ERROR_SIGNAL),
            this, &ClientConnection::onSocketError);
    
    // 配置心跳定时器
    m_heartbeatTimer->setSingleShot(false);
    connect(m_heartbeatTimer, &QTimer::timeout, this, &ClientConnection::sendHeartbeat);
    
    m_heartbeatTimeoutTimer->setSingleShot(true);
    connect(m_heartbeatTimeoutTimer, &QTimer::timeout, this, &ClientConnection::onHeartbeatTimeout);
    
    // 设置连接状态
    setConnectionState(Connected);
    
    qDebug() << "ClientConnection: 新连接已建立" 
             << "ID:" << m_connectionId.toString()
             << "地址:" << getClientAddress()
             << "协议:" << m_protocolHandler->getProtocolName();
}

ClientConnection::~ClientConnection()
{
    qDebug() << "ClientConnection: 连接已销毁" << "ID:" << m_connectionId.toString();
}

QString ClientConnection::getClientAddress() const
{
    if (m_socket) {
        return QString("%1:%2").arg(m_socket->peerAddress().toString()).arg(m_socket->peerPort());
    }
    return "Unknown";
}

qint64 ClientConnection::getConnectionDuration() const
{
    return m_connectTime.secsTo(QDateTime::currentDateTime());
}

bool ClientConnection::sendJsonResponse(const QJsonObject &response)
{
    QJsonDocument doc(response);
    //QByteArray jsonData = doc.toJson(QJsonDocument::Compact);
    QByteArray jsonData = doc.toBinaryData();
    return sendRawData(jsonData);
}

bool ClientConnection::sendRawData(const QByteArray &data)
{
    if (!m_socket || m_socket->state() != QAbstractSocket::ConnectedState) {
        qWarning() << "ClientConnection: 尝试向未连接的socket发送数据" << "ID:" << m_connectionId.toString();
        return false;
    }
    
    if (data.isEmpty()) {
        return true;
    }
    
    QByteArray encodedData = qCompress(data);
    // 使用协议处理器封装数据
    QByteArray wrappedData = m_protocolHandler->wrapMessage(encodedData);
    
    qint64 bytesWritten = m_socket->write(wrappedData);
    if (bytesWritten == -1) {
        qWarning() << "ClientConnection: 数据发送失败" << "ID:" << m_connectionId.toString();
        m_errorsCount++;
        return false;
    }
    
    m_bytesSent += bytesWritten;
    m_messagesSent++;
    
    qDebug() << "ClientConnection: 数据已发送" 
             << "ID:" << m_connectionId.toString()
             << "字节数:" << bytesWritten
             << "原始数据大小:" << data.size();
    
    return true;
}

void ClientConnection::setProtocolType(RWSProtocolHandler::ProtocolType type)
{
    m_protocolHandler->setProtocolType(type);
    qDebug() << "ClientConnection: 协议类型已切换" 
             << "ID:" << m_connectionId.toString()
             << "新协议:" << m_protocolHandler->getProtocolName();
}

RWSProtocolHandler::ProtocolType ClientConnection::getProtocolType() const
{
    return m_protocolHandler->getProtocolType();
}

void ClientConnection::setHeartbeatEnabled(bool enabled, int intervalMs)
{
    m_heartbeatEnabled = enabled;
    m_heartbeatInterval = intervalMs;
    
    if (enabled) {
        m_heartbeatTimer->start(intervalMs);
        qDebug() << "ClientConnection: 心跳检测已启用" 
                 << "ID:" << m_connectionId.toString()
                 << "间隔:" << intervalMs << "ms";
    } else {
        m_heartbeatTimer->stop();
        m_heartbeatTimeoutTimer->stop();
        qDebug() << "ClientConnection: 心跳检测已禁用" << "ID:" << m_connectionId.toString();
    }
}

void ClientConnection::disconnectClient()
{
    if (m_socket && m_socket->state() == QAbstractSocket::ConnectedState) {
        qDebug() << "ClientConnection: 主动断开连接" << "ID:" << m_connectionId.toString();
        m_socket->disconnectFromHost();
    }
}

QJsonObject ClientConnection::getStatistics() const
{
    QJsonObject stats;
    stats["connectionId"] = m_connectionId.toString();
    stats["clientAddress"] = getClientAddress();
    stats["connectionDuration"] = getConnectionDuration();
    stats["protocolType"] = m_protocolHandler->getProtocolName();
    stats["connectionState"] = static_cast<int>(m_connectionState);
    stats["bytesReceived"] = m_bytesReceived;
    stats["bytesSent"] = m_bytesSent;
    stats["messagesReceived"] = m_messagesReceived;
    stats["messagesSent"] = m_messagesSent;
    stats["jsonCommandsReceived"] = m_jsonCommandsReceived;
    stats["errorsCount"] = m_errorsCount;
    stats["heartbeatEnabled"] = m_heartbeatEnabled;
    
    if (m_heartbeatEnabled) {
        stats["lastHeartbeatTime"] = m_lastHeartbeatTime.toString(Qt::ISODate);
    }
    
    return stats;
}

QJsonObject ClientConnection::getCompressionStats() const
{
    QJsonObject stats;
    stats["connectionId"] = m_connectionId.toString();

    // 获取协议处理器的压缩统计
    if (m_protocolHandler) {
        QVariantMap compressionStats = m_protocolHandler->getCompressionStats();
        for (auto it = compressionStats.begin(); it != compressionStats.end(); ++it) {
            stats[it.key()] = QJsonValue::fromVariant(it.value());
        }
    }

    return stats;
}

void ClientConnection::onSocketReadyRead()
{
    if (!m_socket) {
        return;
    }
    
    QByteArray newData = m_socket->readAll();
    if (newData.isEmpty()) {
        return;
    }
    
    m_bytesReceived += newData.size();
    
    qDebug() << "ClientConnection: 接收到数据" 
             << "ID:" << m_connectionId.toString()
             << "字节数:" << newData.size();
    
    // 使用协议处理器解析数据
    QList<QByteArray> messages = m_protocolHandler->processReceivedData(newData);
    
    // 处理每个完整消息
    for (const QByteArray &message : messages) {
        processMessage(message);
    }
}

void ClientConnection::onSocketDisconnected()
{
    qDebug() << "ClientConnection: 连接已断开" << "ID:" << m_connectionId.toString();
    setConnectionState(Disconnected);
    emit connectionDisconnected(m_connectionId);
}

void ClientConnection::onSocketError(QAbstractSocket::SocketError error)
{
    QString errorString = m_socket ? m_socket->errorString() : "Unknown error";
    qWarning() << "ClientConnection: Socket错误" 
               << "ID:" << m_connectionId.toString()
               << "错误码:" << error
               << "错误信息:" << errorString;
    
    m_errorsCount++;
    emit connectionError(m_connectionId, errorString);
}

void ClientConnection::onHeartbeatTimeout()
{
    qWarning() << "ClientConnection: 心跳超时" << "ID:" << m_connectionId.toString();
    disconnectClient();
}

void ClientConnection::sendHeartbeat()
{
    QJsonObject heartbeat;
    heartbeat["type"] = "heartbeat";
    heartbeat["timestamp"] = QDateTime::currentMSecsSinceEpoch();
    heartbeat["serverId"] = "CommandTransceiverTestServer";
    
    if (sendJsonResponse(heartbeat)) {
        m_lastHeartbeatTime = QDateTime::currentDateTime();
        qDebug() << "ClientConnection: 心跳包已发送" << "ID:" << m_connectionId.toString();
    }
}

void ClientConnection::setConnectionState(ConnectionState state)
{
    if (m_connectionState != state) {
        m_connectionState = state;
        emit connectionStateChanged(m_connectionId, state);
    }
}

void ClientConnection::processMessage(const QByteArray &message)
{
    if (message.isEmpty()) {
        return;
    }
    
    m_messagesReceived++;
    
    qDebug() << "ClientConnection: 处理消息" 
             << "ID:" << m_connectionId.toString()
             << "大小:" << message.size();
    
    // 尝试解析为JSON命令
    if (!tryParseJsonCommand(message)) {
        // 如果不是JSON，作为原始数据处理
        emit rawDataReceived(m_connectionId, message);
    }
}

bool ClientConnection::tryParseJsonCommand(const QByteArray &data)
{
    QJsonDocument doc;

    // 先尝试解析为Qt二进制JSON格式（CommandTransceiver客户端使用的格式）
    doc = QJsonDocument::fromBinaryData(data);
    if (!doc.isNull() && doc.isObject()) {
        qDebug() << "ClientConnection: 解析为二进制JSON格式成功"
                 << "ID:" << m_connectionId.toString();
    } else {
        // 如果二进制解析失败，尝试文本JSON格式
        QJsonParseError parseError;
        doc = QJsonDocument::fromJson(data, &parseError);

        if (parseError.error != QJsonParseError::NoError) {
            // 不是有效的JSON数据
            qDebug() << "ClientConnection: JSON解析失败"
                     << "ID:" << m_connectionId.toString()
                     << "错误:" << parseError.errorString()
                     << "数据大小:" << data.size();
            return false;
        }

        if (!doc.isObject()) {
            // JSON不是对象格式
            return false;
        }

        qDebug() << "ClientConnection: 解析为文本JSON格式成功"
                 << "ID:" << m_connectionId.toString();
    }
    
    QJsonObject command = doc.object();
    m_jsonCommandsReceived++;
    
    qDebug() << "ClientConnection: 接收到JSON命令"
             << "ID:" << m_connectionId.toString()
             << "命令:" << QT_JSON_TO_STRING(doc);
    
    // 检查是否是心跳响应
    if (command.value("type").toString() == "heartbeat_response") {
        // 重置心跳超时定时器
        if (m_heartbeatEnabled) {
            m_heartbeatTimeoutTimer->start(m_heartbeatInterval * 2); // 超时时间为心跳间隔的2倍
        }
        return true;
    }
    
    // 发出JSON命令信号
    emit jsonCommandReceived(m_connectionId, command);
    
    return true;
}

QJsonObject ClientConnection::createHeartbeatResponse(const QJsonObject &heartbeatCommand)
{
    QJsonObject response;
    response["type"] = "heartbeat_response";
    response["timestamp"] = QDateTime::currentMSecsSinceEpoch();
    response["clientId"] = m_connectionId.toString();

    // 如果心跳命令包含ID，则回显
    if (heartbeatCommand.contains("id")) {
        response["id"] = heartbeatCommand.value("id");
    }

    return response;
}

QJsonObject ClientConnection::createErrorResponse(const QString &error, const QUuid &commandUuid)
{
    QJsonObject response;
    response["type"] = "error";
    response["error"] = error;
    response["timestamp"] = QDateTime::currentMSecsSinceEpoch();

    if (!commandUuid.isNull()) {
        response["commandId"] = commandUuid.toString();
    }

    return response;
}
