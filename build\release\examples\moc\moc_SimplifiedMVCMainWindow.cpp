/****************************************************************************
** Meta object code from reading C++ file 'SimplifiedMVCMainWindow.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../examples/SimplifiedMVCMainWindow.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'SimplifiedMVCMainWindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_SimplifiedMVCMainWindow_t {
    QByteArrayData data[12];
    char stringdata0[212];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_SimplifiedMVCMainWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_SimplifiedMVCMainWindow_t qt_meta_stringdata_SimplifiedMVCMainWindow = {
    {
QT_MOC_LITERAL(0, 0, 23), // "SimplifiedMVCMainWindow"
QT_MOC_LITERAL(1, 24, 16), // "onConnectClicked"
QT_MOC_LITERAL(2, 41, 0), // ""
QT_MOC_LITERAL(3, 42, 19), // "onDisconnectClicked"
QT_MOC_LITERAL(4, 62, 22), // "onOperationModeChanged"
QT_MOC_LITERAL(5, 85, 24), // "onConnectionStateChanged"
QT_MOC_LITERAL(6, 110, 30), // "NetworkWorker::ConnectionState"
QT_MOC_LITERAL(7, 141, 5), // "state"
QT_MOC_LITERAL(8, 147, 26), // "onVariableSelectionChanged"
QT_MOC_LITERAL(9, 174, 13), // "selectedCount"
QT_MOC_LITERAL(10, 188, 15), // "onStatusMessage"
QT_MOC_LITERAL(11, 204, 7) // "message"

    },
    "SimplifiedMVCMainWindow\0onConnectClicked\0"
    "\0onDisconnectClicked\0onOperationModeChanged\0"
    "onConnectionStateChanged\0"
    "NetworkWorker::ConnectionState\0state\0"
    "onVariableSelectionChanged\0selectedCount\0"
    "onStatusMessage\0message"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_SimplifiedMVCMainWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   44,    2, 0x08 /* Private */,
       3,    0,   45,    2, 0x08 /* Private */,
       4,    0,   46,    2, 0x08 /* Private */,
       5,    1,   47,    2, 0x08 /* Private */,
       8,    1,   50,    2, 0x08 /* Private */,
      10,    1,   53,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 6,    7,
    QMetaType::Void, QMetaType::Int,    9,
    QMetaType::Void, QMetaType::QString,   11,

       0        // eod
};

void SimplifiedMVCMainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SimplifiedMVCMainWindow *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onConnectClicked(); break;
        case 1: _t->onDisconnectClicked(); break;
        case 2: _t->onOperationModeChanged(); break;
        case 3: _t->onConnectionStateChanged((*reinterpret_cast< NetworkWorker::ConnectionState(*)>(_a[1]))); break;
        case 4: _t->onVariableSelectionChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 5: _t->onStatusMessage((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 3:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< NetworkWorker::ConnectionState >(); break;
            }
            break;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject SimplifiedMVCMainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_SimplifiedMVCMainWindow.data,
    qt_meta_data_SimplifiedMVCMainWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *SimplifiedMVCMainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SimplifiedMVCMainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SimplifiedMVCMainWindow.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int SimplifiedMVCMainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
