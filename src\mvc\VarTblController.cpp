#include "mvc/VarTblController.h"
#include <QDebug>
#include <QMutexLocker>
#include <QJsonDocument>
#include <QUuid>

VarTblController::VarTblController(QObject *parent)
    : QObject(parent)
    , m_model(nullptr)
    , m_transceiver(nullptr)
    , m_projectUuid(QUuid::createUuid())
    , m_syncStatus(SyncStatus::Idle)
    , m_connected(false)
    , m_waitForServerResponse(true)      // 默认等待服务器响应
    , m_serverResponseTimeout(5000)     // 默认5秒超时
    , m_autoSyncTimer(new QTimer(this))
    , m_autoSyncInterval(0)
{
    qDebug() << "VarTblController: 创建实例";
    
    // 设置自动同步定时器
    m_autoSyncTimer->setSingleShot(false);
    connect(m_autoSyncTimer, &QTimer::timeout, this, &VarTblController::onAutoSyncTimeout);
}

VarTblController::~VarTblController()
{
    qDebug() << "VarTblController: 销毁实例";
}

// ========== 组件设置 ==========

void VarTblController::setModel(VarTblModel *model)
{
    if (m_model == model) {
        return;
    }
    
    if (m_model) {
        disconnect(m_model, nullptr, this, nullptr);
    }
    
    m_model = model;
    
    if (m_model) {
        // 连接模型信号 - 用于业务逻辑处理
        connect(m_model, &VarTblModel::variableDataChanged, this, &VarTblController::onModelDataChanged);
    }
    
    qDebug() << "VarTblController: 设置数据模型" << model;
}

void VarTblController::setCommandTransceiver(CommandTransceiverRefactored *transceiver)
{
    if (m_transceiver == transceiver) {
        return;
    }
    
    if (m_transceiver) {
        disconnect(m_transceiver, nullptr, this, nullptr);
    }
    
    m_transceiver = transceiver;
    
    if (m_transceiver) {
        // 连接通信组件信号
        connect(m_transceiver, &CommandTransceiverRefactored::connectionStateChanged,
                this, &VarTblController::onConnectionStateChanged);
        connect(m_transceiver, &CommandTransceiverRefactored::commandResponseReceived,
                this, &VarTblController::onCommandResponseReceived);
        
        // 更新连接状态
        m_connected = m_transceiver->isConnected();
        emit connectionStatusChanged(m_connected);
    }
    
    qDebug() << "VarTblController: 设置通信组件" << transceiver;
}

void VarTblController::setProjectUuid(const QUuid &projectUuid)
{
    m_projectUuid = projectUuid;
    qDebug() << "VarTblController: 设置项目UUID" << projectUuid.toString();
}

// ========== 数据操作接口 ==========

void VarTblController::addVariable(int id, const cmd::Var &var, const QString &label, bool syncToServer)
{
    if (!m_model) {
        emitOperationCompleted("add", false, tr("数据模型未设置"), id);
        return;
    }

    // 验证数据
    auto [valid, errorMsg] = validateVariable(var, label);
    if (!valid) {
        emitOperationCompleted("add", false, errorMsg, id);
        return;
    }

    // 更新本地模型 - Model会自动通知View更新
    bool isNew = m_model->insertOrUpdateVariable(id, var, label);
    if (!isNew) {
        qDebug() << "VarTblController: 变量ID已存在，执行更新操作" << id;
    }

    // 同步到服务器
    if (syncToServer && m_transceiver && m_connected) {
        QUuid commandUuid = sendVarUpdateForAdd(id, var, label);
        if (commandUuid.isNull()) {
            emitOperationCompleted("add", false, tr("发送命令失败"), id);
        }
    } else {
        emitOperationCompleted("add", true, tr("变量添加成功（仅本地）"), id);
    }

    qDebug() << "VarTblController: 添加变量，ID=" << id << "标签=" << label
             << "同步=" << syncToServer;
}

void VarTblController::updateVariable(int id, const cmd::Var &var, const QString &label, bool syncToServer)
{
    if (!m_model) {
        emitOperationCompleted("update", false, tr("数据模型未设置"), id);
        return;
    }

    // 验证数据
    auto [valid, errorMsg] = validateVariable(var, label);
    if (!valid) {
        emitOperationCompleted("update", false, errorMsg, id);
        return;
    }

    // 检查变量是否存在
    if (!m_model->containsVariable(id)) {
        emitOperationCompleted("update", false, tr("变量不存在"), id);
        return;
    }

    // 更新本地模型 - Model会自动通知View更新
    m_model->insertOrUpdateVariable(id, var, label);

    // 同步到服务器
    if (syncToServer && m_transceiver && m_connected) {
        QUuid commandUuid = sendVarUpdateCommand(id, var, label, "update");
        if (commandUuid.isNull()) {
            emitOperationCompleted("update", false, tr("发送命令失败"), id);
        }
    } else {
        emitOperationCompleted("update", true, tr("变量更新成功（仅本地）"), id);
    }

    qDebug() << "VarTblController: 更新变量，ID=" << id << "标签=" << label
             << "同步=" << syncToServer;
}

void VarTblController::removeVariable(int id, bool syncToServer)
{
    if (!m_model) {
        emitOperationCompleted("remove", false, tr("数据模型未设置"), id);
        return;
    }

    // 检查变量是否存在
    if (!m_model->containsVariable(id)) {
        emitOperationCompleted("remove", false, tr("变量不存在"), id);
        return;
    }

    // 删除本地模型中的变量 - Model会自动通知View更新
    bool success = m_model->removeVariable(id);
    if (!success) {
        emitOperationCompleted("remove", false, tr("删除变量失败"), id);
        return;
    }

    // 同步到服务器
    if (syncToServer && m_transceiver && m_connected) {
        QUuid commandUuid = sendVarRemoveCommand(id);
        if (commandUuid.isNull()) {
            emitOperationCompleted("remove", false, tr("发送命令失败"), id);
        } else if (m_waitForServerResponse) {
            // 同步模式：等待服务器响应
            QTimer::singleShot(m_serverResponseTimeout, this, [this, commandUuid]() {
                handleCommandTimeout(commandUuid);
            });
            qDebug() << "VarTblController: 删除操作 - 同步模式，等待服务器响应";
        } else {
            // 异步模式：立即完成操作
            emitOperationCompleted("remove", true, tr("变量删除成功（已发送到服务器）"), id);
            qDebug() << "VarTblController: 删除操作 - 异步模式，立即完成";
        }
    } else {
        emitOperationCompleted("remove", true, tr("变量删除成功（仅本地）"), id);
    }

    qDebug() << "VarTblController: 删除变量，ID=" << id << "同步=" << syncToServer;
}

void VarTblController::updateVariables(const cmd::VarTbl &varTbl, bool syncToServer)
{
    if (!m_model) {
        emit operationCompleted(false, tr("数据模型未设置"));
        return;
    }

    // 更新本地模型 - Model会自动通知View更新
    m_model->setVarTbl(varTbl);

    // 实现批量同步到服务器的逻辑
    if (syncToServer && m_transceiver && m_connected) {
        syncVariablesToServer(varTbl);
    }

    emit operationCompleted(true, tr("批量更新成功，共%1个变量").arg(varTbl.size()));

    qDebug() << "VarTblController: 批量更新变量，共" << varTbl.size() << "个";
}

// ========== View数据处理接口 ==========

bool VarTblController::handleAddVariable(const QString &idText, const QString &label, const QString &typeText, const QVariant &value)
{
    // 验证和转换ID
    bool idOk;
    int id = idText.toInt(&idOk);
    if (!idOk || id < 0) {
        emit operationCompleted(false, tr("变量ID必须是非负整数：%1").arg(idText));
        return false;
    }

    // 验证标签
    if (label.trimmed().isEmpty()) {
        emit operationCompleted(false, tr("变量标签不能为空"));
        return false;
    }

    // 检查ID冲突
    if (m_model && m_model->containsVariable(id)) {
        emit operationCompleted(false, tr("变量ID已存在：%1").arg(id));
        return false;
    }

    // 转换类型和值
    cmd::Var var = convertToVar(typeText, value);
    if (!var.isValid()) {
        emit operationCompleted(false, tr("变量值无效"));
        return false;
    }

    // 调用原有的添加方法
    addVariable(id, var, label.trimmed(), true);
    return true;
}

bool VarTblController::handleUpdateVariable(int originalId, const QString &idText, const QString &label, const QString &typeText, const QVariant &value)
{
    // 验证和转换新ID
    bool idOk;
    int newId = idText.toInt(&idOk);
    if (!idOk || newId < 0) {
        emit operationCompleted(false, tr("变量ID必须是非负整数：%1").arg(idText));
        return false;
    }

    // 验证标签
    if (label.trimmed().isEmpty()) {
        emit operationCompleted(false, tr("变量标签不能为空"));
        return false;
    }

    // 检查ID冲突（如果ID发生了变化）
    if (newId != originalId && m_model && m_model->containsVariable(newId)) {
        emit operationCompleted(false, tr("变量ID已存在：%1").arg(newId));
        return false;
    }

    // 转换类型和值
    cmd::Var var = convertToVar(typeText, value);
    if (!var.isValid()) {
        emit operationCompleted(false, tr("变量值无效"));
        return false;
    }

    // 如果ID发生了变化，需要先删除原变量再添加新变量
    if (newId != originalId) {
        removeVariable(originalId, true);
        addVariable(newId, var, label.trimmed(), true);
    } else {
        // ID没有变化，直接更新
        updateVariable(newId, var, label.trimmed(), true);
    }

    return true;
}

// ========== 数据同步接口 ==========

void VarTblController::refreshFromServer()
{
    if (!m_transceiver || !m_connected) {
        emit operationCompleted(false, tr("未连接到服务器"));
        return;
    }

    updateSyncStatus(SyncStatus::Syncing);

    // 实现从服务器获取数据的逻辑
    refreshVariablesFromServer();
}

void VarTblController::syncToServer()
{
    if (!m_transceiver || !m_connected) {
        emit operationCompleted(false, tr("未连接到服务器"));
        return;
    }

    if (!m_model) {
        emit operationCompleted(false, tr("数据模型未设置"));
        return;
    }

    updateSyncStatus(SyncStatus::Syncing);

    // 实现同步本地数据到服务器的逻辑
    syncAllVariablesToServer();
}

void VarTblController::setAutoSyncInterval(int intervalMs)
{
    m_autoSyncInterval = intervalMs;
    
    if (intervalMs > 0) {
        m_autoSyncTimer->start(intervalMs);
        qDebug() << "VarTblController: 启用自动同步，间隔" << intervalMs << "毫秒";
    } else {
        m_autoSyncTimer->stop();
        qDebug() << "VarTblController: 禁用自动同步";
    }
}

VarTblController::SyncStatus VarTblController::getSyncStatus() const
{
    QMutexLocker locker(&m_statusMutex);
    return m_syncStatus;
}

// ========== 连接管理 ==========

void VarTblController::connectToServer(const QString &host, quint16 port)
{
    if (!m_transceiver) {
        emit operationCompleted(false, tr("通信组件未设置"));
        return;
    }

    bool success = m_transceiver->connectToServer(host, port);
    if (!success) {
        emit operationCompleted(false, tr("连接失败"));
    }

    qDebug() << "VarTblController: 连接服务器" << host << ":" << port;
}

void VarTblController::disconnectFromServer()
{
    if (!m_transceiver) {
        return;
    }
    
    m_transceiver->disconnectFromServer();
    qDebug() << "VarTblController: 断开服务器连接";
}

bool VarTblController::isConnected() const
{
    QMutexLocker locker(&m_statusMutex);
    return m_connected;
}

// ========== 数据验证 ==========

std::pair<bool, QString> VarTblController::validateVariable(const cmd::Var &var, const QString &label) const
{
    if (label.isEmpty()) {
        return {false, tr("变量标签不能为空")};
    }
    
    if (!var.isValid()) {
        return {false, tr("变量数据无效")};
    }
    
    if (var.isString()) {
        // 字符串类型的验证
        QString str = var.toString();
        if (str.length() > 10000) {
            return {false, tr("字符串长度不能超过10000字符")};
        }
    } else if (var.isList()) {
        // 列表类型的验证
        QStringList list = var.toList();
        if (list.size() > 1000) {
            return {false, tr("列表项数不能超过1000")};
        }
        
        for (const QString &item : list) {
            if (item.length() > 1000) {
                return {false, tr("列表项长度不能超过1000字符")};
            }
        }
    }
    
    return {true, QString()};
}

// ========== 槽函数实现 ==========

void VarTblController::onConnectionStateChanged(CommandTransceiverRefactored::ConnectionState state)
{
    bool connected = (state == CommandTransceiverRefactored::ConnectionState::Connected);
    
    {
        QMutexLocker locker(&m_statusMutex);
        m_connected = connected;
    }
    
    emit connectionStatusChanged(connected);
    
    qDebug() << "VarTblController: 连接状态变化" << connected;
}

void VarTblController::onCommandResponseReceived(const CommandTransceiverRefactored::CommandResponse &response)
{
    QMutexLocker locker(&m_commandMutex);
    
    if (!m_pendingCommands.contains(response.commandUuid)) {
        return; // 不是我们发送的命令
    }
    
    QString operation = m_pendingCommands.value(response.commandUuid);
    //int variableId = m_commandVariableIds.value(response.commandUuid, -1);
    
    // 清理命令记录
    m_pendingCommands.remove(response.commandUuid);
    m_commandVariableIds.remove(response.commandUuid);
    
    locker.unlock();
    
    // 处理响应
    if (response.result == CommandTransceiverRefactored::CommandResult::Success) {
        handleCommandSuccess(response.commandUuid, response.data);

        // 对于编辑操作（add/update），需要完成编辑状态的清理
        if (operation == "add" || operation == "update") {
            emit busyStatusChanged(false);
            emit editModeChanged(EditMode::None);

            QString operationText = (operation == "add") ? tr("添加") : tr("更新");
            emit operationCompleted(true, tr("%1变量成功").arg(operationText));
        } else {
            emit operationCompleted(true, tr("操作成功"));
        }
    } else {
        handleCommandError(response.commandUuid, response.errorMessage);

        QString errorMessage;
        switch (response.result) {
        case CommandTransceiverRefactored::CommandResult::Timeout:
            errorMessage = tr("操作超时");
            break;
        case CommandTransceiverRefactored::CommandResult::NetworkError:
            errorMessage = tr("网络错误");
            break;
        case CommandTransceiverRefactored::CommandResult::ServerError:
            errorMessage = tr("服务器错误");
            break;
        default:
            errorMessage = tr("未知错误");
            break;
        }

        if (!response.errorMessage.isEmpty()) {
            errorMessage += ": " + response.errorMessage;
        }

        // 对于编辑操作失败，也需要清理状态
        if (operation == "add" || operation == "update") {
            emit busyStatusChanged(false);
            emit editModeChanged(EditMode::None);
        }

        emit operationCompleted(false, errorMessage);
    }
    
    qDebug() << "VarTblController: 收到命令响应" << response.commandUuid.toString() 
             << "操作=" << operation << "结果=" << static_cast<int>(response.result);
}

void VarTblController::onAutoSyncTimeout()
{
    if (m_connected && m_syncStatus == SyncStatus::Idle) {
        syncToServer();
    }
}

// ========== 内部方法 ==========

QUuid VarTblController::sendVarUpdateCommand(int id, const cmd::Var &var, const QString &label, const QString &operation)
{
    if (!m_transceiver || !m_connected) {
        return QUuid();
    }
    
    // 创建VarUpdate命令
    cmd::VarIndex varIndex = createVarIndex(id);
    cmd::VarUpdate varUpdate(varIndex, var);
    varUpdate.setRLabel(label);
    
    // 转换为JSON并发送
    QJsonObject command = varUpdate.toJsonObj();
    QUuid commandUuid = m_transceiver->sendCommand(command);
    
    if (!commandUuid.isNull() && m_waitForServerResponse)
    {
        QMutexLocker locker(&m_commandMutex);
        m_pendingCommands[commandUuid] = operation;
        m_commandVariableIds[commandUuid] = id;
    }

    return commandUuid;
}

QUuid VarTblController::sendVarUpdateForAdd(int id, const cmd::Var &var, const QString &label)
{
    if (!m_transceiver || !m_connected) {
        return QUuid();
    }

    // 创建VarUpdate命令（用于新增变量，替代已废弃的VarAdd）
    cmd::VarIndex varIndex = createVarIndex(id);
    cmd::VarUpdate varUpdate(varIndex, var);
    varUpdate.setRLabel(label);

    // 转换为JSON并发送
    QJsonObject command = varUpdate.toJsonObj();
    QUuid commandUuid = m_transceiver->sendCommand(command);

    if (!commandUuid.isNull() && m_waitForServerResponse)
    {
        QMutexLocker locker(&m_commandMutex);
        m_pendingCommands[commandUuid] = "add";
        m_commandVariableIds[commandUuid] = id;
    }

    return commandUuid;
}

QUuid VarTblController::sendVarRemoveCommand(int id)
{
    if (!m_transceiver || !m_connected) {
        return QUuid();
    }
    
    // 创建VarRemove命令
    cmd::VarIndex varIndex = createVarIndex(id);
    cmd::VarRemove varRemove(varIndex);
    
    // 转换为JSON并发送
    QJsonObject command = varRemove.toJsonObj();
    QUuid commandUuid = m_transceiver->sendCommand(command);
    
    if (!commandUuid.isNull() && m_waitForServerResponse) {
        QMutexLocker locker(&m_commandMutex);
        m_pendingCommands[commandUuid] = "remove";
        m_commandVariableIds[commandUuid] = id;
    }
    
    return commandUuid;
}

void VarTblController::handleCommandSuccess(const QUuid &uuid, const QJsonObject &response)
{
    Q_UNUSED(uuid)
    Q_UNUSED(response)
    // 处理成功响应的逻辑
    qDebug() << "VarTblController: 命令执行成功" << uuid.toString();
}

void VarTblController::handleCommandError(const QUuid &uuid, const QString &errorMessage)
{
    Q_UNUSED(uuid)
    // 处理错误响应的逻辑
    qDebug() << "VarTblController: 命令执行失败" << uuid.toString() << errorMessage;
}

void VarTblController::handleCommandTimeout(const QUuid &uuid)
{
    QMutexLocker locker(&m_commandMutex);

    // 检查命令是否还在等待中
    if (!m_pendingCommands.contains(uuid)) {
        return; // 命令已经处理完成
    }

    QString operation = m_pendingCommands.value(uuid);
    //int variableId = m_commandVariableIds.value(uuid, -1);

    // 清理命令记录
    m_pendingCommands.remove(uuid);
    m_commandVariableIds.remove(uuid);

    locker.unlock();

    qDebug() << "VarTblController: 命令超时" << uuid.toString() << "操作=" << operation;

    // 对于编辑操作，需要清理状态
    if (operation == "add" || operation == "update") {
        emit busyStatusChanged(false);
        emit editModeChanged(EditMode::None);

        QString operationText = (operation == "add") ? tr("添加") : tr("更新");
        emit operationCompleted(false, tr("%1变量超时：服务器无响应").arg(operationText));
    } else {
        emit operationCompleted(false, tr("操作超时：服务器无响应"));
    }
}

cmd::VarIndex VarTblController::createVarIndex(int id) const
{
    return cmd::VarIndex(id, m_projectUuid);
}

void VarTblController::updateSyncStatus(SyncStatus status)
{
    {
        QMutexLocker locker(&m_statusMutex);
        if (m_syncStatus == status) {
            return;
        }
        m_syncStatus = status;
    }

    // 发出简化的同步状态信号（用于UI反馈）
    bool syncing = (status == SyncStatus::Syncing);
    emit syncStatusChanged(syncing);
    emit busyStatusChanged(syncing);

    // 发出详细的同步状态信号（用于Manager层）
    emit syncStatusChanged(status);
}

QString VarTblController::generateOperationId() const
{
    return QUuid::createUuid().toString();
}

cmd::Var VarTblController::convertToVar(const QString &typeText, const QVariant &value) const
{
    if (typeText == tr("字符串") || typeText == "String") {
        QString strValue = value.toString();

        // 验证字符串长度
        if (strValue.length() > 10000) {
            qDebug() << "VarTblController: 字符串长度超过限制：" << strValue.length();
            return cmd::Var();  // 返回null表示失败
        }

        return cmd::Var(strValue);
    }
    else if (typeText == tr("列表") || typeText == "List") {
        QStringList list;

        if (value.type() == QVariant::StringList) {
            list = value.toStringList();
        } else {
            // 如果是字符串，按行分割
            QString text = value.toString();
            list = text.split('\n', Qt::SkipEmptyParts);

            // 清理每一行
            for (QString &line : list) {
                line = line.trimmed();
            }
        }

        // 验证列表项数量和长度
        if (list.size() > 1000) {
            qDebug() << "VarTblController: 列表项数量超过限制：" << list.size();
            return cmd::Var();
        }

        for (const QString &item : list) {
            if (item.length() > 1000) {
                qDebug() << "VarTblController: 列表项长度超过限制：" << item.length();
                return cmd::Var();
            }
        }

        return cmd::Var(list);
    }

    qDebug() << "VarTblController: 不支持的变量类型：" << typeText;
    return cmd::Var();  // 不支持的类型
}

void VarTblController::emitOperationCompleted(const QString &operation, bool success, const QString &message, int id)
{
    // 发射简化版信号（用于UI反馈）
    emit operationCompleted(success, message);

    // 发射详细版信号（用于Manager层）
    OperationResult result = success ? OperationResult::Success : OperationResult::Failed;
    emit operationCompleted(operation, result, message, id);
}

// ========== View操作请求处理槽 ==========

void VarTblController::onAddVariableRequested()
{
    if (!m_model) {
        emit operationCompleted(false, tr("数据模型未设置"));
        return;
    }

    // 检查busy状态 - 防止在处理其他操作时进入添加模式导致状态冲突
    {
        QMutexLocker locker(&m_statusMutex);
        if (m_syncStatus == SyncStatus::Syncing) {
            emit operationCompleted(false, tr("正在同步数据，请稍后再试"));
            qDebug() << "VarTblController: 添加请求被拒绝 - 正在同步";
            return;
        }
    }

    // 检查是否有待处理的命令
    {
        QMutexLocker locker(&m_commandMutex);
        if (!m_pendingCommands.isEmpty()) {
            emit operationCompleted(false, tr("正在处理其他操作，请稍后再试"));
            qDebug() << "VarTblController: 添加请求被拒绝 - 有待处理命令" << m_pendingCommands.size();
            return;
        }
    }

    // 生成新的变量ID
    int newId = 1;
    QList<int> existingIds = m_model->getAllVariableIds();
    if (!existingIds.isEmpty()) {
        newId = *std::max_element(existingIds.begin(), existingIds.end()) + 1;
    }

    // 在表格编辑模式下，直接添加一个空变量到模型
    cmd::Var emptyVar("");
    QString defaultLabel = tr("新变量%1").arg(newId);

    // 添加到模型
    bool success = m_model->insertOrUpdateVariable(newId, emptyVar, defaultLabel);
    if (success) {
        emit operationCompleted(true, tr("已添加新变量，请在表格中编辑"));
        qDebug() << "VarTblController: 在表格编辑模式下添加新变量，ID=" << newId;
    } else {
        emit operationCompleted(false, tr("添加变量失败"));
        qDebug() << "VarTblController: 添加变量失败，ID=" << newId;
    }
}

void VarTblController::onEditVariableRequested(int id)
{
    if (!m_model) {
        emit operationCompleted(false, tr("数据模型未设置"));
        return;
    }

    if (!m_model->containsVariable(id)) {
        emit operationCompleted(false, tr("变量不存在"));
        return;
    }

    // 检查busy状态 - 防止在处理其他操作时进入编辑模式导致状态冲突
    {
        QMutexLocker locker(&m_statusMutex);
        if (m_syncStatus == SyncStatus::Syncing) {
            emit operationCompleted(false, tr("正在同步数据，请稍后再试"));
            qDebug() << "VarTblController: 编辑请求被拒绝 - 正在同步";
            return;
        }
    }

    // 检查是否有待处理的命令
    {
        QMutexLocker locker(&m_commandMutex);
        if (!m_pendingCommands.isEmpty()) {
            emit operationCompleted(false, tr("正在处理其他操作，请稍后再试"));
            qDebug() << "VarTblController: 编辑请求被拒绝 - 有待处理命令" << m_pendingCommands.size();
            return;
        }
    }

    // 在表格编辑模式下，编辑请求只是一个提示，实际编辑在表格中进行
    emit operationCompleted(true, tr("请在表格中编辑变量"));

    qDebug() << "VarTblController: 表格编辑模式下的编辑请求，ID=" << id;
}

void VarTblController::onDeleteVariableRequested(const QList<int> &ids)
{
    if (!m_model) {
        emit operationCompleted(false, tr("数据模型未设置"));
        return;
    }

    if (ids.isEmpty()) {
        emit operationCompleted(false, tr("未选择要删除的变量"));
        return;
    }

    emit busyStatusChanged(true);

    int successCount = 0;
    int failCount = 0;

    for (int id : ids) {
        if (m_model->containsVariable(id)) {
            // 删除本地数据 - Model会自动通知View更新
            bool success = m_model->removeVariable(id);
            if (success) {
                successCount++;

                // 同步到服务器
                if (m_transceiver && m_connected) {
                    sendVarRemoveCommand(id);
                }
            } else {
                failCount++;
            }
        } else {
            failCount++;
        }
    }

    emit busyStatusChanged(false);

    if (failCount == 0) {
        emit operationCompleted(true, tr("成功删除%1个变量").arg(successCount));
    } else {
        emit operationCompleted(false, tr("删除完成：成功%1个，失败%2个").arg(successCount).arg(failCount));
    }

    qDebug() << "VarTblController: 批量删除变量完成，成功" << successCount << "个，失败" << failCount << "个";
}

void VarTblController::onDataSubmitted(int id, const QString &label, const QString &typeText, const QVariant &value)
{
    if (!m_model) {
        emit operationCompleted(false, tr("数据模型未设置"));
        return;
    }

    // 验证标签
    if (label.trimmed().isEmpty()) {
        emit operationCompleted(false, tr("变量标签不能为空"));
        return;
    }

    // 转换数据类型
    cmd::Var var = convertToVar(typeText, value);
    if (var.type() == cmd::Var::Type::String && var.toString().isEmpty() &&
        typeText != tr("字符串")) {
        emit operationCompleted(false, tr("不支持的变量类型或值格式错误"));
        return;
    }

    // 验证变量数据
    auto [valid, errorMsg] = validateVariable(var, label);
    if (!valid) {
        emit operationCompleted(false, errorMsg);
        return;
    }

    emit busyStatusChanged(true);

    // 更新本地模型 - Model会自动通知View更新
    bool isNew = m_model->insertOrUpdateVariable(id, var, label);

    // 同步到服务器
    bool commandSent = false;
    QUuid commandUuid;
    if (m_transceiver && m_connected) {
        if (isNew) {
            commandUuid = sendVarUpdateForAdd(id, var, label);
        } else {
            commandUuid = sendVarUpdateCommand(id, var, label, "update");
        }
        commandSent = !commandUuid.isNull();

        // 根据配置决定是否等待服务器响应
        if (commandSent && m_waitForServerResponse) {
            // 同步模式：启动超时定时器，等待服务器响应
            QTimer::singleShot(m_serverResponseTimeout, this, [this, commandUuid]() {
                handleCommandTimeout(commandUuid);
            });
            qDebug() << "VarTblController: 同步模式 - 等待服务器响应，超时时间" << m_serverResponseTimeout << "ms";
        } else if (commandSent) {
            // 异步模式：立即完成操作，不等待服务器响应
            emit busyStatusChanged(false);
            emit editModeChanged(EditMode::None);

            QString operationText = isNew ? tr("添加") : tr("更新");
            emit operationCompleted(true, tr("%1变量成功（已发送到服务器）").arg(operationText));
            qDebug() << "VarTblController: 异步模式 - 命令已发送，立即完成操作";
        }
    }

    // 如果没有发送命令到服务器，立即完成操作
    if (!commandSent) {
        emit busyStatusChanged(false);
        // 退出编辑模式
        emit editModeChanged(EditMode::None);

        QString operationText = isNew ? tr("添加") : tr("更新");
        emit operationCompleted(true, tr("%1变量成功（仅本地）").arg(operationText));
        qDebug() << "VarTblController: 仅本地模式 - 未连接服务器";
    }
    // 注意：如果是同步模式且发送了命令，等待服务器响应后再完成操作（在onCommandResponseReceived中处理）

    QString operationText = isNew ? tr("添加") : tr("更新");
    qDebug() << "VarTblController: 数据提交完成，ID=" << id << "操作=" << operationText;
}

void VarTblController::onEditCancelled()
{
    // 退出编辑模式
    emit editModeChanged(EditMode::None);

    qDebug() << "VarTblController: 编辑已取消";
}

void VarTblController::onRefreshDataRequested()
{
    refreshFromServer();
}

void VarTblController::onSyncDataRequested()
{
    syncToServer();
}

void VarTblController::onConnectToServerRequested()
{
    // 这里可以添加连接对话框逻辑，暂时使用默认参数
    connectToServer("127.0.0.1", 8080);
}

void VarTblController::onDisconnectFromServerRequested()
{
    disconnectFromServer();
}

void VarTblController::onModelDataChanged(const QString &operation, int id)
{
    // 处理Model数据变化的业务逻辑
    // 例如：自动同步、统计更新等

    qDebug() << "VarTblController: Model数据变化" << operation << "ID=" << id;

    // 发射数据变化信号给Manager层
    emit variableDataChanged(operation, id);

    // 如果启用了自动同步，可以在这里触发同步
    // if (m_autoSyncInterval > 0 && m_connected) {
    //     // 延迟同步，避免频繁操作
    // }
}

// ========== 服务器通信方法实现 ==========

void VarTblController::syncVariablesToServer(const cmd::VarTbl &varTbl)
{
    if (!m_transceiver || !m_connected) {
        qDebug() << "VarTblController: 无法同步到服务器 - 未连接";
        updateSyncStatus(SyncStatus::Error);
        emit operationCompleted(false, tr("同步失败：未连接到服务器"));
        return;
    }

    qDebug() << "VarTblController: 开始批量同步" << varTbl.size() << "个变量到服务器";
    updateSyncStatus(SyncStatus::Syncing);

    int successCount = 0;
    int totalCount = varTbl.size();

    // 遍历所有变量并发送到服务器
    for (auto it = varTbl.cbegin(); it != varTbl.cend(); ++it) {
        int id = it->first;
        const auto &item = it->second;

        try {
            // 发送VarUpdate命令
            QUuid cmdUuid = sendVarUpdateForAdd(id, item.obj, item.label);
            if (!cmdUuid.isNull()) {
                successCount++;
                qDebug() << "VarTblController: 成功发送变量" << id << "到服务器";
            } else {
                qDebug() << "VarTblController: 发送变量" << id << "失败";
            }
        } catch (const std::exception &e) {
            qDebug() << "VarTblController: 同步变量" << id << "时发生异常:" << e.what();
        }
    }

    updateSyncStatus(SyncStatus::Idle);

    QString message = tr("批量同步完成：成功 %1/%2").arg(successCount).arg(totalCount);
    bool success = (successCount == totalCount);
    emit operationCompleted(success, message);

    qDebug() << "VarTblController: 批量同步完成，成功率:" << successCount << "/" << totalCount;
}

void VarTblController::refreshVariablesFromServer()
{
    if (!m_transceiver || !m_connected) {
        qDebug() << "VarTblController: 无法从服务器刷新 - 未连接";
        updateSyncStatus(SyncStatus::Error);
        emit operationCompleted(false, tr("刷新失败：未连接到服务器"));
        return;
    }

    qDebug() << "VarTblController: 开始从服务器刷新变量数据";
    updateSyncStatus(SyncStatus::Syncing);

    try {
        // 创建获取VarTbl的命令
        // 注意：这里需要根据实际的ada3命令协议来实现
        // 假设有一个VarTblGet命令可以获取所有变量

        // 方法1：发送获取所有变量的命令
        QJsonObject getCommand;
        getCommand["type"] = "VarTblGet";
        getCommand["timestamp"] = QDateTime::currentMSecsSinceEpoch();

        // 使用CommandTransceiver发送命令
        QUuid cmdUuid = m_transceiver->sendCommand(getCommand);

        if (!cmdUuid.isNull()) {
            qDebug() << "VarTblController: 已发送获取变量数据的命令，UUID:" << cmdUuid.toString();
            // 注意：实际的数据接收需要在CommandTransceiver的响应处理中实现
            // 这里只是发送了请求，真正的数据更新会在收到响应时进行

            updateSyncStatus(SyncStatus::Idle);
            emit operationCompleted(true, tr("刷新请求已发送"));
        } else {
            qDebug() << "VarTblController: 发送获取变量数据命令失败";
            updateSyncStatus(SyncStatus::Error);
            emit operationCompleted(false, tr("刷新失败：命令发送失败"));
        }

    } catch (const std::exception &e) {
        qDebug() << "VarTblController: 刷新数据时发生异常:" << e.what();
        updateSyncStatus(SyncStatus::Error);
        emit operationCompleted(false, tr("刷新失败：%1").arg(e.what()));
    }
}

void VarTblController::syncAllVariablesToServer()
{
    if (!m_model) {
        qDebug() << "VarTblController: 无法同步 - 数据模型未设置";
        updateSyncStatus(SyncStatus::Error);
        emit operationCompleted(false, tr("同步失败：数据模型未设置"));
        return;
    }

    if (!m_transceiver || !m_connected) {
        qDebug() << "VarTblController: 无法同步 - 未连接到服务器";
        updateSyncStatus(SyncStatus::Error);
        emit operationCompleted(false, tr("同步失败：未连接到服务器"));
        return;
    }

    // 获取当前所有本地变量
    cmd::VarTbl localVarTbl = m_model->getVarTbl();

    if (localVarTbl.size() == 0) {
        qDebug() << "VarTblController: 没有本地变量需要同步";
        updateSyncStatus(SyncStatus::Idle);
        emit operationCompleted(true, tr("同步完成：没有变量需要同步"));
        return;
    }

    qDebug() << "VarTblController: 开始同步所有本地变量到服务器，共" << localVarTbl.size() << "个变量";

    // 调用批量同步方法
    syncVariablesToServer(localVarTbl);
}

// ========== 配置管理方法实现 ==========

void VarTblController::setWaitForServerResponse(bool wait)
{
    if (m_waitForServerResponse != wait) {
        m_waitForServerResponse = wait;
        qDebug() << "VarTblController: 设置等待服务器响应模式为" << (wait ? "同步" : "异步");
    }
}

bool VarTblController::getWaitForServerResponse() const
{
    return m_waitForServerResponse;
}

void VarTblController::setServerResponseTimeout(int timeoutMs)
{
    if (timeoutMs > 0 && m_serverResponseTimeout != timeoutMs) {
        m_serverResponseTimeout = timeoutMs;
        qDebug() << "VarTblController: 设置服务器响应超时时间为" << timeoutMs << "毫秒";
    }
}

int VarTblController::getServerResponseTimeout() const
{
    return m_serverResponseTimeout;
}
