/****************************************************************************
** Meta object code from reading C++ file 'ServerMainWindow.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../ServerMainWindow.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ServerMainWindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ServerMainWindow_t {
    QByteArrayData data[26];
    char stringdata0[440];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ServerMainWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ServerMainWindow_t qt_meta_stringdata_ServerMainWindow = {
    {
QT_MOC_LITERAL(0, 0, 16), // "ServerMainWindow"
QT_MOC_LITERAL(1, 17, 20), // "onStartServerClicked"
QT_MOC_LITERAL(2, 38, 0), // ""
QT_MOC_LITERAL(3, 39, 19), // "onStopServerClicked"
QT_MOC_LITERAL(4, 59, 18), // "onClearLogsClicked"
QT_MOC_LITERAL(5, 78, 17), // "onSaveLogsClicked"
QT_MOC_LITERAL(6, 96, 20), // "onSendMessageClicked"
QT_MOC_LITERAL(7, 117, 25), // "onBroadcastMessageClicked"
QT_MOC_LITERAL(8, 143, 22), // "onClearSendAreaClicked"
QT_MOC_LITERAL(9, 166, 28), // "onConnectionSelectionChanged"
QT_MOC_LITERAL(10, 195, 25), // "onDisconnectClientClicked"
QT_MOC_LITERAL(11, 221, 27), // "onRefreshConnectionsClicked"
QT_MOC_LITERAL(12, 249, 15), // "onServerStarted"
QT_MOC_LITERAL(13, 265, 4), // "port"
QT_MOC_LITERAL(14, 270, 15), // "onServerStopped"
QT_MOC_LITERAL(15, 286, 17), // "onClientConnected"
QT_MOC_LITERAL(16, 304, 2), // "id"
QT_MOC_LITERAL(17, 307, 7), // "address"
QT_MOC_LITERAL(18, 315, 20), // "onClientDisconnected"
QT_MOC_LITERAL(19, 336, 21), // "onJsonCommandReceived"
QT_MOC_LITERAL(20, 358, 12), // "connectionId"
QT_MOC_LITERAL(21, 371, 7), // "command"
QT_MOC_LITERAL(22, 379, 17), // "onRawDataReceived"
QT_MOC_LITERAL(23, 397, 4), // "data"
QT_MOC_LITERAL(24, 402, 16), // "updateStatistics"
QT_MOC_LITERAL(25, 419, 20) // "updateConnectionList"

    },
    "ServerMainWindow\0onStartServerClicked\0"
    "\0onStopServerClicked\0onClearLogsClicked\0"
    "onSaveLogsClicked\0onSendMessageClicked\0"
    "onBroadcastMessageClicked\0"
    "onClearSendAreaClicked\0"
    "onConnectionSelectionChanged\0"
    "onDisconnectClientClicked\0"
    "onRefreshConnectionsClicked\0onServerStarted\0"
    "port\0onServerStopped\0onClientConnected\0"
    "id\0address\0onClientDisconnected\0"
    "onJsonCommandReceived\0connectionId\0"
    "command\0onRawDataReceived\0data\0"
    "updateStatistics\0updateConnectionList"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ServerMainWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      18,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  104,    2, 0x08 /* Private */,
       3,    0,  105,    2, 0x08 /* Private */,
       4,    0,  106,    2, 0x08 /* Private */,
       5,    0,  107,    2, 0x08 /* Private */,
       6,    0,  108,    2, 0x08 /* Private */,
       7,    0,  109,    2, 0x08 /* Private */,
       8,    0,  110,    2, 0x08 /* Private */,
       9,    0,  111,    2, 0x08 /* Private */,
      10,    0,  112,    2, 0x08 /* Private */,
      11,    0,  113,    2, 0x08 /* Private */,
      12,    1,  114,    2, 0x08 /* Private */,
      14,    0,  117,    2, 0x08 /* Private */,
      15,    2,  118,    2, 0x08 /* Private */,
      18,    1,  123,    2, 0x08 /* Private */,
      19,    2,  126,    2, 0x08 /* Private */,
      22,    2,  131,    2, 0x08 /* Private */,
      24,    0,  136,    2, 0x08 /* Private */,
      25,    0,  137,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::UShort,   13,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QUuid, QMetaType::QString,   16,   17,
    QMetaType::Void, QMetaType::QUuid,   16,
    QMetaType::Void, QMetaType::QUuid, QMetaType::QJsonObject,   20,   21,
    QMetaType::Void, QMetaType::QUuid, QMetaType::QByteArray,   20,   23,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void ServerMainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ServerMainWindow *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onStartServerClicked(); break;
        case 1: _t->onStopServerClicked(); break;
        case 2: _t->onClearLogsClicked(); break;
        case 3: _t->onSaveLogsClicked(); break;
        case 4: _t->onSendMessageClicked(); break;
        case 5: _t->onBroadcastMessageClicked(); break;
        case 6: _t->onClearSendAreaClicked(); break;
        case 7: _t->onConnectionSelectionChanged(); break;
        case 8: _t->onDisconnectClientClicked(); break;
        case 9: _t->onRefreshConnectionsClicked(); break;
        case 10: _t->onServerStarted((*reinterpret_cast< quint16(*)>(_a[1]))); break;
        case 11: _t->onServerStopped(); break;
        case 12: _t->onClientConnected((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 13: _t->onClientDisconnected((*reinterpret_cast< const QUuid(*)>(_a[1]))); break;
        case 14: _t->onJsonCommandReceived((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< const QJsonObject(*)>(_a[2]))); break;
        case 15: _t->onRawDataReceived((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< const QByteArray(*)>(_a[2]))); break;
        case 16: _t->updateStatistics(); break;
        case 17: _t->updateConnectionList(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ServerMainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_ServerMainWindow.data,
    qt_meta_data_ServerMainWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ServerMainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ServerMainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ServerMainWindow.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int ServerMainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 18)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 18;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 18)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 18;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
