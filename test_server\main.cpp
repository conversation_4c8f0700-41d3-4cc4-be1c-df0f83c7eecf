#include <QApplication>
#include <QCommandLineParser>
#include <QTimer>
#include <QDebug>
#include <QTextStream>
#include <QStyleFactory>
#include <iostream>
#include "TestServer.h"
#include "ServerMainWindow.h"
#include "QtCompatibility.h"

/**
 * @brief 打印欢迎信息
 */
void printWelcomeMessage()
{
    qInfo() << "========================================";
    qInfo() << "  CommandTransceiver 测试服务器";
    qInfo() << "========================================";
    qInfo() << "版本: 1.0.0";
    qInfo() << "用途: 为 CommandTransceiver 客户端提供测试服务";
    qInfo() << "支持协议: PreLength, EndWithNewLine, Simple";
    qInfo() << "========================================";
}

/**
 * @brief 打印使用说明
 */
void printUsageExamples()
{
    qInfo() << "";
    qInfo() << "使用示例:";
    qInfo() << "  基本启动:";
    qInfo() << "    CommandTransceiverTestServer";
    qInfo() << "";
    qInfo() << "  指定端口和协议:";
    qInfo() << "    CommandTransceiverTestServer -p 9090 -t endwithnewline";
    qInfo() << "";
    qInfo() << "  启用心跳检测:";
    qInfo() << "    CommandTransceiverTestServer --heartbeat --heartbeat-interval 15000";
    qInfo() << "";
    qInfo() << "  限制连接数:";
    qInfo() << "    CommandTransceiverTestServer -m 50";
    qInfo() << "";
    qInfo() << "  静默模式:";
    qInfo() << "    CommandTransceiverTestServer --no-logging --no-echo";
    qInfo() << "";
}

/**
 * @brief 设置控制台输入处理
 */
class ConsoleInputHandler : public QObject
{
    Q_OBJECT

public:
    explicit ConsoleInputHandler(TestServer *server, QObject *parent = nullptr)
        : QObject(parent), m_server(server)
    {
        // 设置定时器检查控制台输入
        m_inputTimer = new QTimer(this);
        connect(m_inputTimer, &QTimer::timeout, this, &ConsoleInputHandler::checkInput);
        m_inputTimer->start(100); // 每100ms检查一次
        
        printHelp();
    }

private slots:
    void checkInput()
    {
        // 检查是否有控制台输入
        if (std::cin.rdbuf()->in_avail() > 0) {
            std::string input;
            std::getline(std::cin, input);
            processCommand(QString::fromStdString(input).trimmed());
        }
    }

private:
    void processCommand(const QString &command)
    {
        if (command.isEmpty()) {
            return;
        }
        
        QStringList parts = command.split(' ', QT_SKIP_EMPTY_PARTS);
        if (parts.isEmpty()) {
            return;
        }
        
        QString cmd = parts[0].toLower();
        
        if (cmd == "help" || cmd == "h") {
            printHelp();
        } else if (cmd == "status" || cmd == "s") {
            m_server->printServerStatus();
        } else if (cmd == "connections" || cmd == "c") {
            m_server->printConnectionList();
        } else if (cmd == "stats") {
            m_server->printStatistics();
        } else if (cmd == "broadcast" || cmd == "b") {
            if (parts.size() > 1) {
                QString message = parts.mid(1).join(' ');
                QJsonObject msg;
                msg["type"] = "broadcast_message";
                msg["message"] = message;
                msg["timestamp"] = QDateTime::currentMSecsSinceEpoch();
                int count = m_server->broadcastMessage(msg);
                qInfo() << QString("广播消息已发送给 %1 个客户端").arg(count);
            } else {
                qInfo() << "用法: broadcast <消息内容>";
            }
        } else if (cmd == "protocol" || cmd == "p") {
            if (parts.size() > 1) {
                QString protocolStr = parts[1].toLower();
                RWSProtocolHandler::ProtocolType type = 
                    RWSProtocolHandler::stringToProtocolType(protocolStr);
                m_server->setProtocolType(type);
                qInfo() << "默认协议已设置为:" << RWSProtocolHandler::protocolTypeToString(type);
            } else {
                qInfo() << "用法: protocol <prelength|endwithnewline|simple>";
            }
        } else if (cmd == "quit" || cmd == "q" || cmd == "exit") {
            qInfo() << "正在关闭服务器...";
            QCoreApplication::quit();
        } else {
            qInfo() << "未知命令:" << command << "，输入 'help' 查看帮助";
        }
    }
    
    void printHelp()
    {
        qInfo() << "";
        qInfo() << "========== 控制台命令 ==========";
        qInfo() << "help, h          - 显示此帮助信息";
        qInfo() << "status, s        - 显示服务器状态";
        qInfo() << "connections, c   - 显示连接列表";
        qInfo() << "stats            - 显示统计信息";
        qInfo() << "broadcast <msg>  - 向所有客户端广播消息";
        qInfo() << "protocol <type>  - 设置默认协议类型";
        qInfo() << "quit, q, exit    - 退出服务器";
        qInfo() << "================================";
        qInfo() << "";
    }

private:
    TestServer *m_server;
    QTimer *m_inputTimer;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    app.setApplicationName("CommandTransceiverTestServerGUI");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("CommandTransceiver");

    // 设置应用程序样式
    app.setStyle(QStyleFactory::create("Fusion"));

    // 设置命令行解析器
    QCommandLineParser parser;
    parser.setApplicationDescription("CommandTransceiver 测试服务器 GUI版本");
    parser.addHelpOption();
    parser.addVersionOption();

    // 添加控制台模式选项
    parser.addOption(QCommandLineOption(
        {"c", "console"},
        "以控制台模式运行（无GUI界面）"
    ));

    parser.process(app);

    // 检查是否使用控制台模式
    if (parser.isSet("console")) {
        // 控制台模式 - 使用原有的控制台逻辑
        printWelcomeMessage();

        TestServer::setupCommandLineOptions(parser);
        parser.process(app);

        if (parser.isSet("help")) {
            printUsageExamples();
            return 0;
        }

        TestServer::ServerConfig config = TestServer::parseCommandLine(parser);
        TestServer server;

        if (!server.startServer(config)) {
            qCritical() << "服务器启动失败";
            return 1;
        }

        ConsoleInputHandler inputHandler(&server);

        QObject::connect(&server, &TestServer::serverStarted, [&](quint16 port) {
            qInfo() << QString("服务器已在端口 %1 上启动").arg(port);
            qInfo() << "输入 'help' 查看可用命令";
        });

        QObject::connect(&server, &TestServer::clientConnected, [&](const QUuid &id, const QString &address) {
            qInfo() << QString("新客户端连接: %1 (%2)").arg(address, id.toString());
        });

        QObject::connect(&server, &TestServer::clientDisconnected, [&](const QUuid &id) {
            qInfo() << QString("客户端断开连接: %1").arg(id.toString());
        });

        QObject::connect(&app, &QApplication::aboutToQuit, [&]() {
            qInfo() << "正在关闭服务器...";
            server.stopServer();
        });

        qInfo() << "服务器正在运行，按 Ctrl+C 或输入 'quit' 退出";
        return app.exec();
    } else {
        // GUI模式
        qInfo() << "启动 CommandTransceiver 测试服务器 GUI版本";

        ServerMainWindow window;
        window.show();

        return app.exec();
    }
}

#include "main.moc"
