QT += core network gui widgets

TARGET = CommandTransceiverTestServerGUI
TEMPLATE = app

# C++标准
CONFIG += c++17

# 构建配置
CONFIG += warn_on
CONFIG += thread

# 预处理器定义
DEFINES += QT_DEPRECATED_WARNINGS

# Qt 版本兼容性
lessThan(QT_MAJOR_VERSION, 5): error("需要 Qt 5.0 或更高版本")
equals(QT_MAJOR_VERSION, 5): lessThan(QT_MINOR_VERSION, 12): error("需要 Qt 5.12 或更高版本")

# 包含路径
INCLUDEPATH += $$PWD

HEADERS += \
    TestServer.h \
    ClientConnection.h \
    RWSProtocolHandler.h \
    QtCompatibility.h \
    ServerMainWindow.h

SOURCES += \
    main.cpp \
    TestServer.cpp \
    ClientConnection.cpp \
    RWSProtocolHandler.cpp \
    ServerMainWindow.cpp

# ========== 输出目录配置 ==========

CONFIG(debug, debug|release) {
    DESTDIR = $$PWD/bin/debug
    OBJECTS_DIR = $$PWD/build/debug/obj
    MOC_DIR = $$PWD/build/debug/moc
    RCC_DIR = $$PWD/build/debug/rcc
    UI_DIR = $$PWD/build/debug/ui
    TARGET = $${TARGET}_d
} else {
    DESTDIR = $$PWD/bin/release
    OBJECTS_DIR = $$PWD/build/release/obj
    MOC_DIR = $$PWD/build/release/moc
    RCC_DIR = $$PWD/build/release/rcc
    UI_DIR = $$PWD/build/release/ui
}

# 创建输出目录
!exists($$DESTDIR) {
    system($$QMAKE_MKDIR $$shell_path($$DESTDIR))
}

# ========== 清理配置 ==========

QMAKE_CLEAN += $$TARGET

# 额外的清理文件
win32 {
    QMAKE_CLEAN += *.pdb *.ilk *.exp *.lib
}

unix {
    QMAKE_CLEAN += *.so *.a
}
