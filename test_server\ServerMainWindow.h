#ifndef SERVERMAINWINDOW_H
#define SERVERMAINWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QPushButton>
#include <QLabel>
#include <QLineEdit>
#include <QSpinBox>
#include <QComboBox>
#include <QTextEdit>
#include <QPlainTextEdit>
#include <QListWidget>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QHeaderView>
#include <QSplitter>
#include <QStatusBar>
#include <QMenuBar>
#include <QMenu>
#include <QAction>
#include <QTimer>
#include <QJsonObject>
#include <QJsonDocument>
#include <QUuid>
#include <QDateTime>
#include <QMessageBox>
#include <QFileDialog>
#include <QApplication>
#include <QClipboard>
#include <QMutex>
#include <QMutexLocker>

#include "TestServer.h"
#include "RWSProtocolHandler.h"

/**
 * @brief 服务器主窗口类
 * 
 * 提供图形化界面来管理和监控CommandTransceiver测试服务器
 */
class ServerMainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit ServerMainWindow(QWidget *parent = nullptr);
    ~ServerMainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    // 服务器控制
    void onStartServerClicked();
    void onStopServerClicked();
    void onClearLogsClicked();
    void onSaveLogsClicked();
    
    // 消息发送
    void onSendMessageClicked();
    void onBroadcastMessageClicked();
    void onClearSendAreaClicked();
    
    // 连接管理
    void onConnectionSelectionChanged();
    void onDisconnectClientClicked();
    void onRefreshConnectionsClicked();
    
    // 服务器事件
    void onServerStarted(quint16 port);
    void onServerStopped();
    void onClientConnected(const QUuid &id, const QString &address);
    void onClientDisconnected(const QUuid &id);
    void onJsonCommandReceived(const QUuid &connectionId, const QJsonObject &command);
    void onRawDataReceived(const QUuid &connectionId, const QByteArray &data);
    
    // 定时更新
    void updateStatistics();
    void updateConnectionList();

private:
    // UI初始化
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void setupServerControlGroup();
    void setupConnectionGroup();
    void setupMessageGroup();
    void setupSendGroup();
    void setupStatisticsGroup();
    
    // UI更新
    void updateServerStatus();
    void updateConnectionTable();
    void addLogMessage(const QString &message, const QString &type = "INFO");
    void addReceivedMessage(const QUuid &connectionId, const QString &message, const QString &type);
    
    // 工具方法
    QString formatJsonForDisplay(const QJsonObject &json);
    QString formatTimestamp(const QDateTime &dateTime = QDateTime::currentDateTime());
    QJsonObject parseJsonFromText(const QString &text);
    void saveLogsToFile(const QString &fileName);
    
    // 服务器相关
    TestServer *m_server;
    TestServer::ServerConfig m_serverConfig;
    
    // UI组件 - 服务器控制
    QGroupBox *m_serverControlGroup;
    QPushButton *m_startButton;
    QPushButton *m_stopButton;
    QSpinBox *m_portSpinBox;
    QComboBox *m_protocolComboBox;
    QLabel *m_serverStatusLabel;
    
    // UI组件 - 连接管理
    QGroupBox *m_connectionGroup;
    QTableWidget *m_connectionTable;
    QPushButton *m_disconnectClientButton;
    QPushButton *m_refreshConnectionsButton;
    QLabel *m_connectionCountLabel;
    
    // UI组件 - 消息显示
    QGroupBox *m_messageGroup;
    QTextEdit *m_messageLogEdit;
    QPushButton *m_clearLogsButton;
    QPushButton *m_saveLogsButton;
    
    // UI组件 - 消息发送
    QGroupBox *m_sendGroup;
    QPlainTextEdit *m_sendTextEdit;
    QPushButton *m_sendMessageButton;
    QPushButton *m_broadcastMessageButton;
    QPushButton *m_clearSendAreaButton;
    QComboBox *m_targetConnectionComboBox;
    
    // UI组件 - 统计信息
    QGroupBox *m_statisticsGroup;
    QLabel *m_uptimeLabel;
    QLabel *m_totalConnectionsLabel;
    QLabel *m_messagesReceivedLabel;
    QLabel *m_messagesSentLabel;
    QLabel *m_bytesReceivedLabel;
    QLabel *m_bytesSentLabel;
    
    // 状态栏
    QLabel *m_statusLabel;
    QLabel *m_timeLabel;
    
    // 定时器
    QTimer *m_statisticsTimer;
    QTimer *m_timeTimer;
    
    // 数据保护
    QMutex m_logMutex;
    
    // 常量
    static const int UPDATE_INTERVAL_MS = 1000;
    static const int MAX_LOG_LINES = 10000;
};

#endif // SERVERMAINWINDOW_H
