#include "mvc/VarListEditor.h"
#include <QInputDialog>
#include <QMessageBox>
#include <QDebug>
#include <QKeyEvent>
#include <QEvent>
#include <QTimer>
#include <exception>
#include <stdexcept>

VarListEditor::VarListEditor(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_contentLayout(nullptr)
    , m_listWidget(nullptr)
    , m_buttonWidget(nullptr)
    , m_buttonLayout(nullptr)
    , m_addButton(nullptr)
    , m_deleteButton(nullptr)
    , m_moveUpButton(nullptr)
    , m_moveDownButton(nullptr)
    , m_editButton(nullptr)
    , m_clearButton(nullptr)
    , m_quickAddWidget(nullptr)
    , m_quickAddLayout(nullptr)
    , m_quickAddLabel(nullptr)
    , m_quickAddEdit(nullptr)
    , m_quickAddButton(nullptr)
    , m_readOnly(false)
{
    setupUI();
    connectSignals();
    updateButtonStates();
    
    qDebug() << "VarListEditor: 创建列表编辑器";
}

// ========== 数据接口 ==========

void VarListEditor::setStringList(const QStringList &list)
{
    if (!m_listWidget) {
        qDebug() << "VarListEditor: setStringList - m_listWidget 为空";
        return;
    }

    qDebug() << "VarListEditor: 开始设置列表数据" << list;

    m_listWidget->clear();

    for (const QString &item : list) {
        if (!item.trimmed().isEmpty()) {
            QListWidgetItem *newItem = new QListWidgetItem(item.trimmed());
            // 创建时就设置为可编辑，避免运行时动态修改标志位
            newItem->setFlags(Qt::ItemIsSelectable | Qt::ItemIsEnabled | Qt::ItemIsEditable);
            m_listWidget->addItem(newItem);
            qDebug() << "VarListEditor: 添加项目" << item.trimmed();
        }
    }

    updateButtonStates();
    emitChangeSignals();

    qDebug() << "VarListEditor: 设置列表数据完成，共" << list.size() << "项，实际添加" << m_listWidget->count() << "项";
}

QStringList VarListEditor::getStringList() const
{
    QStringList result;
    
    if (!m_listWidget) {
        return result;
    }
    
    for (int i = 0; i < m_listWidget->count(); ++i) {
        QListWidgetItem *item = m_listWidget->item(i);
        if (item) {
            QString text = item->text().trimmed();
            if (!text.isEmpty()) {
                result.append(text);
            }
        }
    }
    
    return result;
}

void VarListEditor::clear()
{
    if (m_listWidget) {
        m_listWidget->clear();
        updateButtonStates();
        emitChangeSignals();
    }
}

// ========== 状态接口 ==========

void VarListEditor::setReadOnly(bool readOnly)
{
    m_readOnly = readOnly;

    // 通过禁用列表组件来实现只读效果，而不是动态修改ItemFlags
    if (m_listWidget) {
        m_listWidget->setEnabled(!readOnly);
    }

    // 更新所有按钮的启用状态
    if (m_addButton) m_addButton->setEnabled(!readOnly);
    if (m_deleteButton) m_deleteButton->setEnabled(!readOnly);
    if (m_moveUpButton) m_moveUpButton->setEnabled(!readOnly);
    if (m_moveDownButton) m_moveDownButton->setEnabled(!readOnly);
    if (m_editButton) m_editButton->setEnabled(!readOnly);
    if (m_clearButton) m_clearButton->setEnabled(!readOnly);
    if (m_quickAddEdit) m_quickAddEdit->setEnabled(!readOnly);
    if (m_quickAddButton) m_quickAddButton->setEnabled(!readOnly);

    updateButtonStates();

    qDebug() << "VarListEditor: 设置只读模式" << readOnly;
}

bool VarListEditor::isEmpty() const
{
    return m_listWidget ? m_listWidget->count() == 0 : true;
}

int VarListEditor::itemCount() const
{
    return m_listWidget ? m_listWidget->count() : 0;
}

// ========== 事件处理 ==========

void VarListEditor::keyPressEvent(QKeyEvent *event)
{
    // 防止意外的键盘操作导致数据丢失
    if (event->key() == Qt::Key_Delete) {
        // 只有在非只读模式下才允许Delete键删除
        if (!m_readOnly) {
            // 调用删除按钮的处理逻辑，确保一致性
            onDeleteButtonClicked();
        }
        // 无论如何都不传递给父类，防止QListWidget的默认Delete行为
        event->accept();
        return;
    }

    // 其他键盘事件正常处理
    QWidget::keyPressEvent(event);
}

bool VarListEditor::eventFilter(QObject *watched, QEvent *event)
{
    // 过滤QListWidget的键盘事件，防止意外操作
    if (watched == m_listWidget && event->type() == QEvent::KeyPress) {
        QKeyEvent *keyEvent = static_cast<QKeyEvent*>(event);

        if (keyEvent->key() == Qt::Key_Delete) {
            // 拦截Delete键事件，防止QListWidget的默认删除行为
            qDebug() << "VarListEditor: 拦截Delete键事件，只读模式=" << m_readOnly;
            if (!m_readOnly) {
                // 只有在非只读模式下才允许删除
                onDeleteButtonClicked();
            } else {
                qDebug() << "VarListEditor: Delete键被禁用（只读模式）";
            }
            // 返回true表示事件已处理，不再传递
            return true;
        }
    }

    // 其他事件正常处理
    return QWidget::eventFilter(watched, event);
}

// ========== 按钮事件处理 ==========

void VarListEditor::onAddButtonClicked()
{
    if (m_readOnly) {
        return;
    }

    // 添加一个新项目并立即开始编辑
    QString defaultText = tr("新项目");
    QListWidgetItem *newItem = new QListWidgetItem(defaultText);

    // 创建时就设置为可编辑，避免运行时动态修改标志位
    newItem->setFlags(Qt::ItemIsSelectable | Qt::ItemIsEnabled | Qt::ItemIsEditable);

    m_listWidget->addItem(newItem);
    m_listWidget->setCurrentItem(newItem);

    // 立即开始编辑
    m_listWidget->editItem(newItem);

    updateButtonStates();
    // 注意：不在这里调用emitChangeSignals()，等编辑完成后再调用

    qDebug() << "VarListEditor: 添加新项目并开始编辑";
}

void VarListEditor::onDeleteButtonClicked()
{
    if (m_readOnly || !m_listWidget) {
        return;
    }
    
    int currentRow = getCurrentRow();
    if (currentRow >= 0) {
        QListWidgetItem *item = m_listWidget->takeItem(currentRow);
        delete item;
        
        updateButtonStates();
        emitChangeSignals();
        
        qDebug() << "VarListEditor: 删除列表项，行号=" << currentRow;
    }
}

void VarListEditor::onMoveUpButtonClicked()
{
    if (m_readOnly || !m_listWidget) {
        return;
    }
    
    int currentRow = getCurrentRow();
    if (currentRow > 0) {
        QListWidgetItem *item = m_listWidget->takeItem(currentRow);
        m_listWidget->insertItem(currentRow - 1, item);
        m_listWidget->setCurrentRow(currentRow - 1);
        
        updateButtonStates();
        emitChangeSignals();
        
        qDebug() << "VarListEditor: 上移列表项，从" << currentRow << "到" << (currentRow - 1);
    }
}

void VarListEditor::onMoveDownButtonClicked()
{
    if (m_readOnly || !m_listWidget) {
        return;
    }
    
    int currentRow = getCurrentRow();
    if (currentRow >= 0 && currentRow < m_listWidget->count() - 1) {
        QListWidgetItem *item = m_listWidget->takeItem(currentRow);
        m_listWidget->insertItem(currentRow + 1, item);
        m_listWidget->setCurrentRow(currentRow + 1);
        
        updateButtonStates();
        emitChangeSignals();
        
        qDebug() << "VarListEditor: 下移列表项，从" << currentRow << "到" << (currentRow + 1);
    }
}

void VarListEditor::onEditButtonClicked()
{
    if (m_readOnly || !m_listWidget) {
        qDebug() << "VarListEditor: 编辑按钮被禁用（只读模式或无列表组件）";
        return;
    }

    QListWidgetItem *item = m_listWidget->currentItem();
    if (item) {
        qDebug() << "VarListEditor: 编辑按钮被点击，当前项目=" << item->text();
        editListItem(item);
    } else {
        qDebug() << "VarListEditor: 没有选中的项目可编辑";
        QMessageBox::information(this, tr("提示"), tr("请先选择要编辑的项目"));
    }
}

void VarListEditor::onClearButtonClicked()
{
    if (m_readOnly) {
        return;
    }
    
    if (m_listWidget && m_listWidget->count() > 0) {
        int ret = QMessageBox::question(this, tr("确认清空"), 
                                       tr("确定要清空所有列表项吗？"),
                                       QMessageBox::Yes | QMessageBox::No,
                                       QMessageBox::No);
        
        if (ret == QMessageBox::Yes) {
            clear();
            qDebug() << "VarListEditor: 清空所有列表项";
        }
    }
}

void VarListEditor::onQuickAddButtonClicked()
{
    if (m_readOnly || !m_quickAddEdit) {
        return;
    }
    
    QString text = m_quickAddEdit->text().trimmed();
    if (!text.isEmpty()) {
        addListItem(text);
        m_quickAddEdit->clear();
        m_quickAddEdit->setFocus();
    }
}

// ========== 列表事件处理 ==========

void VarListEditor::onListItemDoubleClicked(QListWidgetItem *item)
{
    if (m_readOnly) {
        qDebug() << "VarListEditor: 只读模式，禁用双击编辑";
        return;
    }

    if (isItemValid(item)) {
        qDebug() << "VarListEditor: 双击编辑项目" << item->text();
        editListItem(item);
    } else {
        qDebug() << "VarListEditor: 双击的项目无效，无法编辑";
    }
}

void VarListEditor::onListSelectionChanged()
{
    updateButtonStates();
}

void VarListEditor::onListItemChanged(QListWidgetItem *item)
{
    if (m_readOnly || !item) {
        return;
    }

    // 处理内联编辑完成事件
    QString newText = item->text().trimmed();

    // 如果文本为空，恢复原始文本或删除项目
    if (newText.isEmpty()) {
        // 可以选择删除空项目或恢复原始文本
        // 这里选择删除空项目
        int row = m_listWidget->row(item);
        delete m_listWidget->takeItem(row);
        updateButtonStates();
        emitChangeSignals();
        qDebug() << "VarListEditor: 删除空的列表项";
        return;
    }

    // 更新项目文本（去除首尾空白）
    if (item->text() != newText) {
        item->setText(newText);
    }

    // 不再动态修改ItemFlags，保持项目始终可编辑
    // 这样避免了Qt内部状态不一致的问题

    emitChangeSignals();
    qDebug() << "VarListEditor: 内联编辑完成，新文本:" << newText;
}

void VarListEditor::onQuickAddReturnPressed()
{
    onQuickAddButtonClicked();
}

// ========== UI初始化 ==========

void VarListEditor::setupUI()
{
    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(5);

    // 创建内容区域布局
    m_contentLayout = new QHBoxLayout();
    m_mainLayout->addLayout(m_contentLayout);

    // 创建列表显示区域
    m_listWidget = new QListWidget(this);
    m_listWidget->setMinimumHeight(120);
    m_listWidget->setMaximumHeight(200);
    m_listWidget->setSelectionMode(QAbstractItemView::SingleSelection);

    // 安装事件过滤器，防止意外的键盘操作
    m_listWidget->installEventFilter(this);

    m_contentLayout->addWidget(m_listWidget, 1);

    // 创建按钮区域
    m_buttonWidget = new QWidget(this);
    m_buttonLayout = new QVBoxLayout(m_buttonWidget);
    m_buttonLayout->setContentsMargins(5, 0, 0, 0);
    m_buttonLayout->setSpacing(3);

    // 创建操作按钮
    m_addButton = new QPushButton(tr("添加"), m_buttonWidget);
    m_deleteButton = new QPushButton(tr("删除"), m_buttonWidget);
    m_moveUpButton = new QPushButton(tr("上移"), m_buttonWidget);
    m_moveDownButton = new QPushButton(tr("下移"), m_buttonWidget);
    m_editButton = new QPushButton(tr("编辑"), m_buttonWidget);
    m_clearButton = new QPushButton(tr("清空"), m_buttonWidget);

    // 设置按钮大小
    QSize buttonSize(60, 25);
    m_addButton->setFixedSize(buttonSize);
    m_deleteButton->setFixedSize(buttonSize);
    m_moveUpButton->setFixedSize(buttonSize);
    m_moveDownButton->setFixedSize(buttonSize);
    m_editButton->setFixedSize(buttonSize);
    m_clearButton->setFixedSize(buttonSize);

    // 添加按钮到布局
    m_buttonLayout->addWidget(m_addButton);
    m_buttonLayout->addWidget(m_deleteButton);
    m_buttonLayout->addWidget(m_moveUpButton);
    m_buttonLayout->addWidget(m_moveDownButton);
    m_buttonLayout->addWidget(m_editButton);
    m_buttonLayout->addWidget(m_clearButton);
    m_buttonLayout->addStretch();

    m_contentLayout->addWidget(m_buttonWidget);

    // 创建快速添加区域
    m_quickAddWidget = new QWidget(this);
    m_quickAddLayout = new QHBoxLayout(m_quickAddWidget);
    m_quickAddLayout->setContentsMargins(0, 0, 0, 0);

    m_quickAddLabel = new QLabel(tr("快速添加:"), m_quickAddWidget);
    m_quickAddEdit = new QLineEdit(m_quickAddWidget);
    m_quickAddEdit->setPlaceholderText(tr("输入新项目"));
    m_quickAddButton = new QPushButton(tr("添加"), m_quickAddWidget);
    m_quickAddButton->setFixedSize(50, 25);

    m_quickAddLayout->addWidget(m_quickAddLabel);
    m_quickAddLayout->addWidget(m_quickAddEdit, 1);
    m_quickAddLayout->addWidget(m_quickAddButton);

    m_mainLayout->addWidget(m_quickAddWidget);
}

void VarListEditor::connectSignals()
{
    // 连接按钮信号
    connect(m_addButton, &QPushButton::clicked, this, &VarListEditor::onAddButtonClicked);
    connect(m_deleteButton, &QPushButton::clicked, this, &VarListEditor::onDeleteButtonClicked);
    connect(m_moveUpButton, &QPushButton::clicked, this, &VarListEditor::onMoveUpButtonClicked);
    connect(m_moveDownButton, &QPushButton::clicked, this, &VarListEditor::onMoveDownButtonClicked);
    connect(m_editButton, &QPushButton::clicked, this, &VarListEditor::onEditButtonClicked);
    connect(m_clearButton, &QPushButton::clicked, this, &VarListEditor::onClearButtonClicked);
    connect(m_quickAddButton, &QPushButton::clicked, this, &VarListEditor::onQuickAddButtonClicked);

    // 连接列表信号
    connect(m_listWidget, &QListWidget::itemDoubleClicked, this, &VarListEditor::onListItemDoubleClicked);
    connect(m_listWidget, &QListWidget::itemSelectionChanged, this, &VarListEditor::onListSelectionChanged);

    // 添加项目编辑完成信号连接 - 用于内联编辑
    connect(m_listWidget, &QListWidget::itemChanged, this, &VarListEditor::onListItemChanged);

    // 连接快速添加信号
    connect(m_quickAddEdit, &QLineEdit::returnPressed, this, &VarListEditor::onQuickAddReturnPressed);
}

// ========== 状态更新 ==========

void VarListEditor::updateButtonStates()
{
    if (m_readOnly) {
        return; // 只读模式下不更新按钮状态
    }

    int currentRow = getCurrentRow();
    int itemCount = m_listWidget ? m_listWidget->count() : 0;
    bool hasSelection = currentRow >= 0;

    // 更新按钮启用状态
    if (m_deleteButton) m_deleteButton->setEnabled(hasSelection);
    if (m_moveUpButton) m_moveUpButton->setEnabled(hasSelection && currentRow > 0);
    if (m_moveDownButton) m_moveDownButton->setEnabled(hasSelection && currentRow < itemCount - 1);
    if (m_editButton) m_editButton->setEnabled(hasSelection);
    if (m_clearButton) m_clearButton->setEnabled(itemCount > 0);
}

void VarListEditor::emitChangeSignals()
{
    int count = itemCount();
    emit listChanged();
    emit itemCountChanged(count);
}

// ========== 列表操作 ==========

void VarListEditor::addListItem(const QString &text)
{
    if (m_readOnly || !m_listWidget || text.trimmed().isEmpty()) {
        return;
    }

    QString cleanText = text.trimmed();
    QListWidgetItem *newItem = new QListWidgetItem(cleanText);

    // 创建时就设置为可编辑，避免运行时动态修改标志位
    newItem->setFlags(Qt::ItemIsSelectable | Qt::ItemIsEnabled | Qt::ItemIsEditable);

    m_listWidget->addItem(newItem);

    // 选中新添加的项目
    m_listWidget->setCurrentRow(m_listWidget->count() - 1);

    updateButtonStates();
    emitChangeSignals();

    qDebug() << "VarListEditor: 添加列表项" << cleanText;
}

void VarListEditor::editListItem(QListWidgetItem *item)
{
    if (m_readOnly) {
        qDebug() << "VarListEditor: 只读模式，无法编辑";
        return;
    }

    // 使用安全检查方法验证项目有效性
    if (!isItemValid(item)) {
        qDebug() << "VarListEditor: 项目无效，无法编辑 - item:" << (item != nullptr)
                 << "listWidget:" << (m_listWidget != nullptr);
        return;
    }

    int row = m_listWidget->row(item);
    qDebug() << "VarListEditor: 开始编辑项目，行号=" << row << "文本=" << item->text();

    // 不再动态修改ItemFlags，直接启动编辑
    // 项目在创建时已经设置为可编辑状态

    // 使用延迟调用避免在模态对话框中的事件循环冲突
    QTimer::singleShot(0, [this, row]() {
        if (m_listWidget && row >= 0 && row < m_listWidget->count()) {
            QListWidgetItem *safeItem = m_listWidget->item(row);
            if (safeItem) {
                m_listWidget->editItem(safeItem);
                qDebug() << "VarListEditor: 延迟启动内联编辑，行号=" << row << "文本=" << safeItem->text();
            } else {
                qDebug() << "VarListEditor: 延迟调用时项目已无效，行号=" << row;
            }
        } else {
            qDebug() << "VarListEditor: 延迟调用时列表状态无效 - listWidget:" << (m_listWidget != nullptr)
                     << "row:" << row << "count:" << (m_listWidget ? m_listWidget->count() : -1);
        }
    });

    qDebug() << "VarListEditor: 准备编辑列表项完成，等待延迟调用";
}

int VarListEditor::getCurrentRow() const
{
    return m_listWidget ? m_listWidget->currentRow() : -1;
}

bool VarListEditor::isItemValid(QListWidgetItem *item) const
{
    if (!item || !m_listWidget) {
        return false;
    }

    // 检查项目是否仍在列表中
    int row = m_listWidget->row(item);
    if (row < 0) {
        return false;
    }

    // 通过行号重新获取项目，确保指针一致性
    QListWidgetItem *currentItem = m_listWidget->item(row);
    return (currentItem == item);
}
