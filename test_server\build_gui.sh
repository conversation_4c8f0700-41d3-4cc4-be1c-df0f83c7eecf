#!/bin/bash

echo "========================================"
echo "  CommandTransceiver 测试服务器 GUI版本"
echo "  构建脚本"
echo "========================================"

# 检查Qt环境
if ! command -v qmake &> /dev/null; then
    echo "错误: 未找到qmake，请确保Qt环境已正确配置"
    exit 1
fi

# 清理之前的构建
echo "清理之前的构建文件..."
rm -rf build bin

# 创建构建目录
mkdir -p build
cd build

# 生成Makefile
echo "生成Makefile..."
qmake ../test_server.pro

# 编译项目
echo "开始编译..."
make -j$(nproc)

if [ $? -eq 0 ]; then
    echo ""
    echo "========================================"
    echo "  编译成功！"
    echo "========================================"
    echo "可执行文件位置:"
    find ../bin -name "*.exe" -o -name "CommandTransceiverTestServerGUI*" 2>/dev/null
    echo ""
    echo "运行方式:"
    echo "  GUI模式: ./CommandTransceiverTestServerGUI"
    echo "  控制台模式: ./CommandTransceiverTestServerGUI -c"
    echo "========================================"
else
    echo ""
    echo "========================================"
    echo "  编译失败！"
    echo "========================================"
    exit 1
fi

cd ..
