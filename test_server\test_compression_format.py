#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 CommandTransceiver 压缩格式兼容性

验证修改后的测试服务器能否正确处理 CommandTransceiver 格式的压缩数据
"""

import socket
import json
import struct
import zlib
import time
import sys

def qt_compress(data):
    """模拟 Qt 的 qCompress 函数"""
    if isinstance(data, str):
        data = data.encode('utf-8')
    
    # Qt qCompress 格式：4字节原始长度（大端序）+ zlib压缩数据
    compressed = zlib.compress(data)
    return struct.pack('>I', len(data)) + compressed

def simulate_commandtransceiver_data(json_data, use_compression=True):
    """模拟 CommandTransceiver 的完整数据处理流程"""
    # 1. JSON对象转为字符串（模拟 QJsonDocument::toBinaryData）
    json_str = json.dumps(json_data, ensure_ascii=False)
    binary_data = json_str.encode('utf-8')
    
    print(f"  1. JSON 转二进制: {len(binary_data)} 字节")
    
    if use_compression:
        # 2. 使用 qCompress 压缩
        compressed_data = qt_compress(binary_data)
        print(f"  2. qCompress 压缩: {len(compressed_data)} 字节")
        print(f"     压缩率: {(1.0 - len(compressed_data) / len(binary_data)) * 100:.1f}%")
        return compressed_data
    else:
        print(f"  2. 不压缩，直接使用二进制数据")
        return binary_data

def send_prelength_data(sock, data):
    """使用 PreLength 协议发送数据"""
    # PreLength 格式：4字节长度头 + 数据
    length = len(data)
    packet = struct.pack('>I', length) + data
    
    print(f"  3. PreLength 封装: 长度头({length}) + 数据({len(data)}) = {len(packet)} 字节")
    
    sock.send(packet)
    print(f"  4. 发送完成")

def receive_response(sock, timeout=5):
    """接收服务器响应"""
    sock.settimeout(timeout)
    try:
        # 接收长度头
        length_data = sock.recv(4)
        if len(length_data) != 4:
            return None
            
        length = struct.unpack('>I', length_data)[0]
        print(f"  ← 响应长度: {length} 字节")
        
        # 接收数据
        data = b''
        while len(data) < length:
            chunk = sock.recv(length - len(data))
            if not chunk:
                break
            data += chunk
        
        # 尝试解析JSON
        try:
            response = json.loads(data.decode('utf-8'))
            print(f"  ← 响应内容: {json.dumps(response, ensure_ascii=False)}")
            return response
        except:
            print(f"  ← 原始响应: {len(data)} 字节")
            return data
            
    except socket.timeout:
        print("  ← 响应超时")
        return None
    except Exception as e:
        print(f"  ← 接收错误: {e}")
        return None

def test_compression_compatibility(host="localhost", port=8080):
    """测试压缩兼容性"""
    print("=" * 60)
    print("CommandTransceiver 压缩格式兼容性测试")
    print("=" * 60)
    
    # 连接到服务器
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect((host, port))
        print(f"✓ 已连接到 {host}:{port}")
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False
    
    try:
        # 测试1: 发送压缩数据
        print("\n1. 测试压缩数据发送:")
        print("-" * 40)
        
        test_data = {
            "type": "compression_test",
            "data": {
                "message": "这是一个压缩测试消息，包含中文字符。" * 10,
                "numbers": list(range(100)),
                "repeated": ["重复数据"] * 20
            },
            "id": "compression-test-001"
        }
        
        compressed_data = simulate_commandtransceiver_data(test_data, use_compression=True)
        send_prelength_data(sock, compressed_data)
        
        response = receive_response(sock)
        if response:
            print("  ✓ 压缩数据发送成功")
        else:
            print("  ✗ 压缩数据发送失败")
        
        time.sleep(1)
        
        # 测试2: 发送未压缩数据
        print("\n2. 测试未压缩数据发送:")
        print("-" * 40)
        
        test_data2 = {
            "type": "uncompressed_test",
            "data": "简单的未压缩测试数据",
            "id": "uncompressed-test-001"
        }
        
        uncompressed_data = simulate_commandtransceiver_data(test_data2, use_compression=False)
        send_prelength_data(sock, uncompressed_data)
        
        response = receive_response(sock)
        if response:
            print("  ✓ 未压缩数据发送成功")
        else:
            print("  ✗ 未压缩数据发送失败")
        
        time.sleep(1)
        
        # 测试3: 查询压缩统计
        print("\n3. 测试压缩统计查询:")
        print("-" * 40)
        
        stats_query = {
            "type": "compression_stats",
            "id": "stats-query-001"
        }
        
        stats_data = simulate_commandtransceiver_data(stats_query, use_compression=False)
        send_prelength_data(sock, stats_data)
        
        response = receive_response(sock)
        if response and response.get("type") == "compression_stats_response":
            print("  ✓ 压缩统计查询成功")
            print(f"    压缩包数量: {response.get('compressedPacketsReceived', 0)}")
            print(f"    压缩前总大小: {response.get('totalUncompressedBytes', 0)} 字节")
            print(f"    压缩后总大小: {response.get('totalCompressedBytes', 0)} 字节")
        else:
            print("  ✗ 压缩统计查询失败")
        
        return True
        
    finally:
        sock.close()
        print("\n✓ 连接已关闭")

def test_format_details():
    """测试格式细节"""
    print("\n" + "=" * 60)
    print("数据格式细节测试")
    print("=" * 60)
    
    test_json = {"type": "test", "message": "Hello, 世界!"}
    
    print("\n原始 JSON:")
    json_str = json.dumps(test_json, ensure_ascii=False)
    print(f"  内容: {json_str}")
    print(f"  UTF-8 编码长度: {len(json_str.encode('utf-8'))} 字节")
    
    print("\n模拟 CommandTransceiver 压缩:")
    compressed = simulate_commandtransceiver_data(test_json, use_compression=True)
    print(f"  压缩后长度: {len(compressed)} 字节")
    
    # 解析压缩格式
    if len(compressed) >= 4:
        orig_len = struct.unpack('>I', compressed[:4])[0]
        zlib_data = compressed[4:]
        print(f"  qCompress 格式:")
        print(f"    原始长度头: {orig_len} 字节")
        print(f"    zlib 数据长度: {len(zlib_data)} 字节")
        
        # 尝试解压缩
        try:
            decompressed = zlib.decompress(zlib_data)
            print(f"    解压缩成功: {len(decompressed)} 字节")
            print(f"    解压内容: {decompressed.decode('utf-8')}")
        except Exception as e:
            print(f"    解压缩失败: {e}")

def main():
    """主函数"""
    print("CommandTransceiver 压缩格式测试工具")
    
    # 检查命令行参数
    host = "localhost"
    port = 8080
    
    if len(sys.argv) > 1:
        port = int(sys.argv[1])
    if len(sys.argv) > 2:
        host = sys.argv[2]
    
    # 运行格式细节测试
    test_format_details()
    
    # 运行兼容性测试
    print(f"\n目标服务器: {host}:{port}")
    success = test_compression_compatibility(host, port)
    
    if success:
        print("\n" + "=" * 60)
        print("✓ 所有测试完成")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("✗ 测试失败")
        print("=" * 60)

if __name__ == "__main__":
    main()
