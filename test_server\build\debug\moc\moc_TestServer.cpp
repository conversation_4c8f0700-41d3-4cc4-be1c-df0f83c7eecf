/****************************************************************************
** Meta object code from reading C++ file 'TestServer.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../TestServer.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'TestServer.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_TestServer_t {
    QByteArrayData data[25];
    char stringdata0[384];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_TestServer_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_TestServer_t qt_meta_stringdata_TestServer = {
    {
QT_MOC_LITERAL(0, 0, 10), // "TestServer"
QT_MOC_LITERAL(1, 11, 13), // "serverStarted"
QT_MOC_LITERAL(2, 25, 0), // ""
QT_MOC_LITERAL(3, 26, 4), // "port"
QT_MOC_LITERAL(4, 31, 13), // "serverStopped"
QT_MOC_LITERAL(5, 45, 15), // "clientConnected"
QT_MOC_LITERAL(6, 61, 12), // "connectionId"
QT_MOC_LITERAL(7, 74, 13), // "clientAddress"
QT_MOC_LITERAL(8, 88, 18), // "clientDisconnected"
QT_MOC_LITERAL(9, 107, 19), // "jsonCommandReceived"
QT_MOC_LITERAL(10, 127, 7), // "command"
QT_MOC_LITERAL(11, 135, 15), // "rawDataReceived"
QT_MOC_LITERAL(12, 151, 4), // "data"
QT_MOC_LITERAL(13, 156, 17), // "printServerStatus"
QT_MOC_LITERAL(14, 174, 19), // "printConnectionList"
QT_MOC_LITERAL(15, 194, 15), // "printStatistics"
QT_MOC_LITERAL(16, 210, 15), // "onNewConnection"
QT_MOC_LITERAL(17, 226, 30), // "onClientConnectionStateChanged"
QT_MOC_LITERAL(18, 257, 33), // "ClientConnection::ConnectionS..."
QT_MOC_LITERAL(19, 291, 5), // "state"
QT_MOC_LITERAL(20, 297, 20), // "onClientDisconnected"
QT_MOC_LITERAL(21, 318, 19), // "onClientJsonCommand"
QT_MOC_LITERAL(22, 338, 15), // "onClientRawData"
QT_MOC_LITERAL(23, 354, 23), // "onClientConnectionError"
QT_MOC_LITERAL(24, 378, 5) // "error"

    },
    "TestServer\0serverStarted\0\0port\0"
    "serverStopped\0clientConnected\0"
    "connectionId\0clientAddress\0"
    "clientDisconnected\0jsonCommandReceived\0"
    "command\0rawDataReceived\0data\0"
    "printServerStatus\0printConnectionList\0"
    "printStatistics\0onNewConnection\0"
    "onClientConnectionStateChanged\0"
    "ClientConnection::ConnectionState\0"
    "state\0onClientDisconnected\0"
    "onClientJsonCommand\0onClientRawData\0"
    "onClientConnectionError\0error"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_TestServer[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      15,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   89,    2, 0x06 /* Public */,
       4,    0,   92,    2, 0x06 /* Public */,
       5,    2,   93,    2, 0x06 /* Public */,
       8,    1,   98,    2, 0x06 /* Public */,
       9,    2,  101,    2, 0x06 /* Public */,
      11,    2,  106,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      13,    0,  111,    2, 0x0a /* Public */,
      14,    0,  112,    2, 0x0a /* Public */,
      15,    0,  113,    2, 0x0a /* Public */,
      16,    0,  114,    2, 0x08 /* Private */,
      17,    2,  115,    2, 0x08 /* Private */,
      20,    1,  120,    2, 0x08 /* Private */,
      21,    2,  123,    2, 0x08 /* Private */,
      22,    2,  128,    2, 0x08 /* Private */,
      23,    2,  133,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::UShort,    3,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QUuid, QMetaType::QString,    6,    7,
    QMetaType::Void, QMetaType::QUuid,    6,
    QMetaType::Void, QMetaType::QUuid, QMetaType::QJsonObject,    6,   10,
    QMetaType::Void, QMetaType::QUuid, QMetaType::QByteArray,    6,   12,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QUuid, 0x80000000 | 18,    6,   19,
    QMetaType::Void, QMetaType::QUuid,    6,
    QMetaType::Void, QMetaType::QUuid, QMetaType::QJsonObject,    6,   10,
    QMetaType::Void, QMetaType::QUuid, QMetaType::QByteArray,    6,   12,
    QMetaType::Void, QMetaType::QUuid, QMetaType::QString,    6,   24,

       0        // eod
};

void TestServer::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<TestServer *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->serverStarted((*reinterpret_cast< quint16(*)>(_a[1]))); break;
        case 1: _t->serverStopped(); break;
        case 2: _t->clientConnected((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 3: _t->clientDisconnected((*reinterpret_cast< const QUuid(*)>(_a[1]))); break;
        case 4: _t->jsonCommandReceived((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< const QJsonObject(*)>(_a[2]))); break;
        case 5: _t->rawDataReceived((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< const QByteArray(*)>(_a[2]))); break;
        case 6: _t->printServerStatus(); break;
        case 7: _t->printConnectionList(); break;
        case 8: _t->printStatistics(); break;
        case 9: _t->onNewConnection(); break;
        case 10: _t->onClientConnectionStateChanged((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< ClientConnection::ConnectionState(*)>(_a[2]))); break;
        case 11: _t->onClientDisconnected((*reinterpret_cast< const QUuid(*)>(_a[1]))); break;
        case 12: _t->onClientJsonCommand((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< const QJsonObject(*)>(_a[2]))); break;
        case 13: _t->onClientRawData((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< const QByteArray(*)>(_a[2]))); break;
        case 14: _t->onClientConnectionError((*reinterpret_cast< const QUuid(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (TestServer::*)(quint16 );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TestServer::serverStarted)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (TestServer::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TestServer::serverStopped)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (TestServer::*)(const QUuid & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TestServer::clientConnected)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (TestServer::*)(const QUuid & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TestServer::clientDisconnected)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (TestServer::*)(const QUuid & , const QJsonObject & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TestServer::jsonCommandReceived)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (TestServer::*)(const QUuid & , const QByteArray & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TestServer::rawDataReceived)) {
                *result = 5;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject TestServer::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_TestServer.data,
    qt_meta_data_TestServer,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *TestServer::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TestServer::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_TestServer.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int TestServer::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 15)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 15;
    }
    return _id;
}

// SIGNAL 0
void TestServer::serverStarted(quint16 _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void TestServer::serverStopped()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void TestServer::clientConnected(const QUuid & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void TestServer::clientDisconnected(const QUuid & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void TestServer::jsonCommandReceived(const QUuid & _t1, const QJsonObject & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void TestServer::rawDataReceived(const QUuid & _t1, const QByteArray & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
