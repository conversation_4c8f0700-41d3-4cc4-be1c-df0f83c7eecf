#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CommandTransceiver 测试服务器压缩功能演示

演示如何使用压缩功能与测试服务器通信
"""

import socket
import json
import struct
import zlib
import time
import sys

class CompressionDemo:
    """压缩功能演示客户端"""
    
    def __init__(self, host="localhost", port=8080):
        self.host = host
        self.port = port
        self.socket = None
        
    def qt_compress(self, data):
        """模拟 Qt 的 qCompress 函数"""
        if isinstance(data, str):
            data = data.encode('utf-8')

        # Qt qCompress 格式：4字节原始长度（大端序）+ zlib压缩数据
        compressed = zlib.compress(data)
        return struct.pack('>I', len(data)) + compressed

    def simulate_commandtransceiver_format(self, json_data, use_compression=True):
        """模拟 CommandTransceiver 的数据格式"""
        # 模拟 QJsonDocument::toBinaryData()
        json_str = json.dumps(json_data, ensure_ascii=False)
        binary_data = json_str.encode('utf-8')

        if use_compression:
            return self.qt_compress(binary_data)
        else:
            return binary_data
    
    def connect(self):
        """连接到服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            print(f"✓ 已连接到 {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.socket:
            self.socket.close()
            self.socket = None
            print("✓ 已断开连接")
    
    def send_compressed_json(self, data):
        """发送压缩的JSON数据（CommandTransceiver格式）"""
        json_str = json.dumps(data, ensure_ascii=False)
        original_size = len(json_str.encode('utf-8'))

        print(f"原始JSON大小: {original_size} 字节")
        print(f"原始JSON内容: {json_str}")

        # 使用 CommandTransceiver 格式压缩
        compressed_data = self.simulate_commandtransceiver_format(data, use_compression=True)
        compression_ratio = (1.0 - len(compressed_data) / original_size) * 100

        print(f"CommandTransceiver 压缩后大小: {len(compressed_data)} 字节")
        print(f"压缩率: {compression_ratio:.1f}%")

        # 使用 PreLength 协议发送
        packet = struct.pack('>I', len(compressed_data)) + compressed_data

        self.socket.send(packet)
        print("✓ 已发送 CommandTransceiver 格式压缩数据")
        
    def send_uncompressed_json(self, data):
        """发送未压缩的JSON数据（CommandTransceiver格式）"""
        json_str = json.dumps(data, ensure_ascii=False)

        # 使用 CommandTransceiver 格式但不压缩
        uncompressed_data = self.simulate_commandtransceiver_format(data, use_compression=False)

        print(f"未压缩 CommandTransceiver 格式大小: {len(uncompressed_data)} 字节")
        print(f"JSON内容: {json_str}")

        # 使用 PreLength 协议发送
        packet = struct.pack('>I', len(uncompressed_data)) + uncompressed_data

        self.socket.send(packet)
        print("✓ 已发送未压缩 CommandTransceiver 格式数据")
    
    def receive_response(self):
        """接收服务器响应"""
        try:
            # 接收长度头
            length_data = self.socket.recv(4)
            if len(length_data) != 4:
                return None
                
            length = struct.unpack('>I', length_data)[0]
            
            # 接收数据
            data = b''
            while len(data) < length:
                chunk = self.socket.recv(length - len(data))
                if not chunk:
                    break
                data += chunk
            
            # 尝试解析JSON
            try:
                response = json.loads(data.decode('utf-8'))
                print(f"← 接收响应: {json.dumps(response, ensure_ascii=False, indent=2)}")
                return response
            except:
                print(f"← 接收原始数据: {len(data)} 字节")
                return data
                
        except Exception as e:
            print(f"接收数据失败: {e}")
            return None

def demo_compression_vs_uncompressed():
    """演示压缩与非压缩的对比"""
    print("=" * 60)
    print("压缩功能对比演示")
    print("=" * 60)
    
    demo = CompressionDemo()
    
    if not demo.connect():
        return
    
    try:
        # 创建大量重复数据，适合压缩
        large_data = {
            "type": "compression_demo",
            "data": {
                "description": "这是一个压缩演示，包含大量重复数据。" * 50,
                "repeated_array": ["重复项目"] * 100,
                "numbers": list(range(500)),
                "nested_data": {
                    "level1": {
                        "level2": {
                            "repeated_text": "嵌套重复文本 " * 30
                        }
                    }
                }
            },
            "timestamp": time.time(),
            "id": "compression-demo-001"
        }
        
        print("\n1. 发送压缩数据:")
        print("-" * 40)
        demo.send_compressed_json(large_data)
        time.sleep(1)
        demo.receive_response()
        
        print("\n2. 发送相同的未压缩数据:")
        print("-" * 40)
        demo.send_uncompressed_json(large_data)
        time.sleep(1)
        demo.receive_response()
        
        print("\n3. 查询压缩统计:")
        print("-" * 40)
        stats_query = {
            "type": "compression_stats",
            "id": "stats-query-001"
        }
        demo.send_uncompressed_json(stats_query)
        time.sleep(1)
        demo.receive_response()
        
    finally:
        demo.disconnect()

def demo_different_data_types():
    """演示不同数据类型的压缩效果"""
    print("\n" + "=" * 60)
    print("不同数据类型压缩效果演示")
    print("=" * 60)
    
    demo = CompressionDemo()
    
    if not demo.connect():
        return
    
    try:
        # 测试数据集
        test_cases = [
            {
                "name": "高重复性文本",
                "data": {
                    "type": "test_high_repetition",
                    "content": "重复文本 " * 200,
                    "id": "high-rep-001"
                }
            },
            {
                "name": "随机数据",
                "data": {
                    "type": "test_random",
                    "content": "".join([chr(ord('a') + (i * 7) % 26) for i in range(1000)]),
                    "id": "random-001"
                }
            },
            {
                "name": "JSON结构数据",
                "data": {
                    "type": "test_structured",
                    "users": [
                        {"name": f"用户{i}", "age": 20 + i % 50, "city": "北京"}
                        for i in range(50)
                    ],
                    "id": "structured-001"
                }
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. 测试 {test_case['name']}:")
            print("-" * 40)
            demo.send_compressed_json(test_case['data'])
            time.sleep(1)
            demo.receive_response()
            
    finally:
        demo.disconnect()

def main():
    """主函数"""
    print("CommandTransceiver 测试服务器 - 压缩功能演示")
    
    # 检查命令行参数
    host = "localhost"
    port = 8080
    
    if len(sys.argv) > 1:
        port = int(sys.argv[1])
    if len(sys.argv) > 2:
        host = sys.argv[2]
    
    print(f"目标服务器: {host}:{port}")
    
    # 运行演示
    demo_compression_vs_uncompressed()
    demo_different_data_types()
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
