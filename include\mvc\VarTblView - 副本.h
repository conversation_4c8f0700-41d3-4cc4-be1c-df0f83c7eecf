#ifndef VAR_TBL_VIEW_H
#define VAR_TBL_VIEW_H

#include <QWidget>
#include <QTableView>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QToolBar>
#include <QPushButton>
#include <QLabel>
#include <QProgressBar>
#include <QGroupBox>
#include <QLineEdit>
#include <QComboBox>
#include <QTextEdit>
#include <QSplitter>
#include <QHeaderView>
#include <QItemSelectionModel>
#include <QMenu>
#include <QAction>
#include <QTimer>
#include <QStatusBar>

// 引入相关组件
#include "VarTblModel.h"
#include "VarTblController.h"
#include "VarListEditor.h"
#include "VarTblItemDelegate.h"

// 引入ada3数据结构
#include "cmd/var.h"

/**
 * @brief VarTbl表格视图 - 纯View层实现
 * 
 * 设计原则：
 * - 只负责UI显示和用户交互
 * - 不包含任何数据逻辑和业务逻辑
 * - 通过信号槽与Controller通信
 * - 提供丰富的用户界面功能
 */
class VarTblView : public QWidget
{
    Q_OBJECT

public:
    // 编辑模式枚举已移除，现在只使用表格编辑模式

    explicit VarTblView(QWidget *parent = nullptr);
    ~VarTblView() override;

    // ========== 组件设置 ==========
    
    /**
     * @brief 设置数据模型
     * @param model VarTbl数据模型
     */
    void setModel(VarTblModel *model);
    
    /**
     * @brief 设置控制器
     * @param controller VarTbl控制器
     */
    void setController(VarTblController *controller);

    // ========== UI配置 ==========
    
    /**
     * @brief 设置是否显示工具栏
     * @param visible 是否显示
     */
    void setToolbarVisible(bool visible);
    
    // 编辑面板相关方法已移除，现在只使用表格编辑模式
    
    /**
     * @brief 设置是否显示状态栏
     * @param visible 是否显示
     */
    void setStatusBarVisible(bool visible);
    
    /**
     * @brief 设置是否启用自动刷新
     * @param enabled 是否启用
     * @param intervalMs 刷新间隔（毫秒）
     */
    void setAutoRefresh(bool enabled, int intervalMs = 5000);
    
    /**
     * @brief 设置表格是否只读
     * @param readOnly 是否只读
     */
    void setReadOnly(bool readOnly);

    // ========== 选择和导航 ==========
    
    /**
     * @brief 获取当前选中的变量ID列表
     * @return 变量ID列表
     */
    QList<int> getSelectedVariableIds() const;
    
    /**
     * @brief 选中指定变量
     * @param id 变量ID
     */
    void selectVariable(int id);
    
    /**
     * @brief 清除选择
     */
    void clearSelection();

    // ========== 数据导入导出 ==========
    
    /**
     * @brief 导出数据到文件
     * @param filePath 文件路径
     * @param format 导出格式（json/csv）
     * @return 是否成功
     */
    bool exportData(const QString &filePath, const QString &format = "json");
    
    /**
     * @brief 从文件导入数据
     * @param filePath 文件路径
     * @param format 导入格式（json/csv）
     * @return 是否成功
     */
    bool importData(const QString &filePath, const QString &format = "json");

    // ========== 表格编辑模式控制 ==========

    /**
     * @brief 设置表格编辑模式
     * @param enabled 是否启用表格编辑模式
     */
    void setTableEditMode(bool enabled);

    /**
     * @brief 获取表格编辑模式状态
     * @return 是否启用表格编辑模式
     */
    bool isTableEditMode() const { return m_tableEditMode; }

    /**
     * @brief 切换表格编辑模式
     */
    void toggleTableEditMode();

signals:
    // ========== 简化的用户操作信号 ==========

    /**
     * @brief 请求添加变量
     */
    void addVariableRequested();

    /**
     * @brief 请求编辑变量
     * @param id 变量ID
     */
    void editVariableRequested(int id);

    /**
     * @brief 请求删除变量
     * @param ids 变量ID列表
     */
    void deleteVariableRequested(const QList<int> &ids);

    /**
     * @brief 数据提交信号（编辑完成时发出）
     * @param id 变量ID
     * @param label 变量标签
     * @param typeText 变量类型文本
     * @param value 变量值
     */
    void dataSubmitted(int id, const QString &label, const QString &typeText, const QVariant &value);

    /**
     * @brief 编辑取消信号
     */
    void editCancelled();

    /**
     * @brief 请求刷新数据
     */
    void refreshDataRequested();

    /**
     * @brief 请求同步数据
     */
    void syncDataRequested();

    /**
     * @brief 请求连接服务器
     */
    void connectToServerRequested();

    /**
     * @brief 请求断开连接
     */
    void disconnectFromServerRequested();

    // ========== 选择变化信号 ==========

    /**
     * @brief 选择变化信号
     * @param selectedIds 选中的变量ID列表
     */
    void selectionChanged(const QList<int> &selectedIds);

private slots:
    // ========== UI事件处理 ==========
    
    void onAddButtonClicked();
    void onEditButtonClicked();
    void onDeleteButtonClicked();
    void onRefreshButtonClicked();
    void onSyncButtonClicked();
    void onImportButtonClicked();
    void onExportButtonClicked();
    void onConnectButtonClicked();
    void onDisconnectButtonClicked();
    void onTableEditButtonClicked();    // 表格编辑模式切换
    
    // ========== 表格事件 ==========
    
    void onTableSelectionChanged();
    void onTableDoubleClicked(const QModelIndex &index);
    void onTableContextMenuRequested(const QPoint &pos);
    
    // 编辑面板事件处理已移除，现在只使用表格编辑模式

    // ========== 表格编辑相关槽 ==========

    void onItemDelegateListEditingFinished(const QModelIndex& index, const QStringList& newList);
    void onItemDelegateCellDataChanged(const QModelIndex& index, const QVariant& newValue);
    void onItemDelegateEditingError(const QString& message);

    // ========== Controller UI状态更新槽 ==========

    /**
     * @brief 响应操作完成
     * @param success 是否成功
     * @param message 消息内容
     */
    void onOperationCompleted(bool success, const QString &message);

    /**
     * @brief 响应连接状态变化
     * @param connected 是否已连接
     */
    void onConnectionStatusChanged(bool connected);

    /**
     * @brief 响应同步状态变化
     * @param syncing 是否正在同步
     */
    void onSyncStatusChanged(bool syncing);

    /**
     * @brief 响应忙碌状态变化
     * @param busy 是否忙碌
     */
    void onBusyStatusChanged(bool busy);

    /**
     * @brief 响应显示消息请求
     * @param message 消息内容
     * @param isError 是否为错误消息
     */
    void onShowMessage(const QString &message, bool isError);
    
    // ========== 定时器事件 ==========
    
    void onAutoRefreshTimeout();

private:
    // ========== UI组件 ==========
    
    // 主布局
    QVBoxLayout *m_mainLayout;
    QSplitter *m_mainSplitter;
    
    // 工具栏
    QToolBar *m_toolbar;
    QAction *m_addAction;
    QAction *m_editAction;
    QAction *m_deleteAction;
    QAction *m_refreshAction;
    QAction *m_syncAction;
    QAction *m_importAction;
    QAction *m_exportAction;
    QAction *m_connectAction;
    QAction *m_disconnectAction;
    QAction *m_tableEditAction;        // 表格编辑模式切换
    
    // 表格视图
    QTableView *m_tableView;
    QMenu *m_contextMenu;
    VarTblItemDelegate *m_itemDelegate;  // 表格编辑器代理
    
    // 编辑面板组件已移除，现在只使用表格编辑模式
    
    // 状态栏
    QWidget *m_statusWidget;
    QHBoxLayout *m_statusLayout;
    QLabel *m_statusLabel;
    QProgressBar *m_progressBar;
    QLabel *m_countLabel;
    QLabel *m_connectionLabel;
    
    // ========== 核心组件 ==========
    
    VarTblModel *m_model;
    VarTblController *m_controller;
    
    // ========== 状态管理 ==========

    bool m_readOnly;
    bool m_tableEditMode;               // 表格编辑模式状态
    
    // ========== 自动刷新 ==========
    
    QTimer *m_autoRefreshTimer;
    bool m_autoRefreshEnabled;
    
    // ========== UI初始化 ==========
    
    void setupUI();
    void setupToolbar();
    void setupTableView();
    void setupStatusBar();
    void setupContextMenu();
    void connectSignals();
    void setupTableEditDelegate();      // 设置表格编辑代理

    // ========== UI更新 ==========

    void updateToolbarState();
    void updateStatusBar();
    void updateConnectionStatus(bool connected);
    void updateTableEditMode();         // 更新表格编辑模式状态
    
    // 编辑功能已移除，现在只使用表格编辑模式

    // ========== 纯显示相关的辅助方法 ==========

    QString formatVariableValue(const cmd::Var &var) const;  // 仅用于显示格式化
    QString getVariableTypeString(const cmd::Var &var) const;  // 仅用于显示
    
    void showMessage(const QString &message, int timeout = 3000);
    void showError(const QString &error);
    void showSuccess(const QString &message);

    // 数据验证已移至Controller层
    int generateNewVariableId() const;  // 仅用于UI显示的ID生成
};

#endif // VAR_TBL_VIEW_H
