@echo off
echo ========================================
echo  CommandTransceiver 测试服务器 GUI版本
echo  构建脚本
echo ========================================

REM 检查Qt环境
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到qmake，请确保Qt环境已正确配置
    pause
    exit /b 1
)

REM 清理之前的构建
echo 清理之前的构建文件...
if exist build rmdir /s /q build
if exist bin rmdir /s /q bin

REM 创建构建目录
mkdir build
cd build

REM 生成Makefile
echo 生成Makefile...
qmake ../test_server.pro

REM 编译项目
echo 开始编译...
nmake

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo  编译成功！
    echo ========================================
    echo 可执行文件位置:
    dir ..\bin\debug\*.exe 2>nul
    dir ..\bin\release\*.exe 2>nul
    echo.
    echo 运行方式:
    echo   GUI模式: CommandTransceiverTestServerGUI.exe
    echo   控制台模式: CommandTransceiverTestServerGUI.exe -c
    echo ========================================
) else (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
)

cd ..
pause
