#include "mvc/VarTblModel.h"
#include <QDebug>
#include <QMutexLocker>
#include <QDateTime>
#include <QJsonArray>
#include <QJsonDocument>
#include <algorithm>

VarTblModel::VarTblModel(QObject *parent)
    : QAbstractTableModel(parent)
    , m_tableEditMode(false)
{
    qDebug() << "VarTblModel: 创建实例";
}

VarTblModel::~VarTblModel()
{
    qDebug() << "VarTblModel: 销毁实例";
}

// ========== QAbstractTableModel接口实现 ==========

int VarTblModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    QMutexLocker locker(&m_dataMutex);
    return m_displayOrder.size();
}

int VarTblModel::columnCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return ColumnCount;
}

QVariant VarTblModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= m_displayOrder.size() || index.column() >= ColumnCount) {
        return QVariant();
    }

    QMutexLocker locker(&m_dataMutex);
    int id = m_displayOrder.at(index.row());
    
    if (!m_varTbl.contains(id)) {
        return QVariant();
    }

    switch (role) {
    case Qt::DisplayRole:
        return getDisplayData(id, index.column());
    case Qt::EditRole:
        return getEditData(id, index.column());
    case Qt::ToolTipRole:
        return getDisplayData(id, index.column());
    case Qt::UserRole: // 返回变量ID
        return id;
    default:
        return QVariant();
    }
}

QVariant VarTblModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation != Qt::Horizontal || role != Qt::DisplayRole) {
        return QVariant();
    }

    switch (section) {
    case ColumnId:
        return tr("ID");
    case ColumnLabel:
        return tr("标签");
    case ColumnType:
        return tr("类型");
    case ColumnValue:
        return tr("值");
    case ColumnLastModified:
        return tr("最后修改");
    default:
        return QVariant();
    }
}

bool VarTblModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    if (!index.isValid() || index.row() >= m_displayOrder.size() || 
        index.column() >= ColumnCount || role != Qt::EditRole) {
        return false;
    }

    QMutexLocker locker(&m_dataMutex);
    int id = m_displayOrder.at(index.row());
    
    if (!m_varTbl.contains(id)) {
        return false;
    }

    bool success = setEditData(id, index.column(), value);
    if (success) {
        // 更新修改时间
        m_modifyTimes[id] = QDateTime::currentDateTime();
        
        // 发出数据变化信号
        QVector<int> roles;
        roles << role;
        emit dataChanged(index, index, roles);
        emit variableDataChanged("update", id);
    }
    
    return success;
}

Qt::ItemFlags VarTblModel::flags(const QModelIndex &index) const
{
    if (!index.isValid()) {
        return Qt::NoItemFlags;
    }

    Qt::ItemFlags flags = Qt::ItemIsEnabled | Qt::ItemIsSelectable;

    // 只有在表格编辑模式下才可编辑
    if (m_tableEditMode) {
        // 所有列都可编辑（包括ID列，用于新增数据时设置ID）
        flags |= Qt::ItemIsEditable;
    }

    return flags;
}

// ========== 数据操作接口 ==========

void VarTblModel::setVarTbl(const cmd::VarTbl &varTbl)
{
    beginResetModel();
    
    {
        QMutexLocker locker(&m_dataMutex);
        m_varTbl = varTbl;
        
        // 更新修改时间
        QDateTime now = QDateTime::currentDateTime();
        for (auto it = m_varTbl.cbegin(); it != m_varTbl.cend(); ++it) {
            if (!m_modifyTimes.contains(it->first)) {
                m_modifyTimes[it->first] = now;
            }
        }
        
        updateDisplayOrder();
    }
    
    endResetModel();
    emit variableDataChanged("setAll", -1);
    
    qDebug() << "VarTblModel: 设置VarTbl数据，共" << varTbl.size() << "个变量";
}

cmd::VarTbl VarTblModel::getVarTbl() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_varTbl;
}

bool VarTblModel::insertOrUpdateVariable(int id, const cmd::Var &var, const QString &label)
{
    bool isNewVariable;
    int newRow = -1;
    int updateRow = -1;

    // 第一阶段：在锁保护下准备数据
    {
        QMutexLocker locker(&m_dataMutex);
        isNewVariable = !m_varTbl.contains(id);

        if (isNewVariable) {
            newRow = m_displayOrder.size();
        } else {
            updateRow = getRowByVariableId(id);
        }
    }

    // 第二阶段：在无锁状态下调用Qt信号槽机制
    if (isNewVariable) {
        // 新增变量
        beginInsertRows(QModelIndex(), newRow, newRow);

        {
            QMutexLocker locker(&m_dataMutex);
            m_varTbl.insertOrAssign(id, {label, var});
            m_modifyTimes[id] = QDateTime::currentDateTime();
            updateDisplayOrder();
        }

        endInsertRows();

        // 在无锁状态下发出信号
        emit variableDataChanged("insert", id);
        qDebug() << "VarTblModel: 插入新变量，ID=" << id << "标签=" << label;
    } else {
        // 更新现有变量
        {
            QMutexLocker locker(&m_dataMutex);
            m_varTbl.insertOrAssign(id, {label, var});
            m_modifyTimes[id] = QDateTime::currentDateTime();
        }

        if (updateRow >= 0) {
            QModelIndex topLeft = index(updateRow, 0);
            QModelIndex bottomRight = index(updateRow, ColumnCount - 1);
            emit QAbstractTableModel::dataChanged(topLeft, bottomRight);
        }

        // 在无锁状态下发出信号
        emit variableDataChanged("update", id);
        qDebug() << "VarTblModel: 更新变量，ID=" << id << "标签=" << label;
    }

    return isNewVariable;
}

bool VarTblModel::removeVariable(int id)
{
    QMutexLocker locker(&m_dataMutex);
    
    if (!m_varTbl.contains(id)) {
        return false;
    }
    
    int row = getRowByVariableId(id);
    if (row < 0) {
        return false;
    }
    
    locker.unlock();
    beginRemoveRows(QModelIndex(), row, row);

    locker.relock();
    m_varTbl.erase(id);
    m_modifyTimes.remove(id);
    updateDisplayOrder();
    locker.unlock();

    endRemoveRows();
    
    emit variableDataChanged("remove", id);
    qDebug() << "VarTblModel: 删除变量，ID=" << id;
    
    return true;
}

bool VarTblModel::getVariable(int id, cmd::Var &var, QString &label) const
{
    QMutexLocker locker(&m_dataMutex);
    
    if (!m_varTbl.contains(id)) {
        return false;
    }
    
    const auto &item = m_varTbl.at(id);
    var = item.obj;
    label = item.label;
    
    return true;
}

bool VarTblModel::containsVariable(int id) const
{
    QMutexLocker locker(&m_dataMutex);
    return m_varTbl.contains(id);
}

QList<int> VarTblModel::getAllVariableIds() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_varTbl.ids();
}

void VarTblModel::clear()
{
    beginResetModel();
    
    {
        QMutexLocker locker(&m_dataMutex);
        m_varTbl = cmd::VarTbl();
        m_displayOrder.clear();
        m_modifyTimes.clear();
    }
    
    endResetModel();
    emit variableDataChanged("clear", -1);
    
    qDebug() << "VarTblModel: 清空所有数据";
}

int VarTblModel::getVariableCount() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_varTbl.size();
}

// ========== 索引转换接口 ==========

int VarTblModel::getVariableIdByRow(int row) const
{
    QMutexLocker locker(&m_dataMutex);
    
    if (row < 0 || row >= m_displayOrder.size()) {
        return -1;
    }
    
    return m_displayOrder.at(row);
}

int VarTblModel::getRowByVariableId(int id) const
{
    // 注意：此方法假设已经持有互斥锁
    //QMutexLocker locker(&m_dataMutex);
    
    for (int i = 0; i < m_displayOrder.size(); ++i) {
        if (m_displayOrder.at(i) == id) {
            return i;
        }
    }
    
    return -1;
}

// ========== 内部辅助方法 ==========

void VarTblModel::updateDisplayOrder()
{
    // 注意：此方法假设已经持有互斥锁
    m_displayOrder.clear();
    m_displayOrder.reserve(m_varTbl.size());
    
    // 获取所有ID并排序
    QList<int> ids = m_varTbl.ids();
    std::sort(ids.begin(), ids.end());
    
    m_displayOrder = ids;
}

QString VarTblModel::formatDisplayValue(const cmd::Var &var) const
{
    if (var.isString()) {
        QString str = var.toString();
        if (str.length() > 50) {
            return str.left(47) + "...";
        }
        return str;
    } else if (var.isList()) {
        QStringList list = var.toList();
        if (list.isEmpty()) {
            return tr("[]");
        } else if (list.size() == 1) {
            return QString("[%1]").arg(list.first());
        } else {
            return QString("[%1, ...] (%2项)").arg(list.first()).arg(list.size());
        }
    }
    
    return tr("未知");
}

QString VarTblModel::getVariableTypeString(const cmd::Var &var) const
{
    switch (var.type()) {
    case cmd::Var::Type::String:
        return tr("字符串");
    case cmd::Var::Type::List:
        return tr("列表");
    default:
        return tr("未知");
    }
}

QVariant VarTblModel::getDisplayData(int id, int column) const
{
    // 注意：此方法假设已经持有互斥锁且已验证ID存在
    const auto &item = m_varTbl.at(id);
    
    switch (column) {
    case ColumnId:
        return id;
    case ColumnLabel:
        return item.label;
    case ColumnType:
        return getVariableTypeString(item.obj);
    case ColumnValue:
        return formatDisplayValue(item.obj);
    case ColumnLastModified:
        return m_modifyTimes.value(id, QDateTime()).toString("yyyy-MM-dd hh:mm:ss");
    default:
        return QVariant();
    }
}

QVariant VarTblModel::getEditData(int id, int column) const
{
    // 注意：此方法假设已经持有互斥锁且已验证ID存在
    const auto &item = m_varTbl.at(id);
    
    switch (column) {
    case ColumnId:
        return id;
    case ColumnLabel:
        return item.label;
    case ColumnType:
        return static_cast<int>(item.obj.type());
    case ColumnValue:
        if (item.obj.isString()) {
            return item.obj.toString();
        } else if (item.obj.isList()) {
            return item.obj.toList();
        }
        return QVariant();
    case ColumnLastModified:
        return m_modifyTimes.value(id, QDateTime());
    default:
        return QVariant();
    }
}

bool VarTblModel::setEditData(int id, int column, const QVariant &value)
{
    // 注意：此方法假设已经持有互斥锁且已验证ID存在
    auto &item = m_varTbl.at(id);
    
    switch (column) {
    case ColumnLabel:
        item.label = value.toString();
        return true;
        
    case ColumnType: {
        cmd::Var::Type newType = static_cast<cmd::Var::Type>(value.toInt());
        if (newType != item.obj.type()) {
            // 类型变化，需要重新创建Var对象
            if (newType == cmd::Var::Type::String) {
                item.obj = cmd::Var(QString());
            } else if (newType == cmd::Var::Type::List) {
                item.obj = cmd::Var(QStringList());
            }
        }
        return true;
    }
    
    case ColumnValue:
        if (item.obj.isString()) {
            item.obj.setData(value.toString());
            return true;
        } else if (item.obj.isList()) {
            if (value.type() == QVariant::StringList) {
                item.obj.setData(value.toStringList());
                return true;
            } else if (value.type() == QVariant::String) {
                // 尝试解析字符串为列表
                QString str = value.toString().trimmed();
                if (str.startsWith('[') && str.endsWith(']')) {
                    str = str.mid(1, str.length() - 2);
                    QStringList list = str.split(',', Qt::SkipEmptyParts);
                    for (QString &item : list) {
                        item = item.trimmed();
                        if (item.startsWith('"') && item.endsWith('"')) {
                            item = item.mid(1, item.length() - 2);
                        }
                    }
                    item.obj.setData(list);
                    return true;
                }
            }
        }
        return false;
        
    default:
        return false;
    }
}

// ========== 表格编辑模式控制 ==========

void VarTblModel::setTableEditMode(bool enabled)
{
    if (m_tableEditMode != enabled) {
        m_tableEditMode = enabled;

        // 通知视图更新所有项目的flags
        emit dataChanged(index(0, 0), index(rowCount() - 1, columnCount() - 1));

        qDebug() << "VarTblModel: 设置表格编辑模式为" << enabled;
    }
}
