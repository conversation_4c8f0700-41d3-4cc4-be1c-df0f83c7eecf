#ifndef TEST_SERVER_H
#define TEST_SERVER_H

#include <QObject>
#include <QTcpServer>
#include <QTimer>
#include <QJsonObject>
#include <QJsonDocument>
#include <QUuid>
#include <QHash>
#include <QDateTime>
#include <QCommandLineParser>
#include "ClientConnection.h"
#include "RWSProtocolHandler.h"

/**
 * @brief CommandTransceiver 测试服务器
 * 
 * 提供网络通信测试功能，支持多种协议和客户端连接
 */
class TestServer : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 服务器配置结构
     */
    struct ServerConfig {
        quint16 port = 8080;                                    // 监听端口
        RWSProtocolHandler::ProtocolType protocolType = RWSProtocolHandler::PreLength; // 默认协议
        bool enableHeartbeat = false;                           // 启用心跳检测
        int heartbeatInterval = 30000;                          // 心跳间隔（毫秒）
        int maxConnections = 100;                               // 最大连接数
        bool enableLogging = true;                              // 启用日志
        bool enableEcho = true;                                 // 启用命令回显
        bool enableAutoResponse = true;                         // 启用自动响应
        
        ServerConfig() = default;
    };

    explicit TestServer(QObject *parent = nullptr);
    ~TestServer();

    /**
     * @brief 启动服务器
     * @param config 服务器配置
     * @return 是否启动成功
     */
    bool startServer(const ServerConfig &config);

    /**
     * @brief 停止服务器
     */
    void stopServer();

    /**
     * @brief 是否正在运行
     * @return 运行状态
     */
    bool isRunning() const;

    /**
     * @brief 获取服务器配置
     * @return 当前配置
     */
    ServerConfig getConfig() const { return m_config; }

    /**
     * @brief 获取连接数量
     * @return 当前连接数
     */
    int getConnectionCount() const;

    /**
     * @brief 获取服务器统计信息
     * @return 统计信息JSON对象
     */
    QJsonObject getServerStatistics() const;

    /**
     * @brief 获取所有客户端连接统计信息
     * @return 连接统计信息列表
     */
    QJsonArray getConnectionsStatistics() const;

    /**
     * @brief 向指定客户端发送消息
     * @param connectionId 连接ID
     * @param message JSON消息
     * @return 是否发送成功
     */
    bool sendMessageToClient(const QUuid &connectionId, const QJsonObject &message);

    /**
     * @brief 向所有客户端广播消息
     * @param message JSON消息
     * @return 成功发送的客户端数量
     */
    int broadcastMessage(const QJsonObject &message);

    /**
     * @brief 断开指定客户端连接
     * @param connectionId 连接ID
     * @return 是否成功断开
     */
    bool disconnectClient(const QUuid &connectionId);

    /**
     * @brief 设置协议类型（影响新连接）
     * @param type 协议类型
     */
    void setProtocolType(RWSProtocolHandler::ProtocolType type);

    /**
     * @brief 解析命令行参数并创建配置
     * @param parser 命令行解析器
     * @return 服务器配置
     */
    static ServerConfig parseCommandLine(const QCommandLineParser &parser);

    /**
     * @brief 设置命令行选项
     * @param parser 命令行解析器
     */
    static void setupCommandLineOptions(QCommandLineParser &parser);

public slots:
    /**
     * @brief 打印服务器状态
     */
    void printServerStatus();

    /**
     * @brief 打印连接列表
     */
    void printConnectionList();
    /**
     * @brief 定期打印统计信息
     */
    void printStatistics();

signals:
    /**
     * @brief 服务器启动信号
     * @param port 监听端口
     */
    void serverStarted(quint16 port);

    /**
     * @brief 服务器停止信号
     */
    void serverStopped();

    /**
     * @brief 新客户端连接信号
     * @param connectionId 连接ID
     * @param clientAddress 客户端地址
     */
    void clientConnected(const QUuid &connectionId, const QString &clientAddress);

    /**
     * @brief 客户端断开连接信号
     * @param connectionId 连接ID
     */
    void clientDisconnected(const QUuid &connectionId);

    /**
     * @brief 接收到JSON命令信号
     * @param connectionId 连接ID
     * @param command JSON命令
     */
    void jsonCommandReceived(const QUuid &connectionId, const QJsonObject &command);

    /**
     * @brief 接收到原始数据信号
     * @param connectionId 连接ID
     * @param data 原始数据
     */
    void rawDataReceived(const QUuid &connectionId, const QByteArray &data);

private slots:
    /**
     * @brief 处理新的客户端连接
     */
    void onNewConnection();

    /**
     * @brief 处理客户端连接状态变化
     * @param connectionId 连接ID
     * @param state 新状态
     */
    void onClientConnectionStateChanged(const QUuid &connectionId, ClientConnection::ConnectionState state);

    /**
     * @brief 处理客户端断开连接
     * @param connectionId 连接ID
     */
    void onClientDisconnected(const QUuid &connectionId);

    /**
     * @brief 处理客户端JSON命令
     * @param connectionId 连接ID
     * @param command JSON命令
     */
    void onClientJsonCommand(const QUuid &connectionId, const QJsonObject &command);

    /**
     * @brief 处理客户端原始数据
     * @param connectionId 连接ID
     * @param data 原始数据
     */
    void onClientRawData(const QUuid &connectionId, const QByteArray &data);

    /**
     * @brief 处理客户端连接错误
     * @param connectionId 连接ID
     * @param error 错误信息
     */
    void onClientConnectionError(const QUuid &connectionId, const QString &error);



private:
    /**
     * @brief 创建自动响应
     * @param command 接收到的命令
     * @return 响应JSON对象
     */
    QJsonObject createAutoResponse(const QJsonObject &command);

    /**
     * @brief 处理特殊命令
     * @param connectionId 连接ID
     * @param command 命令对象
     * @return 是否为特殊命令
     */
    bool handleSpecialCommand(const QUuid &connectionId, const QJsonObject &command);

    /**
     * @brief 记录日志
     * @param message 日志消息
     */
    void logMessage(const QString &message);

private:
    QTcpServer *m_tcpServer;                                    // TCP服务器
    ServerConfig m_config;                                      // 服务器配置
    QHash<QUuid, ClientConnection*> m_connections;              // 客户端连接映射
    QTimer *m_statisticsTimer;                                  // 统计信息定时器
    
    // 统计信息
    QDateTime m_startTime;                                      // 服务器启动时间
    qint64 m_totalConnections;                                  // 总连接数
    qint64 m_totalMessagesReceived;                             // 总接收消息数
    qint64 m_totalMessagesSent;                                 // 总发送消息数
    qint64 m_totalBytesReceived;                                // 总接收字节数
    qint64 m_totalBytesSent;                                    // 总发送字节数
};

#endif // TEST_SERVER_H
