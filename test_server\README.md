# CommandTransceiver 测试服务器

## 概述

CommandTransceiver 测试服务器是一个专为 CommandTransceiver 客户端设计的极简测试服务器程序。它支持多种通信协议，提供完整的网络通信测试功能，帮助验证 CommandTransceiver 框架的网络通信能力。

## 主要特性

### 🌐 网络通信
- 基于 Qt 网络模块（QTcpServer/QTcpSocket）
- 支持多客户端并发连接
- 可配置的最大连接数限制
- 完善的连接状态管理

### 📡 协议支持
- **PreLength**: 4字节长度头 + 数据内容（默认协议）
- **EndWithNewLine**: 换行符分隔的文本协议
- **Simple**: 简单直传协议

### 🗜️ 数据压缩支持
- 完全兼容 CommandTransceiver 框架的压缩格式
- 自动检测和解压缩 qCompress 格式的数据
- 支持 JSON → toBinaryData → qCompress 数据流程
- 压缩统计信息（压缩率、数据量等）
- 透明处理，不影响非压缩数据

### 💓 心跳检测
- 可选的心跳检测机制
- 可配置的心跳间隔
- 自动超时断开

### 🔄 自动响应
- JSON 命令自动响应
- 命令回显功能
- 特殊命令处理（状态查询、协议切换等）

### 📊 监控和日志
- 实时连接统计
- 数据传输统计
- 详细的日志输出
- 控制台交互命令

## 编译和安装

### 前提条件
- Qt 5.12 或更高版本（推荐 Qt 5.15+）
- C++17 兼容的编译器
- qmake 构建工具

### 版本兼容性
- **Qt 5.12-5.14**: 支持，但需要注意信号名称差异
- **Qt 5.15+**: 完全支持，推荐版本
- **Qt 6.x**: 支持，自动处理 API 差异

### 编译步骤

1. **进入测试服务器目录**
   ```bash
   cd test_server
   ```

2. **生成 Makefile**
   ```bash
   qmake test_server.pro
   ```

3. **编译程序**
   ```bash
   make        # Linux/macOS
   nmake       # Windows (Visual Studio)
   mingw32-make # Windows (MinGW)
   ```

4. **运行程序**
   ```bash
   # Debug 版本
   ./bin/debug/CommandTransceiverTestServer_d
   
   # Release 版本
   ./bin/release/CommandTransceiverTestServer
   ```

## 使用方法

### 基本启动

```bash
# 使用默认配置启动（端口8080，PreLength协议）
CommandTransceiverTestServer

# 指定端口
CommandTransceiverTestServer -p 9090

# 指定协议类型
CommandTransceiverTestServer -t endwithnewline
```

### 命令行选项

| 选项 | 简写 | 描述 | 默认值 |
|------|------|------|--------|
| `--port` | `-p` | 服务器监听端口 | 8080 |
| `--protocol` | `-t` | 通信协议类型 | prelength |
| `--heartbeat` | - | 启用心跳检测 | 禁用 |
| `--heartbeat-interval` | - | 心跳间隔（毫秒） | 30000 |
| `--max-connections` | `-m` | 最大连接数 | 100 |
| `--no-logging` | - | 禁用日志输出 | 启用 |
| `--no-echo` | - | 禁用命令回显 | 启用 |
| `--no-auto-response` | - | 禁用自动响应 | 启用 |

### 协议类型

- `prelength`: 预长度协议（4字节长度头 + 数据）
- `endwithnewline`: 换行符结束协议
- `simple`: 简单协议（直接传输）

### 使用示例

```bash
# 基本启动
CommandTransceiverTestServer

# 自定义端口和协议
CommandTransceiverTestServer -p 9090 -t endwithnewline

# 启用心跳检测
CommandTransceiverTestServer --heartbeat --heartbeat-interval 15000

# 限制连接数
CommandTransceiverTestServer -m 50

# 静默模式（无日志，无回显）
CommandTransceiverTestServer --no-logging --no-echo
```

## 控制台命令

服务器运行时，可以在控制台输入以下命令：

| 命令 | 简写 | 描述 |
|------|------|------|
| `help` | `h` | 显示帮助信息 |
| `status` | `s` | 显示服务器状态 |
| `connections` | `c` | 显示连接列表 |
| `stats` | - | 显示统计信息 |
| `broadcast <消息>` | `b` | 向所有客户端广播消息 |
| `protocol <类型>` | `p` | 设置默认协议类型 |
| `quit` | `q` | 退出服务器 |

### 控制台命令示例

```bash
# 查看服务器状态
status

# 查看连接列表
connections

# 广播消息
broadcast Hello, all clients!

# 切换协议
protocol endwithnewline

# 退出服务器
quit
```

## JSON 命令支持

服务器支持以下特殊 JSON 命令：

### 1. 服务器状态查询
```json
{
    "type": "server_status"
}
```

### 2. 连接列表查询
```json
{
    "type": "connection_list"
}
```

### 3. 协议切换
```json
{
    "type": "set_protocol",
    "protocol": "endwithnewline"
}
```

### 4. 广播消息
```json
{
    "type": "broadcast",
    "message": {
        "content": "Hello, everyone!"
    }
}
```

### 5. 心跳检测
```json
{
    "type": "heartbeat",
    "id": "unique-id"
}
```

### 6. 压缩统计查询
```json
{
    "type": "compression_stats"
}
```

### 7. 通用测试命令
```json
{
    "type": "test",
    "data": "test data",
    "id": "command-123"
}
```

## 测试验证

### 1. 基本连接测试

1. 启动测试服务器：
   ```bash
   CommandTransceiverTestServer -p 8080
   ```

2. 启动 CommandTransceiver 客户端
3. 连接到 `localhost:8080`
4. 观察服务器日志输出

### 2. 协议兼容性测试

测试不同协议的兼容性：

```bash
# PreLength 协议测试
CommandTransceiverTestServer -t prelength

# EndWithNewLine 协议测试  
CommandTransceiverTestServer -t endwithnewline

# Simple 协议测试
CommandTransceiverTestServer -t simple
```

### 3. 心跳检测测试

```bash
# 启用心跳检测（15秒间隔）
CommandTransceiverTestServer --heartbeat --heartbeat-interval 15000
```

### 4. 压缩功能测试

使用 Python 测试客户端测试压缩功能：

```bash
# 运行包含压缩测试的完整测试套件
python test_client.py 8080

# 测试客户端会自动测试：
# - 压缩数据传输
# - 压缩统计查询
# - 压缩率计算
```

### 5. 多客户端测试

```bash
# 限制最大连接数为5，测试连接管理
CommandTransceiverTestServer -m 5
```

## 故障排除

### 常见问题

1. **编译错误: 'errorOccurred' is not a member**
   ```
   原因: Qt 版本过低（< 5.15）
   解决: 项目已包含兼容性处理，确保使用 Qt 5.12+
   ```

2. **编译错误: call of overloaded 'arg(QByteArray)' is ambiguous**
   ```
   原因: QByteArray 到 QString 的隐式转换问题
   解决: 项目已修复，使用显式转换
   ```

3. **端口被占用**
   ```
   错误: 无法启动服务器，端口: 8080
   解决: 使用 -p 选项指定其他端口
   ```

4. **连接被拒绝**
   ```
   原因: 达到最大连接数限制
   解决: 使用 -m 选项增加最大连接数
   ```

5. **协议不匹配**
   ```
   现象: 客户端连接后无法正常通信
   解决: 确保客户端和服务器使用相同的协议类型
   ```

### 调试技巧

1. **启用详细日志**
   ```bash
   # 默认已启用日志，如需禁用使用 --no-logging
   CommandTransceiverTestServer
   ```

2. **监控连接状态**
   ```bash
   # 在控制台输入
   connections
   ```

3. **查看统计信息**
   ```bash
   # 在控制台输入
   stats
   ```

## 项目结构

```
test_server/
├── test_server.pro          # qmake 项目文件
├── main.cpp                 # 主程序入口
├── TestServer.h             # 服务器头文件
├── TestServer.cpp           # 服务器实现
├── ClientConnection.h       # 客户端连接管理头文件
├── ClientConnection.cpp     # 客户端连接管理实现
├── RWSProtocolHandler.h     # 协议处理器头文件
├── RWSProtocolHandler.cpp   # 协议处理器实现
├── README.md               # 本文档
├── bin/                    # 可执行文件输出目录
│   ├── debug/             # Debug 版本
│   └── release/           # Release 版本
└── build/                 # 构建临时文件目录
    ├── debug/
    └── release/
```

## 许可证

本项目遵循与 CommandTransceiver 主项目相同的许可证。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个测试服务器。

---

**注意**: 这是一个测试工具，不建议在生产环境中使用。
