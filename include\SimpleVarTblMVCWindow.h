#ifndef SIMPLEVARTBLMVCWINDOW_H
#define SIMPLEVARTBLMVCWINDOW_H

#include <QMainWindow>

// 前向声明
class QVBoxLayout;
class QHBoxLayout;
class QLabel;
class QPushButton;

// VarTbl MVC架构组件
#ifdef COMMANDTRANSCEIVER_MVC_SUPPORT
class VarTblManager;
#endif

/**
 * @brief 简单的VarTbl MVC验证窗口
 * 
 * 这个窗口类用于验证VarTbl MVC架构的基本功能：
 * - 显示VarTbl管理界面
 * - 提供添加示例数据的功能
 * - 验证MVC架构的集成
 */
class SimpleVarTblMVCWindow : public QMainWindow
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit SimpleVarTblMVCWindow(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~SimpleVarTblMVCWindow();

private slots:
    /**
     * @brief 添加示例数据按钮点击处理
     */
    void onAddDemoDataClicked();

private:
    /**
     * @brief 设置用户界面
     */
    void setupUI();

    /**
     * @brief 设置VarTbl MVC架构
     */
    void setupVarTblMVC();

private:
    // UI组件
    QPushButton *m_addDataButton;

#ifdef COMMANDTRANSCEIVER_MVC_SUPPORT
    // VarTbl MVC管理器
    VarTblManager *m_varTblManager;
#endif
};

#endif // SIMPLEVARTBLMVCWINDOW_H
