#ifndef VAR_TBL_MODEL_H
#define VAR_TBL_MODEL_H

#include <QAbstractTableModel>
#include <QMutex>
#include <QDateTime>
#include <QJsonObject>
#include <QJsonArray>
#include <QVariant>
#include <QList>

// 引入ada3的数据结构
#include "cmd/vartbl.h"
#include "cmd/var.h"
#include "cmd/varindex.h"

/**
 * @brief VarTbl表格数据模型 - 纯Model层实现
 * 
 * 设计原则：
 * - 直接使用ada3的VarTbl作为内部数据存储
 * - 实现标准的QAbstractTableModel接口
 * - 线程安全的数据访问
 * - 职责单一：只负责数据存储和展示
 */
class VarTblModel : public QAbstractTableModel
{
    Q_OBJECT

public:
    /**
     * @brief 列定义
     */
    enum Column {
        ColumnId = 0,           // 变量ID
        ColumnLabel,            // 变量标签/名称
        ColumnType,             // 变量类型（String/List）
        ColumnValue,            // 变量值
        ColumnLastModified,     // 最后修改时间
        ColumnCount
    };

    explicit VarTblModel(QObject *parent = nullptr);
    ~VarTblModel() override;

    // ========== QAbstractTableModel接口实现 ==========
    
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;
    Qt::ItemFlags flags(const QModelIndex &index) const override;
    
    // ========== 数据操作接口 ==========
    
    /**
     * @brief 设置VarTbl数据（完全替换）
     * @param varTbl 新的VarTbl数据
     */
    void setVarTbl(const cmd::VarTbl &varTbl);
    
    /**
     * @brief 获取VarTbl数据的副本
     * @return VarTbl数据副本
     */
    cmd::VarTbl getVarTbl() const;
    
    /**
     * @brief 插入或更新变量
     * @param id 变量ID
     * @param var 变量对象
     * @param label 变量标签
     * @return 是否为新插入（true）还是更新（false）
     */
    bool insertOrUpdateVariable(int id, const cmd::Var &var, const QString &label);
    
    /**
     * @brief 删除变量
     * @param id 变量ID
     * @return 是否删除成功
     */
    bool removeVariable(int id);
    
    /**
     * @brief 获取指定ID的变量
     * @param id 变量ID
     * @param var 输出变量对象
     * @param label 输出变量标签
     * @return 是否找到变量
     */
    bool getVariable(int id, cmd::Var &var, QString &label) const;
    
    /**
     * @brief 检查是否包含指定ID的变量
     * @param id 变量ID
     * @return 是否包含
     */
    bool containsVariable(int id) const;
    
    /**
     * @brief 获取所有变量ID列表
     * @return ID列表
     */
    QList<int> getAllVariableIds() const;
    
    /**
     * @brief 清空所有数据
     */
    void clear();
    
    /**
     * @brief 获取变量总数
     * @return 变量数量
     */
    int getVariableCount() const;
    
    // ========== 索引转换接口 ==========
    
    /**
     * @brief 根据行号获取变量ID
     * @param row 行号
     * @return 变量ID，-1表示无效
     */
    int getVariableIdByRow(int row) const;
    
    /**
     * @brief 根据变量ID获取行号
     * @param id 变量ID
     * @return 行号，-1表示未找到
     */
    int getRowByVariableId(int id) const;

    /**
     * @brief 设置表格编辑模式
     * @param enabled 是否启用表格编辑模式
     */
    void setTableEditMode(bool enabled);

signals:
    /**
     * @brief 数据变化信号
     * @param operation 操作类型（insert/update/remove/clear）
     * @param id 变量ID（clear操作时为-1）
     */
    void variableDataChanged(const QString &operation, int id);

private:
    // ========== 数据存储 ==========
    
    mutable QMutex m_dataMutex;         // 数据访问互斥锁
    cmd::VarTbl m_varTbl;               // ada3 VarTbl数据结构
    QList<int> m_displayOrder;          // 显示顺序（行号到ID的映射）
    QHash<int, QDateTime> m_modifyTimes; // 修改时间记录
    bool m_tableEditMode;               // 表格编辑模式状态
    
    // ========== 内部辅助方法 ==========
    
    /**
     * @brief 更新显示顺序
     */
    void updateDisplayOrder();
    
    /**
     * @brief 格式化显示值
     * @param var 变量对象
     * @return 格式化后的显示字符串
     */
    QString formatDisplayValue(const cmd::Var &var) const;
    
    /**
     * @brief 获取变量类型字符串
     * @param var 变量对象
     * @return 类型字符串
     */
    QString getVariableTypeString(const cmd::Var &var) const;

    // ========== 表格编辑模式控制 ==========
    /**
     * @brief 获取表格编辑模式状态
     * @return 是否启用表格编辑模式
     */
    bool isTableEditMode() const { return m_tableEditMode; }
    
    /**
     * @brief 获取显示数据
     * @param id 变量ID
     * @param column 列号
     * @return 显示数据
     */
    QVariant getDisplayData(int id, int column) const;
    
    /**
     * @brief 获取编辑数据
     * @param id 变量ID
     * @param column 列号
     * @return 编辑数据
     */
    QVariant getEditData(int id, int column) const;
    
    /**
     * @brief 设置编辑数据
     * @param id 变量ID
     * @param column 列号
     * @param value 新值
     * @return 是否设置成功
     */
    bool setEditData(int id, int column, const QVariant &value);
};

#endif // VAR_TBL_MODEL_H
