#ifndef CLIENT_CONNECTION_H
#define CLIENT_CONNECTION_H

#include <QObject>
#include <QTcpSocket>
#include <QTimer>
#include <QJsonObject>
#include <QJsonDocument>
#include <QUuid>
#include <QDateTime>
#include "RWSProtocolHandler.h"

/**
 * @brief 客户端连接管理类
 * 
 * 管理单个客户端连接，处理数据收发和协议解析
 */
class ClientConnection : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 连接状态枚举
     */
    enum ConnectionState {
        Disconnected,       // 已断开
        Connected,          // 已连接
        Authenticating,     // 认证中（预留）
        Ready              // 就绪状态
    };

    explicit ClientConnection(QTcpSocket *socket, 
                            RWSProtocolHandler::ProtocolType protocolType = RWSProtocolHandler::PreLength,
                            QObject *parent = nullptr);
    ~ClientConnection();

    /**
     * @brief 获取连接ID
     * @return 连接唯一标识符
     */
    QUuid getConnectionId() const { return m_connectionId; }

    /**
     * @brief 获取客户端地址信息
     * @return 地址字符串
     */
    QString getClientAddress() const;

    /**
     * @brief 获取连接状态
     * @return 当前连接状态
     */
    ConnectionState getConnectionState() const { return m_connectionState; }

    /**
     * @brief 获取连接时长（秒）
     * @return 连接持续时间
     */
    qint64 getConnectionDuration() const;

    /**
     * @brief 发送JSON响应
     * @param response JSON响应对象
     * @return 是否发送成功
     */
    bool sendJsonResponse(const QJsonObject &response);

    /**
     * @brief 发送原始数据
     * @param data 原始数据
     * @return 是否发送成功
     */
    bool sendRawData(const QByteArray &data);

    /**
     * @brief 设置协议类型
     * @param type 协议类型
     */
    void setProtocolType(RWSProtocolHandler::ProtocolType type);

    /**
     * @brief 获取协议类型
     * @return 当前协议类型
     */
    RWSProtocolHandler::ProtocolType getProtocolType() const;

    /**
     * @brief 启用/禁用心跳检测
     * @param enabled 是否启用
     * @param intervalMs 心跳间隔（毫秒）
     */
    void setHeartbeatEnabled(bool enabled, int intervalMs = 30000);

    /**
     * @brief 断开连接
     */
    void disconnectClient();

    /**
     * @brief 获取统计信息
     * @return 统计信息JSON对象
     */
    QJsonObject getStatistics() const;

    /**
     * @brief 获取压缩统计信息
     * @return 压缩统计信息JSON对象
     */
    QJsonObject getCompressionStats() const;

signals:
    /**
     * @brief 连接状态变化信号
     * @param connectionId 连接ID
     * @param state 新状态
     */
    void connectionStateChanged(const QUuid &connectionId, ConnectionState state);

    /**
     * @brief 接收到JSON命令信号
     * @param connectionId 连接ID
     * @param command JSON命令对象
     */
    void jsonCommandReceived(const QUuid &connectionId, const QJsonObject &command);

    /**
     * @brief 接收到原始数据信号
     * @param connectionId 连接ID
     * @param data 原始数据
     */
    void rawDataReceived(const QUuid &connectionId, const QByteArray &data);

    /**
     * @brief 连接错误信号
     * @param connectionId 连接ID
     * @param error 错误描述
     */
    void connectionError(const QUuid &connectionId, const QString &error);

    /**
     * @brief 连接断开信号
     * @param connectionId 连接ID
     */
    void connectionDisconnected(const QUuid &connectionId);

private slots:
    /**
     * @brief 处理socket数据就绪
     */
    void onSocketReadyRead();

    /**
     * @brief 处理socket断开连接
     */
    void onSocketDisconnected();

    /**
     * @brief 处理socket错误
     * @param error socket错误类型
     */
    void onSocketError(QAbstractSocket::SocketError error);

    /**
     * @brief 处理心跳超时
     */
    void onHeartbeatTimeout();

    /**
     * @brief 发送心跳包
     */
    void sendHeartbeat();

private:
    /**
     * @brief 设置连接状态
     * @param state 新状态
     */
    void setConnectionState(ConnectionState state);

    /**
     * @brief 处理接收到的消息
     * @param message 完整消息
     */
    void processMessage(const QByteArray &message);

    /**
     * @brief 尝试解析JSON命令
     * @param data 数据
     * @return 是否成功解析为JSON
     */
    bool tryParseJsonCommand(const QByteArray &data);

    /**
     * @brief 创建心跳响应
     * @param heartbeatCommand 心跳命令
     * @return 心跳响应JSON
     */
    QJsonObject createHeartbeatResponse(const QJsonObject &heartbeatCommand);

    /**
     * @brief 创建错误响应
     * @param error 错误信息
     * @param commandUuid 命令UUID（可选）
     * @return 错误响应JSON
     */
    QJsonObject createErrorResponse(const QString &error, const QUuid &commandUuid = QUuid());

private:
    QUuid m_connectionId;                   // 连接唯一标识符
    QTcpSocket *m_socket;                   // TCP socket
    RWSProtocolHandler *m_protocolHandler;  // 协议处理器
    ConnectionState m_connectionState;      // 连接状态
    QDateTime m_connectTime;                // 连接时间
    
    // 心跳检测
    QTimer *m_heartbeatTimer;               // 心跳定时器
    QTimer *m_heartbeatTimeoutTimer;        // 心跳超时定时器
    bool m_heartbeatEnabled;                // 是否启用心跳
    int m_heartbeatInterval;                // 心跳间隔
    QDateTime m_lastHeartbeatTime;          // 最后心跳时间
    
    // 统计信息
    qint64 m_bytesReceived;                 // 接收字节数
    qint64 m_bytesSent;                     // 发送字节数
    qint64 m_messagesReceived;              // 接收消息数
    qint64 m_messagesSent;                  // 发送消息数
    qint64 m_jsonCommandsReceived;          // 接收JSON命令数
    qint64 m_errorsCount;                   // 错误计数
};

#endif // CLIENT_CONNECTION_H
