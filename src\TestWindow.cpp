#include "TestWindow.h"

#include <QDebug>
#include <QTimer>
#include <QMessageBox>
#include <QApplication>

#include "mvc/VarTblView.h"
#include "mvc/VarTblModel.h"
#include "mvc/VarTblController.h"
#include "CommandTransceiverRefactored.h"

#ifdef COMMANDTRANSCEIVER_ADA3_SUPPORT
#include "cmd/VarTbl.h"
#include "cmd/Var.h"
#endif

TestWindow::TestWindow(QWidget *parent)
    : QMainWindow(parent)
{
    qDebug() << "=== TestWindow: 初始化开始 ===";
    
    setupUI();
    setupMVC();
    connectSignals();
    
    // 延迟添加测试数据，确保MVC组件完全初始化
    QTimer::singleShot(100, this, &TestWindow::addTestData);
    
    qDebug() << "=== TestWindow: 初始化完成 ===";
}

void TestWindow::setupUI()
{
    // 创建中央窗口部件
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    
    // 创建测试按钮布局
    m_buttonLayout = new QHBoxLayout();
    
    m_addBtn = new QPushButton("测试添加", this);
    m_editBtn = new QPushButton("测试编辑", this);
    m_deleteBtn = new QPushButton("测试删除", this);
    m_refreshBtn = new QPushButton("测试刷新", this);
    m_syncBtn = new QPushButton("测试同步", this);
    m_connectBtn = new QPushButton("测试连接", this);
    m_disconnectBtn = new QPushButton("测试断开", this);

    // 表格编辑测试按钮
    m_tableEditBtn = new QPushButton("切换表格编辑", this);
    m_addListDataBtn = new QPushButton("添加列表数据", this);
    m_testTableEditingBtn = new QPushButton("测试表格编辑", this);
    
    m_buttonLayout->addWidget(m_addBtn);
    m_buttonLayout->addWidget(m_editBtn);
    m_buttonLayout->addWidget(m_deleteBtn);
    m_buttonLayout->addWidget(m_refreshBtn);
    m_buttonLayout->addWidget(m_syncBtn);
    m_buttonLayout->addWidget(m_connectBtn);
    m_buttonLayout->addWidget(m_disconnectBtn);

    // 添加分隔符
    m_buttonLayout->addWidget(new QLabel("|", this));

    // 添加表格编辑测试按钮
    m_buttonLayout->addWidget(m_tableEditBtn);
    m_buttonLayout->addWidget(m_addListDataBtn);
    m_buttonLayout->addWidget(m_testTableEditingBtn);
    m_buttonLayout->addStretch();
    
    // 连接按钮信号
    connect(m_addBtn, &QPushButton::clicked, this, &TestWindow::onTestAddVariable);
    connect(m_editBtn, &QPushButton::clicked, this, &TestWindow::onTestEditVariable);
    connect(m_deleteBtn, &QPushButton::clicked, this, &TestWindow::onTestDeleteVariable);
    connect(m_refreshBtn, &QPushButton::clicked, this, &TestWindow::onTestRefreshData);
    connect(m_syncBtn, &QPushButton::clicked, this, &TestWindow::onTestSyncData);
    connect(m_connectBtn, &QPushButton::clicked, this, &TestWindow::onTestConnect);
    connect(m_disconnectBtn, &QPushButton::clicked, this, &TestWindow::onTestDisconnect);

    // 连接表格编辑测试按钮信号
    connect(m_tableEditBtn, &QPushButton::clicked, this, &TestWindow::onTestTableEditMode);
    connect(m_addListDataBtn, &QPushButton::clicked, this, &TestWindow::onTestAddListData);
    connect(m_testTableEditingBtn, &QPushButton::clicked, this, &TestWindow::onTestTableEditing);
    
    m_mainLayout->addLayout(m_buttonLayout);
    
    // 创建状态显示布局
    m_statusLayout = new QHBoxLayout();
    
    m_statusLabel = new QLabel("就绪", this);
    m_connectionLabel = new QLabel("连接状态: 未连接", this);
    m_syncLabel = new QLabel("同步状态: 空闲", this);
    m_busyLabel = new QLabel("", this);
    
    m_statusLayout->addWidget(m_statusLabel);
    m_statusLayout->addStretch();
    m_statusLayout->addWidget(m_syncLabel);
    m_statusLayout->addWidget(m_connectionLabel);
    m_statusLayout->addWidget(m_busyLabel);
    
    m_mainLayout->addLayout(m_statusLayout);
    
    // 设置窗口属性
    setWindowTitle("MVC架构重构测试程序");
    resize(1000, 700);
    
    qDebug() << "TestWindow: UI设置完成";
}

void TestWindow::setupMVC()
{
    // 创建MVC组件
    m_model = new VarTblModel(this);
    m_controller = new VarTblController(this);
    m_controller->setWaitForServerResponse(false);
    m_view = new VarTblView(this);

    // 创建通信组件
    m_transceiver = new CommandTransceiverRefactored(this);

    // 设置组件关系
    m_controller->setModel(m_model);
    m_controller->setCommandTransceiver(m_transceiver);  // 设置通信组件
    m_view->setModel(m_model);        // Qt自动处理数据显示
    m_view->setController(m_controller); // 自动连接信号槽

    // 将View添加到主窗口
    m_mainLayout->insertWidget(1, m_view); // 插入到按钮和状态之间

    qDebug() << "TestWindow: MVC组件设置完成";
}

void TestWindow::connectSignals()
{
    // 连接Controller的状态信号到测试窗口
//    connect(m_controller, &VarTblController::operationCompleted,
//            this, &TestWindow::onOperationCompleted);
    connect(m_controller, &VarTblController::connectionStatusChanged,
            this, &TestWindow::onConnectionStatusChanged);
//    connect(m_controller, &VarTblController::syncStatusChanged,
//            this, &TestWindow::onSyncStatusChanged);
    connect(m_controller, &VarTblController::busyStatusChanged,
            this, &TestWindow::onBusyStatusChanged);
    
    qDebug() << "TestWindow: 信号连接完成";
}

void TestWindow::addTestData()
{
#ifdef COMMANDTRANSCEIVER_ADA3_SUPPORT
    // 添加一些测试数据来验证Qt标准机制
    cmd::VarTbl testData;
    
    testData.insertOrAssign(1, {"测试字符串", cmd::Var("Hello World")});
    testData.insertOrAssign(2, {"测试整数", cmd::Var(QString::number(42))});
    testData.insertOrAssign(3, {"测试浮点数", cmd::Var(QString::number(3.14159))});
    testData.insertOrAssign(4, {"测试布尔值", cmd::Var(QString::number(true))});
    testData.insertOrAssign(5, {"空字符串", cmd::Var("")});

    // 添加列表类型测试数据
    QStringList testList1 = {"项目1", "项目2", "项目3"};
    testData.insertOrAssign(6, {"测试列表1", cmd::Var(testList1)});

    QStringList testList2 = {"Apple", "Banana", "Cherry", "Date"};
    testData.insertOrAssign(7, {"水果列表", cmd::Var(testList2)});

    QStringList emptyList;
    testData.insertOrAssign(8, {"空列表", cmd::Var(emptyList)});
    
    // 通过Controller更新数据，验证Model->View的自动更新
    m_controller->updateVariables(testData, false);
    
    updateStatus(QString("测试数据添加完成，共 %1 个变量").arg(testData.size()));
    qDebug() << "TestWindow: 测试数据添加完成，共" << testData.size() << "个变量";
#else
    updateStatus("警告: 未启用ada3支持，无法添加测试数据", true);
    qDebug() << "TestWindow: 警告 - 未启用ada3支持";
#endif
}

// ========== 测试方法实现 ==========

void TestWindow::onTestAddVariable()
{
    qDebug() << "=== 测试添加变量 ===";
    updateStatus("测试添加变量...");

    // 模拟用户点击添加按钮
    if (m_view) {
        emit m_view->addVariableRequested();
    } else {
        updateStatus("错误: View组件未初始化", true);
    }
}

void TestWindow::onTestEditVariable()
{
    qDebug() << "=== 测试编辑变量 ===";
    updateStatus("测试编辑变量...");

    // 获取当前选中的变量进行编辑
    if (m_view && m_model) {
        QList<int> selectedIds = m_view->getSelectedVariableIds();
        if (selectedIds.size() == 1) {
            // 编辑选中的变量
            emit m_view->editVariableRequested(selectedIds.first());
        } else if (selectedIds.isEmpty()) {
            updateStatus("错误: 请先选择一个变量进行编辑", true);
        } else {
            updateStatus("错误: 请只选择一个变量进行编辑", true);
        }
    } else {
        updateStatus("错误: View或Model组件未初始化", true);
    }
}

void TestWindow::onTestDeleteVariable()
{
    qDebug() << "=== 测试删除变量 ===";
    updateStatus("测试删除变量...");

    // 获取当前选中的变量进行删除
    if (m_view && m_model) {
        QList<int> selectedIds = m_view->getSelectedVariableIds();
        if (!selectedIds.isEmpty()) {
            // 显示确认对话框
            QString message;
            if (selectedIds.size() == 1) {
                message = QString("确定要删除变量 ID=%1 吗？").arg(selectedIds.first());
            } else {
                message = QString("确定要删除选中的 %1 个变量吗？").arg(selectedIds.size());
            }

            int ret = QMessageBox::question(this, "确认删除", message,
                                          QMessageBox::Yes | QMessageBox::No);

            if (ret == QMessageBox::Yes) {
                emit m_view->deleteVariableRequested(selectedIds);
            } else {
                updateStatus("删除操作已取消");
            }
        } else {
            updateStatus("错误: 请先选择要删除的变量", true);
        }
    } else {
        updateStatus("错误: View或Model组件未初始化", true);
    }
}

void TestWindow::onTestRefreshData()
{
    qDebug() << "=== 测试刷新数据 ===";
    updateStatus("测试刷新数据...");

    if (m_view) {
        emit m_view->refreshDataRequested();
    } else {
        updateStatus("错误: View组件未初始化", true);
    }
}

void TestWindow::onTestSyncData()
{
    qDebug() << "=== 测试同步数据 ===";
    updateStatus("测试同步数据...");

    if (m_view) {
        emit m_view->syncDataRequested();
    } else {
        updateStatus("错误: View组件未初始化", true);
    }
}

void TestWindow::onTestConnect()
{
    qDebug() << "=== 测试连接服务器 ===";
    updateStatus("测试连接服务器...");

    if (m_view) {
        emit m_view->connectToServerRequested();
    } else {
        updateStatus("错误: View组件未初始化", true);
    }
}

void TestWindow::onTestDisconnect()
{
    qDebug() << "=== 测试断开连接 ===";
    updateStatus("测试断开连接...");

    if (m_view) {
        emit m_view->disconnectFromServerRequested();
    } else {
        updateStatus("错误: View组件未初始化", true);
    }
}

// ========== 状态响应方法实现 ==========

void TestWindow::onOperationCompleted(bool success, const QString &message)
{
    QString status = success ? "成功" : "失败";
    updateStatus(QString("操作%1: %2").arg(status, message), !success);
    qDebug() << "TestWindow: 操作完成 -" << status << ":" << message;
}

void TestWindow::onConnectionStatusChanged(bool connected)
{
    QString status = connected ? "已连接" : "未连接";
    m_connectionLabel->setText(QString("连接状态: %1").arg(status));

    // 更新按钮状态
    m_connectBtn->setEnabled(!connected);
    m_disconnectBtn->setEnabled(connected);
    m_refreshBtn->setEnabled(connected);
    m_syncBtn->setEnabled(connected);

    updateStatus(QString("连接状态变化: %1").arg(status));
    qDebug() << "TestWindow: 连接状态变化 -" << status;
}

void TestWindow::onSyncStatusChanged(bool syncing)
{
    QString status = syncing ? "同步中" : "空闲";
    m_syncLabel->setText(QString("同步状态: %1").arg(status));

    // 同步时禁用相关按钮
    m_syncBtn->setEnabled(!syncing);
    m_refreshBtn->setEnabled(!syncing);

    if (syncing) {
        updateStatus("正在同步数据...");
    }

    qDebug() << "TestWindow: 同步状态变化 -" << status;
}

void TestWindow::onBusyStatusChanged(bool busy)
{
    QString status = busy ? "忙碌" : "";
    m_busyLabel->setText(status);

    // 忙碌时禁用所有操作按钮
    m_addBtn->setEnabled(!busy);
    m_editBtn->setEnabled(!busy);
    m_deleteBtn->setEnabled(!busy);

    if (busy) {
        updateStatus("系统忙碌中...");
        setCursor(Qt::WaitCursor);
    } else {
        unsetCursor();
    }

    qDebug() << "TestWindow: 忙碌状态变化 -" << (busy ? "忙碌" : "空闲");
}

// ========== 辅助方法实现 ==========

void TestWindow::updateStatus(const QString &message, bool isError)
{
    if (m_statusLabel) {
        m_statusLabel->setText(message);

        // 设置颜色
        if (isError) {
            m_statusLabel->setStyleSheet("color: red; font-weight: bold;");
        } else {
            m_statusLabel->setStyleSheet("color: green;");
        }

        // 自动清除状态消息
        QTimer::singleShot(5000, [this]() {
            if (m_statusLabel) {
                m_statusLabel->setText("就绪");
                m_statusLabel->setStyleSheet("");
            }
        });
    }
}

// ========== 表格编辑测试方法实现 ==========

void TestWindow::onTestTableEditMode()
{
    qDebug() << "=== 测试表格编辑模式切换 ===";
    updateStatus("切换表格编辑模式...");

    if (m_view) {
        // 切换表格编辑模式
        m_view->toggleTableEditMode();
        bool isEnabled = m_view->isTableEditMode();
        updateStatus(QString("表格编辑模式已%1").arg(isEnabled ? "启用" : "禁用"));

        // 更新按钮文本
        m_tableEditBtn->setText(isEnabled ? "关闭表格编辑" : "启用表格编辑");
    } else {
        updateStatus("错误: View组件未初始化", true);
    }
}

void TestWindow::onTestAddListData()
{
    qDebug() << "=== 测试添加列表数据 ===";
    updateStatus("添加列表类型测试数据...");

#ifdef COMMANDTRANSCEIVER_ADA3_SUPPORT
    if (m_controller) {
        // 添加更多列表类型的测试数据
        QStringList colorList = {"红色", "绿色", "蓝色", "黄色", "紫色"};
        cmd::Var colorVar(colorList);
        m_controller->addVariable(101, colorVar,"颜色列表", false);

        QStringList numberList = {"一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};
        cmd::Var numberVar(numberList);
        m_controller->addVariable(102, numberVar,"数字列表",false);

        QStringList singleItemList = {"单个项目"};
        cmd::Var singleVar(singleItemList);
        m_controller->addVariable(103, singleVar,"单项列表" ,false);

        updateStatus("列表类型测试数据添加完成");
        qDebug() << "TestWindow: 列表类型测试数据添加完成";
    } else {
        updateStatus("错误: Controller组件未初始化", true);
    }
#else
    updateStatus("警告: 未启用ada3支持，无法添加列表数据", true);
#endif
}

void TestWindow::onTestTableEditing()
{
    qDebug() << "=== 测试表格编辑功能 ===";
    updateStatus("开始表格编辑功能测试...");

    if (!m_view) {
        updateStatus("错误: View组件未初始化", true);
        return;
    }

    // 显示测试说明对话框
    QString instructions =
        "表格编辑功能测试说明：\n\n"
        "🎯 新的编辑模式特点：\n"
        "• 移除了之前的编辑面板，完全使用表格内编辑\n"
        "• 只有在表格编辑模式下才能进行新增、编辑、删除操作\n"
        "• 工具栏的添加、编辑、删除按钮只在编辑模式下可用\n\n"
        "📋 测试步骤：\n"
        "1. 点击工具栏的'表格编辑'按钮启用编辑模式\n"
        "2. 编辑模式启用后：\n"
        "   • 表格背景变为浅蓝色\n"
        "   • 添加、编辑、删除按钮变为可用状态\n"
        "   • 双击单元格可直接编辑\n\n"
        "3. 编辑操作：\n"
        "   • 添加：点击'添加'按钮在表格中新增一行\n"
        "   • 编辑：选中行后点击'编辑'按钮或双击单元格\n"
        "   • 删除：选中行后点击'删除'按钮\n\n"
        "4. 单元格编辑器：\n"
        "   • ID列：数字输入框（0-999999）\n"
        "   • 标签列：文本输入框\n"
        "   • 类型列：下拉选择框（字符串/列表）\n"
        "   • 值列：根据类型显示不同编辑器\n"
        "     - 字符串类型：文本输入框\n"
        "     - 列表类型：点击按钮弹出列表编辑对话框\n\n"
        "5. 测试要点：\n"
        "   • 验证非编辑模式下按钮不可用\n"
        "   • 测试编辑模式切换的视觉反馈\n"
        "   • 尝试各种数据类型的编辑\n"
        "   • 测试数据验证和错误处理\n\n"
        "6. 完成测试后，再次点击'表格编辑'按钮关闭编辑模式";

    QMessageBox::information(this, "表格编辑功能测试", instructions);

    updateStatus("请按照说明进行表格编辑功能测试");
}
