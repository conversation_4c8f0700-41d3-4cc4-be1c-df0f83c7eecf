#include "mvc/VarTblItemDelegate.h"
#include <QDebug>
#include <QSpinBox>
#include <QLineEdit>
#include <QComboBox>
#include <QPushButton>
#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QDialogButtonBox>
#include <QMessageBox>
#include <QApplication>
#include <QLabel>

VarTblItemDelegate::VarTblItemDelegate(QObject *parent)
    : QStyledItemDelegate(parent)
    , m_editingEnabled(false)
{
    qDebug() << "VarTblItemDelegate: 创建表格编辑器代理";
}

// ========== QStyledItemDelegate接口实现 ==========

QWidget* VarTblItemDelegate::createEditor(QWidget* parent, 
                                         const QStyleOptionViewItem& option, 
                                         const QModelIndex& index) const
{
    Q_UNUSED(option)
    
    if (!m_editingEnabled || !index.isValid()) {
        return nullptr;
    }
    
    int column = index.column();
    
    switch (column) {
    case VarTblModel::ColumnId:
        return createIdEditor(parent);
    case VarTblModel::ColumnLabel:
        return createLabelEditor(parent);
    case VarTblModel::ColumnType:
        return createTypeEditor(parent);
    case VarTblModel::ColumnValue:
        return createValueEditor(parent, index);
    default:
        return nullptr;
    }
}

void VarTblItemDelegate::setEditorData(QWidget* editor, const QModelIndex& index) const
{
    if (!editor || !index.isValid()) {
        return;
    }
    
    QVariant value = index.model()->data(index, Qt::EditRole);
    int column = index.column();
    
    switch (column) {
    case VarTblModel::ColumnId: {
        QSpinBox* spinBox = qobject_cast<QSpinBox*>(editor);
        if (spinBox) {
            spinBox->setValue(value.toInt());
        }
        break;
    }
    case VarTblModel::ColumnLabel: {
        QLineEdit* lineEdit = qobject_cast<QLineEdit*>(editor);
        if (lineEdit) {
            lineEdit->setText(value.toString());
        }
        break;
    }
    case VarTblModel::ColumnType: {
        QComboBox* comboBox = qobject_cast<QComboBox*>(editor);
        if (comboBox) {
            QString typeText = value.toString();
            int index = comboBox->findText(typeText);
            if (index >= 0) {
                comboBox->setCurrentIndex(index);
            }
        }
        break;
    }
    case VarTblModel::ColumnValue: {
        // 值列的数据设置在创建编辑器时已处理
        break;
    }
    }
    
    qDebug() << "VarTblItemDelegate: 设置编辑器数据，列=" << column << "值=" << value;
}

void VarTblItemDelegate::setModelData(QWidget* editor, 
                                     QAbstractItemModel* model, 
                                     const QModelIndex& index) const
{
    if (!editor || !model || !index.isValid()) {
        return;
    }
    
    int column = index.column();
    QVariant newValue;
    
    switch (column) {
    case VarTblModel::ColumnId: {
        QSpinBox* spinBox = qobject_cast<QSpinBox*>(editor);
        if (spinBox) {
            newValue = spinBox->value();
        }
        break;
    }
    case VarTblModel::ColumnLabel: {
        QLineEdit* lineEdit = qobject_cast<QLineEdit*>(editor);
        if (lineEdit) {
            newValue = lineEdit->text().trimmed();
        }
        break;
    }
    case VarTblModel::ColumnType: {
        QComboBox* comboBox = qobject_cast<QComboBox*>(editor);
        if (comboBox) {
            newValue = comboBox->currentText();
        }
        break;
    }
    case VarTblModel::ColumnValue: {
        QLineEdit* lineEdit = qobject_cast<QLineEdit*>(editor);
        if (lineEdit) {
            newValue = lineEdit->text();
        }
        // 列表类型的值通过信号处理，这里不需要处理
        break;
    }
    }
    
    // 验证输入数据
    if (!validateInput(column, newValue)) {
        emit const_cast<VarTblItemDelegate*>(this)->editingError(
            tr("输入数据无效，请检查后重试"));
        return;
    }

    // 发出数据变化信号，由View转发给Controller处理
    emit const_cast<VarTblItemDelegate*>(this)->cellDataChanged(index, newValue);

    qDebug() << "VarTblItemDelegate: 发出单元格数据变化信号，列=" << column << "值=" << newValue;
}

void VarTblItemDelegate::updateEditorGeometry(QWidget* editor,
                                             const QStyleOptionViewItem& option,
                                             const QModelIndex& index) const
{
    Q_UNUSED(index)
    
    if (editor) {
        editor->setGeometry(option.rect);
    }
}

// ========== 配置接口 ==========

void VarTblItemDelegate::setEditingEnabled(bool enabled)
{
    m_editingEnabled = enabled;
    qDebug() << "VarTblItemDelegate: 设置编辑状态为" << enabled;
}

// ========== 私有槽函数 ==========

void VarTblItemDelegate::onListEditButtonClicked()
{
    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if (!button || !m_currentIndex.isValid()) {
        return;
    }
    
    // 获取当前列表数据
    QVariant currentValue = m_currentIndex.model()->data(m_currentIndex, Qt::EditRole);
    QStringList currentList;

    // 正确处理列表数据：优先尝试直接获取QStringList
    if (currentValue.isValid()) {
        if (currentValue.canConvert<QStringList>()) {
            // 直接获取QStringList，这是正确的方式
            currentList = currentValue.toStringList();
            qDebug() << "VarTblItemDelegate: 直接获取列表数据" << currentList;
        } else {
            // 如果不是QStringList，尝试字符串分割（向后兼容）
            QString valueStr = currentValue.toString();
            if (!valueStr.isEmpty()) {
                currentList = valueStr.split(",", Qt::SkipEmptyParts);
                for (QString& item : currentList) {
                    item = item.trimmed();
                }
                qDebug() << "VarTblItemDelegate: 通过字符串分割获取列表数据" << currentList;
            }
        }
    }
    
    // 创建列表编辑对话框
    QDialog dialog(button->parentWidget());
    dialog.setWindowTitle(tr("编辑列表"));
    dialog.setModal(true);
    dialog.resize(400, 300);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    VarListEditor* listEditor = new VarListEditor(&dialog);
    listEditor->setStringList(currentList);

    // 添加提示标签
    QLabel* hintLabel = new QLabel(tr("提示：数据只有在点击确定后才会保存"), &dialog);
    hintLabel->setStyleSheet("color: #666; font-size: 11px;");
    layout->addWidget(hintLabel);

    layout->addWidget(listEditor);

    QDialogButtonBox* buttonBox = new QDialogButtonBox(
        QDialogButtonBox::Ok | QDialogButtonBox::Cancel, &dialog);
    layout->addWidget(buttonBox);
    
    connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);
    
    // 显示对话框
    qDebug() << "VarTblItemDelegate: 显示列表编辑对话框，初始数据=" << currentList;

    int result = dialog.exec();
    QStringList finalList = listEditor->getStringList();

    qDebug() << "VarTblItemDelegate: 对话框结果=" << (result == QDialog::Accepted ? "确定" : "取消")
             << "最终数据=" << finalList;

    if (result == QDialog::Accepted) {
        emit listEditingFinished(m_currentIndex, finalList);
        qDebug() << "VarTblItemDelegate: 列表编辑完成，保存新数据=" << finalList;
    } else {
        qDebug() << "VarTblItemDelegate: 用户取消编辑，不保存数据";
    }
}

// ========== 编辑器创建方法 ==========

QWidget* VarTblItemDelegate::createIdEditor(QWidget* parent) const
{
    QSpinBox* spinBox = new QSpinBox(parent);
    spinBox->setRange(0, 999999);
    spinBox->setSingleStep(1);
    return spinBox;
}

QWidget* VarTblItemDelegate::createLabelEditor(QWidget* parent) const
{
    QLineEdit* lineEdit = new QLineEdit(parent);
    lineEdit->setPlaceholderText(tr("输入变量标签"));
    return lineEdit;
}

QWidget* VarTblItemDelegate::createTypeEditor(QWidget* parent) const
{
    QComboBox* comboBox = new QComboBox(parent);
    comboBox->addItem(tr("字符串"));
    comboBox->addItem(tr("列表"));
    return comboBox;
}

QWidget* VarTblItemDelegate::createValueEditor(QWidget* parent, const QModelIndex& index) const
{
    cmd::Var::Type varType = getVariableType(index);
    
    if (varType == cmd::Var::Type::List) {
        // 列表类型：创建按钮，点击弹出编辑对话框
        QPushButton* button = new QPushButton(parent);
        
        // 获取当前列表项数量并显示
        QVariant editValue = index.model()->data(index, Qt::EditRole);
        int itemCount = 0;

        if (editValue.isValid() && editValue.canConvert<QStringList>()) {
            QStringList list = editValue.toStringList();
            itemCount = list.size();
        } else {
            // 备用方案：使用显示文本计算
            QVariant displayValue = index.model()->data(index, Qt::DisplayRole);
            QString displayText = displayValue.toString();
            if (!displayText.isEmpty()) {
                itemCount = displayText.split(",", Qt::SkipEmptyParts).size();
            }
        }

        button->setText(tr("编辑列表 (%1 项)").arg(itemCount));
        
        // 保存当前索引，用于列表编辑
        m_currentIndex = index;
        
        // 连接按钮点击信号
        connect(button, &QPushButton::clicked, 
                const_cast<VarTblItemDelegate*>(this), 
                &VarTblItemDelegate::onListEditButtonClicked);
        
        return button;
    } else {
        // 字符串类型：使用普通文本编辑器
        QLineEdit* lineEdit = new QLineEdit(parent);
        lineEdit->setPlaceholderText(tr("输入字符串值"));
        
        // 设置当前值
        QVariant currentValue = index.model()->data(index, Qt::EditRole);
        lineEdit->setText(currentValue.toString());
        
        return lineEdit;
    }
}

// ========== 数据处理方法 ==========

cmd::Var::Type VarTblItemDelegate::getVariableType(const QModelIndex& index) const
{
    if (!index.isValid()) {
        return cmd::Var::Type::Unknown;
    }
    
    // 从类型列获取类型信息
    QModelIndex typeIndex = index.sibling(index.row(), VarTblModel::ColumnType);
    QString typeText = typeIndex.data(Qt::DisplayRole).toString();
    
    if (typeText == tr("列表") || typeText == "List") {
        return cmd::Var::Type::List;
    } else if (typeText == tr("字符串") || typeText == "String") {
        return cmd::Var::Type::String;
    }
    
    return cmd::Var::Type::Unknown;
}

bool VarTblItemDelegate::validateInput(int column, const QVariant& value) const
{
    switch (column) {
    case VarTblModel::ColumnId:
        return value.toInt() >= 0;
    case VarTblModel::ColumnLabel:
        return !value.toString().trimmed().isEmpty();
    case VarTblModel::ColumnType:
        return value.toString() == tr("字符串") || value.toString() == tr("列表");
    case VarTblModel::ColumnValue:
        return true; // 值列的验证在具体类型中处理
    default:
        return false;
    }
}

QString VarTblItemDelegate::formatDisplayText(const cmd::Var& var) const
{
    if (var.isString()) {
        return var.toString();
    } else if (var.isList()) {
        QStringList list = var.toList();
        return list.join(", ");
    }
    return QString();
}
