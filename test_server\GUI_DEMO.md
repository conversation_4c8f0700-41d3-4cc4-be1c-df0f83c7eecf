# CommandTransceiver GUI 服务器演示

## 🎯 功能概览

GUI版本的CommandTransceiver测试服务器提供了完整的图形化界面，让您可以：

### ✅ 已实现的核心功能

1. **🖥️ 图形化服务器控制**
   - 启动/停止服务器
   - 配置端口和协议类型
   - 实时显示服务器状态

2. **👥 客户端连接管理**
   - 实时显示所有连接的客户端
   - 查看连接详情（ID、地址、协议、持续时间）
   - 支持断开指定客户端连接

3. **📨 消息收发功能**
   - 彩色显示接收到的消息
   - 向指定客户端发送JSON消息
   - 广播消息到所有客户端
   - 支持JSON格式验证

4. **📊 实时统计信息**
   - 服务器运行时间
   - 连接数统计
   - 消息收发统计
   - 流量统计

5. **🔧 JSON格式兼容性修复**
   - ✅ 支持Qt二进制JSON格式（CommandTransceiver客户端使用）
   - ✅ 支持标准文本JSON格式
   - ✅ 自动检测和解析压缩数据
   - ✅ 向后兼容性保证

## 🚀 使用演示

### 1. 启动GUI服务器

```bash
# 编译项目
.\build_simple.bat

# 运行GUI版本
.\build\debug\CommandTransceiverTestServerGUI.exe

# 或运行控制台版本
.\build\debug\CommandTransceiverTestServerGUI.exe -c
```

### 2. 配置和启动服务器

1. **设置端口**：在"端口"输入框中输入监听端口（默认8080）
2. **选择协议**：从下拉菜单选择协议类型
   - `PreLength`：4字节长度头 + 数据（推荐用于CommandTransceiver客户端）
   - `EndWithNewLine`：换行符分隔
   - `Simple`：原始数据传输
3. **点击"启动服务器"**：服务器状态会变为绿色"运行中"

### 3. 连接客户端

使用CommandTransceiver客户端连接到服务器：

```cpp
// 客户端代码示例
CommandTransceiver transceiver;
transceiver.setServerAddress("127.0.0.1", 8080);
transceiver.setProtocolType(RWSProtocolHandler::PreLength);
transceiver.connectToServer();

// 发送JSON消息
QJsonObject message;
message["type"] = "test";
message["content"] = "Hello GUI Server!";
transceiver.sendJsonCommand(message);
```

### 4. 监控连接状态

在"客户端连接"区域可以看到：
- 连接ID（UUID的前8位）
- 客户端IP地址和端口
- 使用的协议类型
- 连接持续时间

### 5. 查看消息日志

"消息日志"区域会显示：
- 🔵 **连接事件**（蓝色）：新客户端连接
- 🟠 **断开事件**（橙色）：客户端断开
- ⚪ **JSON消息**：接收到的JSON命令
- ⚪ **原始数据**：非JSON格式的数据
- 🟣 **发送消息**（紫色）：服务器发送的消息
- 🟢 **成功事件**（绿色）：操作成功
- 🔴 **错误事件**（红色）：错误信息

### 6. 发送消息到客户端

在"发送消息"区域：

1. **选择目标**：
   - "广播到所有连接"：发送给所有客户端
   - 特定客户端：选择具体的连接

2. **输入JSON消息**：
```json
{
  "type": "server_message",
  "content": "Hello from GUI server!",
  "timestamp": 1234567890
}
```

3. **发送方式**：
   - "发送到选中连接"：发送给选中的客户端
   - "广播到所有连接"：发送给所有客户端

### 7. 查看统计信息

"统计信息"区域实时显示：
- **运行时间**：服务器运行的时长（时:分:秒）
- **总连接数**：历史总连接数
- **接收消息**：接收到的消息总数
- **发送消息**：发送的消息总数
- **接收字节**：接收的数据总量
- **发送字节**：发送的数据总量

## 🔧 JSON格式兼容性

### 问题背景
之前的服务器只能解析标准JSON文本格式，但CommandTransceiver客户端发送的是Qt二进制JSON格式（经过压缩），导致解析失败。

### 解决方案
GUI服务器现在支持两种格式：

1. **Qt二进制JSON**（CommandTransceiver客户端格式）：
```cpp
// 客户端发送代码
QJsonDocument doc(json);
QByteArray binaryData = doc.toBinaryData();
QByteArray compressedData = qCompress(binaryData);
// 发送 compressedData
```

2. **标准文本JSON**：
```json
{"type": "test", "message": "hello"}
```

### 服务器解析逻辑
```cpp
// 先尝试解析为Qt二进制JSON格式
doc = QJsonDocument::fromBinaryData(data);
if (!doc.isNull() && doc.isObject()) {
    // 二进制JSON解析成功
} else {
    // 尝试文本JSON格式
    doc = QJsonDocument::fromJson(data, &parseError);
}
```

## 🎨 界面特性

### 颜色编码
- 🔴 **红色**：错误和失败
- 🟢 **绿色**：成功和正常状态
- 🔵 **蓝色**：连接事件
- 🟠 **橙色**：断开事件
- 🟣 **紫色**：发送的消息
- ⚪ **默认**：一般信息

### 快捷键
- **F5**：启动服务器
- **F6**：停止服务器
- **Ctrl+S**：保存日志
- **Ctrl+L**：清空日志
- **F5**：刷新连接列表
- **Ctrl+Q**：退出程序

### 菜单功能
- **文件菜单**：保存日志、退出
- **服务器菜单**：启动/停止服务器
- **工具菜单**：清空日志、刷新连接
- **帮助菜单**：关于信息

## 🧪 测试场景

### 场景1：基本连接测试
1. 启动GUI服务器（端口8080，PreLength协议）
2. 启动CommandTransceiver客户端连接
3. 观察连接列表中出现新连接
4. 查看消息日志中的连接事件

### 场景2：JSON消息收发
1. 客户端发送JSON命令
2. 服务器接收并显示在日志中
3. 服务器发送响应消息给客户端
4. 观察统计信息更新

### 场景3：广播消息
1. 连接多个客户端
2. 在GUI中输入广播消息
3. 点击"广播到所有连接"
4. 所有客户端都会收到消息

### 场景4：连接管理
1. 在连接列表中选择一个连接
2. 点击"断开选中连接"
3. 观察客户端断开，连接从列表中移除

## 🔍 故障排除

### 常见问题

1. **编译失败**
   - 确保Qt环境正确配置
   - 检查编译器是否可用
   - 尝试使用不同的构建脚本

2. **服务器启动失败**
   - 检查端口是否被占用
   - 确认防火墙设置
   - 查看错误日志

3. **客户端连接失败**
   - 验证IP地址和端口
   - 检查协议类型匹配
   - 确认网络连通性

4. **JSON解析失败**
   - 现在应该不会出现此问题
   - 服务器支持多种JSON格式
   - 查看详细错误信息

## 📈 性能特性

- **实时更新**：统计信息每秒更新
- **日志限制**：最多保留10000行日志
- **内存优化**：自动清理过期连接
- **线程安全**：使用互斥锁保护共享数据

## 🎉 总结

GUI版本的CommandTransceiver测试服务器提供了：
- ✅ 完整的图形化界面
- ✅ 实时监控和管理功能
- ✅ JSON格式兼容性修复
- ✅ 丰富的统计和日志功能
- ✅ 用户友好的操作体验

这个GUI版本完全解决了之前JSON解析的问题，并提供了更好的用户体验！
